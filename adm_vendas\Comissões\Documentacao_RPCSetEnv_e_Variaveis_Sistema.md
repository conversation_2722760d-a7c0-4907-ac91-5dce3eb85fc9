# Documentação - Funções que utilizam RPCSetEnv e/ou alteram variáveis de sistema

## Descrição
Este documento lista todas as funções encontradas na pasta `adm_vendas\Comissões` e suas subpastas que:
- Utilizam a função **RPCSetEnv** ou **RpcSetEnv**
- Alteram o valor da variável **cEmpAnt**
- Alteram o valor da variável **__cUserId**

---

## 1. FUNÇÕES QUE UTILIZAM RPCSetEnv

### 1.1 Environment Definition\Functions\accsta13.prw
- **Linha 51**: `If !RpcSetEnv( cEmpPar, cFilPar )`
- **Linha 153**: `If !RPCSETENV( cEmpresa,cFilSM0 )`
- **Linha 435**: `If !RPCSETENV( cEmpresa,cFilSM0)`
- **Linha 524**: `RPCSetEnv( '00','00001000100' )`
- **Linha 956**: `If !RPCSETENV( cEmpresa,cFilSM0 )`
- **Linha 1095**: `RPCSetEnv( cEmpresa, cFilSM0 )`

### 1.2 Funcoes Genericas\MXMECM.prw
- **Linha 66**: `If !RpcSetEnv(CEMPX, CFILX)`

### 1.3 Funcoes Genericas\TINTJ018.PRW
- **Linha 28**: `RpcSetEnv("00","00001000100")`

### 1.4 Interfaces\Functions\ACCSTM09.PRW
- **Linha 126**: `If !RpcSetEnv(cEmpPar, cFilPar,,,,,{"SX6"})`
- **Linha 164**: `If !RpcSetEnv(cEmpPar,cFilPar,,,,,{"SX6"})`
- **Linha 253**: `If !RpcSetEnv(cEmpPar, cFilPar,,,,,{"SX6"})`

### 1.5 Interfaces\Functions\ACCSTM34.prw
- **Linha 251**: `If !RpcSetEnv( xEmp, xFil )`

### 1.6 Interfaces\Functions\ACCSTM37.prw
- **Linha 150**: `If RpcSetEnv( _cEmp, _cFil )`

### 1.7 Relatorios\TCMSR001.PRW
- **Linha 1015**: `RpcSetEnv("00", "00001000100")`

### 1.8 Rotinas Ajustes\CMSAJU01.prw
- **Linha 60**: `RpcSetEnv('00','01')`

### 1.9 Rotinas Ajustes\CMSAJU04.prw
- **Linha 460**: `RpcSetEnv( "00","00001000100")`

### 1.10 RV\TIRVA006.prw
- **Linha 134**: `RpcSetEnv(cEmpresa)`

### 1.11 RV\TIRVF001.prw
- **Linha 20**: `RpcSetEnv('00', '00001000100')`

### 1.12 RV\TIRVJ000.PRW
- **Linha 21**: `RpcSetEnv(aParam[2], aParam[3])`
- **Função**: `User Function TIRVJ000(aParam)` - Job responsável por buscar dados do PSA e calcular na P66

### 1.13 RV\TIRVJ001.prw
- **Linha 57**: `RpcSetEnv(aParam[1], aParam[2])`

### 1.14 RV\TIRVR002.prw
- **Linha 368**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.15 RV\TIRVR003.prw
- **Linha 194**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.16 ACCSTA58.prw
- **Linha 41**: `If RpcSetEnv( _cEmp, _cFil )`

### 1.17 ACCSTABX.PRW
- **Linha 119**: `If !RPCSETENV( cEmpresa , cFilSM0 , "PROTHEUS-AUTO" ,  "AP710" ,, aTabProvisao )`

### 1.18 ACRM900.PRW
- **Linha 1070**: `If !RpcSetEnv("00" , "00001000100")`

### 1.19 TCMSA003.PRW
- **Linha 62**: `RpcSetEnv("00","00001000100")`

---

## 2. FUNÇÕES QUE ALTERAM A VARIÁVEL cEmpAnt

### 2.1 Interfaces\Functions\ACCSTA02.PRW
- **Linha 335**: `cEmpAnt := cEmpBkp`

### 2.2 Interfaces\Functions\ACCSTA06.PRW
- **Linha 44**: `Private cEmpAnt := SUBSTR(ZXO->ZXO_EMPFAT,1,2)`

### 2.3 Interfaces\Functions\ACCSTA32.PRW
- **Linha 89**: `Private cEmpAnt := SUBSTR(ZXO->ZXO_EMPFAT,1,2)`

---

## 3. FUNÇÕES QUE ALTERAM A VARIÁVEL __cUserId

### 3.1 RV\TIRVF003.prw
- **Linha 45**: `__cUserID := "000000"`
- **Função**: Static Function SaveEle(aDados) - Callback de cadastro de elegíveis

### 3.2 RV\TIRVF004.prw
- **Linha 45**: `__cUserID := "000000"`
- **Função**: Static Function SaveArea(aDados) - Callback de cadastro de áreas

### 3.3 RV\TIRVF005.prw
- **Linha 45**: `__cUserID := "000000"`
- **Função**: Static Function SaveMArea(aDados) - Callback de cadastro de Macro Areas x Divisão x Cargos

### 3.4 RV\TIRVF011.prw
- **Linha 100**: `__cUserID := "000000"`

### 3.5 RV\TIRVF013.PRW
- **Linha 45**: `__cUserID := "000000"`

---

## 4. RESUMO ESTATÍSTICO

- **Total de arquivos com RPCSetEnv**: 19 arquivos
- **Total de ocorrências de RPCSetEnv**: 25 ocorrências
- **Total de arquivos que alteram cEmpAnt**: 3 arquivos
- **Total de arquivos que alteram __cUserId**: 5 arquivos

---

## 5. OBSERVAÇÕES IMPORTANTES

1. **Segurança**: As funções que alteram `__cUserId` para "000000" podem representar um risco de segurança, pois assumem um usuário genérico.

2. **Ambiente**: A maioria das chamadas `RpcSetEnv` utiliza empresa "00" e filial "00001000100", indicando um ambiente padrão.

3. **Jobs**: Várias funções são executadas como jobs (TIRVJ000, TIRVJ001), necessitando configurar o ambiente via RpcSetEnv.

4. **Callbacks**: Muitas funções são callbacks de integração com sistemas externos (RM, PSA, ACTIO).

---

**Data de criação**: 05/08/2025  
**Responsável**: Documentação automática via análise de código
