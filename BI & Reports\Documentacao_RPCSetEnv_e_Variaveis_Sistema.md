# Documentação - Funções que utilizam RPCSetEnv e/ou alteram variáveis de sistema

## Descrição
Este documento lista todas as funções encontradas na pasta `BI & Reports` e suas subpastas que:
- Utilizam a função **RPCSetEnv** ou **RpcSetEnv**
- Alteram o valor da variável **cEmpAnt**
- Alteram o valor da variável **__cUserId**

---

## 1. FUNÇÕES QUE UTILIZAM RPCSetEnv

### 1.1 TBIA001.PRW
- **Linha 41**: `//RpcSetEnv("00","02501002000")` *(comentado)*
- **Linha 226**: `If !RpcSetEnv(clEmp,clFil)`
- **Linha 336**: `If !RpcSetEnv(clEmp,clFil)`
- **Linha 489**: `If !RpcSetEnv(clEmp,clFil)`
- **Linha 681**: `RpcSetEnv(cEmpAnt , cFilAnt)`
- **Linha 940**: `If !RpcSetEnv(clEmp , clFil)`
- **Linha 969**: `RpcSetEnv(cEmpAnt , cFilAnt)`
- **Linha 1073**: `If !RpcSetEnv(cEmpresa,cFilEmp)`
- **Linha 1534**: `If !RpcSetEnv(cEmpresa,cFilEmp)`
- **Linha 2591**: `If !RpcSetEnv(cEmpresa,cFilEmp)`
- **Linha 3812**: `RpcSetEnv(clEmp , clFil)`
- **Linha 7490**: `If !RpcSetEnv(clEmp,clFil)`

### 1.2 TBIA002.PRW
- **Linha 238**: `If !RpcSetEnv(clEmp,clFil)`
- **Linha 384**: `If !RpcSetEnv(clEmp,clFil)`
- **Linha 602**: `RpcSetEnv(cEmpAnt , cFilAnt)`
- **Linha 778**: `RpcSetEnv(cEmpAnt , cFilAnt)`
- **Linha 863**: `RpcSetEnv(cEmpAnt , cFilAnt)`
- **Linha 917**: `If !RpcSetEnv(cEmpresa,cFilEmp)`
- **Linha 1083**: `If !RpcSetEnv(cEmpresa,cFilEmp)`

### 1.3 TBIA005.prw
- **Linha 190**: `If !RPCSETENV(cEMPENV, cCodFilEnv,,,, "CRM", {"ADK", "ACA", "SA1", "SA3", "PCF"},,, lAbreSxs)`
- **Linha 1000**: `If !RPCSETENV(cEMPENV, cCodFilEnv,,,, "CRM", {"ADK", "ACA", "SA1", "SA3", "PCF"},,, lAbreSxs)`
- **Linha 1991**: `If !RpcSetEnv(clEmp,clFil)`
- **Linha 2006**: `rpcSetEnv(cempAnt , cfilAnt)`
- **Linha 2014**: `rpcSetEnv(cempAnt , cfilAnt)`
- **Linha 2080**: `If !RpcSetEnv(clEmp,clFil)`
- **Linha 2107**: `rpcSetEnv(cempAnt , cfilAnt)`
- **Linha 2115**: `rpcSetEnv(cempAnt , cfilAnt)`

### 1.4 TBIA016.PRW
- **Linha 847**: `If !RpcSetEnv(cEmpFore , "***********")`
- **Linha 856**: `If !RpcSetEnv(cEmpFore , "***********")`
- **Linha 866**: `If !RpcSetEnv(cEmpFore , "***********")`
- **Linha 1433**: `//-- RpcSetEnv ( cRpcEmp, cRpcFil, cEnvUser, cEnvPass, cEnvMod, cFunName, aTables, lShowFinal, lAbend, lOpenSX, lConnect )` *(comentário)*
- **Linha 1434**: `If !RpcSetEnv(cEmpProc , cFilProc)`
- **Linha 1477**: `If !RpcSetEnv(cEmpFore , "***********")`
- **Linha 2031**: `//-- RpcSetEnv ( cRpcEmp, cRpcFil, cEnvUser, cEnvPass, cEnvMod, cFunName, aTables, lShowFinal, lAbend, lOpenSX, lConnect )` *(comentário)*
- **Linha 2032**: `If !RpcSetEnv(cEmpFore , "***********")`
- **Linha 2059**: `If !RpcSetEnv(cEmpFore , "***********")`

### 1.5 TBIA019.prw
- **Linha 41**: `If !RpcSetEnv(cEmpPro,cFilPro)`

### 1.6 TBIJ010.prw
- **Linha 26**: `If !RpcSetEnv("00","***********")//RpcSetEnv("00", "***********", "PROTHEUS-AUTO", "AP710",,)`
- **Linha 42**: `rpcSetEnv(cempAnt , cfilAnt)`
- **Linha 82**: `rpcSetEnv(cempAnt , cfilAnt)`

### 1.7 TBIJ011.prw
- **Linha 85**: `If !RpcSetEnv(cEmpProc,"***********") //,'ADMIN','ADMIN','SIGAFIN')`
- **Linha 230**: `If !RpcSetEnv(cEmpTra,"***********")`
- **Linha 288**: `If !RpcSetEnv(cEmp,cFil)`
- **Linha 788**: `If !RpcSetEnv(cEmp,cFil)`

### 1.8 TBIJ012.prw
- **Linha 42**: `//-- RpcSetEnv ( cRpcEmp, cRpcFil, cEnvUser, cEnvPass, cEnvMod, cFunName, aTables, lShowFinal, lAbend, lOpenSX, lConnect )` *(comentário)*
- **Linha 44**: `If !RpcSetEnv("00" , "***********")//,, "PROTHEUS-AUTO", "AP710"`
- **Linha 111**: `//-- RpcSetEnv ( cRpcEmp, cRpcFil, cEnvUser, cEnvPass, cEnvMod, cFunName, aTables, lShowFinal, lAbend, lOpenSX, lConnect )` *(comentário)*
- **Linha 113**: `If !RpcSetEnv("00" , "***********")//, "PROTHEUS-AUTO", "AP710"`

### 1.9 TBIJ013.prw
- **Linha 31**: `//-- RpcSetEnv ( cRpcEmp, cRpcFil, cEnvUser, cEnvPass, cEnvMod, cFunName, aTables, lShowFinal, lAbend, lOpenSX, lConnect )` *(comentário)*
- **Linha 34**: `If !RpcSetEnv("00" , "***********",,,, GetEnvServer(), aTabelas)`

---

## 2. FUNÇÕES QUE ALTERAM A VARIÁVEL cEmpAnt

**Nenhuma ocorrência encontrada** na pasta `BI & Reports`.

---

## 3. FUNÇÕES QUE ALTERAM A VARIÁVEL __cUserId

**Nenhuma ocorrência encontrada** na pasta `BI & Reports`.

---

## 4. RESUMO ESTATÍSTICO

- **Total de arquivos com RPCSetEnv**: 9 arquivos
- **Total de ocorrências de RPCSetEnv**: 41 ocorrências
- **Total de arquivos que alteram cEmpAnt**: 0 arquivos
- **Total de arquivos que alteram __cUserId**: 0 arquivos

---

## 5. ANÁLISE DOS PADRÕES ENCONTRADOS

### 5.1 Padrões de Empresa/Filial
- **Empresa "00" + Filial "***********"**: Padrão mais comum
- **Empresa "00" + Filial "02501002000"**: Encontrado comentado em TBIA001.PRW
- **Variáveis dinâmicas**: Uso de clEmp/clFil, cEmpAnt/cFilAnt, cEmpresa/cFilEmp

### 5.2 Tipos de Arquivos
- **TBIA***: 5 arquivos - Funções de BI e análise
- **TBIJ***: 4 arquivos - Jobs de BI

### 5.3 Padrões de Uso
- **Jobs de BI**: Padrão `If !RpcSetEnv(clEmp,clFil)` predominante
- **Ambiente Fixo**: Alguns arquivos usam ambiente fixo "00"/"***********"
- **Módulo CRM**: TBIA005.prw especifica módulo "CRM" com tabelas específicas
- **Comentários**: Vários comentários explicativos sobre parâmetros do RpcSetEnv

### 5.4 Características Específicas
- **Tabelas específicas**: TBIA005.prw e TBIJ013.prw especificam arrays de tabelas
- **Módulos**: Especificação de módulo "CRM" em algumas chamadas
- **Variações de sintaxe**: Uso tanto de `RpcSetEnv` quanto `rpcSetEnv` (case insensitive)

---

## 6. OBSERVAÇÕES IMPORTANTES

1. **Foco em BI**: Arquivos focados em Business Intelligence e relatórios
2. **Ambiente Padrão**: Predominância da empresa "00" e filial "***********"
3. **Segurança**: Não foram encontradas alterações diretas de __cUserId ou cEmpAnt
4. **Jobs**: Vários jobs de processamento de BI (TBIJ*)
5. **Documentação**: Presença de comentários explicativos sobre parâmetros
6. **Módulo CRM**: Integração específica com módulo CRM
7. **Tabelas**: Especificação de tabelas específicas em algumas chamadas

---

## 7. COMPARATIVO COM OUTROS MÓDULOS

| Módulo | Arquivos RPCSetEnv | Ocorrências | Altera cEmpAnt | Altera __cUserId |
|--------|-------------------|-------------|----------------|------------------|
| **BI & Reports** | 9 | 41 | 0 | 0 |
| **Integracões** | 47 | 69 | 0 | 0 |
| **Comissões** | 19 | 25 | 3 | 5 |
| **Contratos** | 69 | 95 | 4 | 11 |
| **Backoffice** | 142 | 350+ | 18 | 18 |

**Observação**: O módulo BI & Reports apresenta um padrão similar ao módulo Integracões, sendo "limpo" sem alterações diretas das variáveis de sistema, focando apenas na configuração de ambiente via RpcSetEnv.

---

## 8. ARQUIVOS POR CATEGORIA

### 8.1 Análise e BI (TBIA*)
- **TBIA001.PRW**: 12 ocorrências - Principal arquivo de BI
- **TBIA002.PRW**: 7 ocorrências - Funções de análise
- **TBIA005.prw**: 8 ocorrências - Integração CRM
- **TBIA016.PRW**: 9 ocorrências - Processamento de dados
- **TBIA019.prw**: 1 ocorrência - Função específica

### 8.2 Jobs de BI (TBIJ*)
- **TBIJ010.prw**: 3 ocorrências - Job básico
- **TBIJ011.prw**: 4 ocorrências - Job de processamento
- **TBIJ012.prw**: 4 ocorrências - Job de relatórios
- **TBIJ013.prw**: 1 ocorrência - Job com tabelas específicas

---

**Data de criação**: 05/08/2025  
**Responsável**: Documentação automática via análise de código
