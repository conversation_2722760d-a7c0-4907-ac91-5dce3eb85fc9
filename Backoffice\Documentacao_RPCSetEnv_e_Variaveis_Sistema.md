# Documentação - Funções que utilizam RPCSetEnv e/ou alteram variáveis de sistema

## Descrição
Este documento lista todas as funções encontradas na pasta `Backoffice` e suas subpastas que:
- Utilizam a função **RPCSetEnv** ou **RpcSetEnv**
- Alteram o valor da variável **cEmpAnt**
- Alteram o valor da variável **__cUserId**

---

## 1. FUNÇÕES QUE UTILIZAM RPCSetEnv

### 1.1 Cadastro\TCAD001.PRW
- **Linha 33**: `RpcSetEnv("00", "00001000100")`

### 1.2 Compras\TCOMA012.PRW
- **Linha 1278**: `RpcSetEnv( cEmpresa,; // c - Código da empresa.`

### 1.3 Compras\TCOMJ000.PRW
- **Linha 98**: `RpcSetEnv( '00', '00001000100')`
- **Linha 124**: `RpcSetEnv( '00', '00001000100')`
- **Linha 149**: `RpcSetEnv( '00', '00001000100')`

### 1.4 Compras\TCOMJ002.PRW
- **Linha 21**: `RpcSetEnv('00', '00001000100')`

### 1.5 Compras\TCOMJ012.PRW
- **Linha 25**: `RpcSetEnv('00', '00001000100')`

### 1.6 Compras\TCOMJ016.PRW
- **Linha 622**: `RpcSetEnv( SM0->M0_CODIGO, SM0->M0_CODFIL )`

### 1.7 Compras\TCOMJ022.PRW
- **Linha 281**: `RpcSetEnv( cEmpresa,; // c - Código da empresa.`

### 1.8 Compras\TCOMJ023.PRW
- **Linha 418**: `RpcSetEnv( SM0->M0_CODIGO, SM0->M0_CODFIL )`

### 1.9 Compras\TCOMJ026.PRW
- **Linha 8**: `RpcSetEnv('00', '00001000100')`

### 1.10 Configurador\GestaoUsuarios\Controller\ReceiveJsonToCreateUserController.tlpp
- **Linha 87**: `RPCSetEnv(cRequestGrupo,cRequestFilial)`
- **Linha 138**: `RPCSetEnv(cRequestGrupo,cRequestFilial)`
- **Linha 191**: `RPCSetEnv(cRequestGrupo,cRequestFilial)`
- **Linha 237**: `RPCSetEnv(cRequestGrupo,cRequestFilial)`
- **Linha 281**: `RPCSetEnv(cRequestGrupo,cRequestFilial)`
- **Linha 330**: `RPCSetEnv(cRequestGrupo,cRequestFilial)`

### 1.11 Configurador\GestaoUsuarios\Utils\ConstructEnvironmentUtils.tlpp
- **Linha 28**: `RPCSetEnv("00","00001000100")`

### 1.12 Contabilidade\Integracoes\TCTBWS03.PRW
- **Linha 17**: `RpcSetEnv( '00', '00001000100')`
- **Linha 43**: `RpcSetEnv('60', '60001000100')`

### 1.13 Contabilidade\TCTBA042.PRW
- **Linha 870**: `RpcSetEnv(cEmpThd,aData[2,GetFldPos("PSN_FILIAL")])`

### 1.14 Contabilidade\TCTBA045.PRW
- **Linha 1796**: `RpcSetEnv(cEmpThd,aData[2,GetFldPos("PA7_FILIAL")])`

### 1.15 Contabilidade\TCTBA046.PRW
- **Linha 604**: `RpcSetEnv(cEmpAnt, cFilAnt)`
- **Linha 1949**: `RpcSetEnv(cEmpThd,aData[2,GetFldPos("PA7_FILIAL")])`

### 1.16 Contabilidade\TCTBA053.PRW
- **Linha 41**: `RpcSetEnv(c_EmpAnt, c_FilAnt)`

### 1.17 Contabilidade\TCTBA055.PRW
- **Linha 32**: `RpcSetEnv(c_EmpAnt, c_FilAnt)`

### 1.18 Contabilidade\TCTBA057.PRW
- **Linha 40**: `RpcSetEnv(c_EmpAnt, c_FilAnt)`

### 1.19 Contabilidade\TCTBA060.PRW
- **Linha 37**: `RpcSetEnv(c_EmpAnt, c_FilAnt)`

### 1.20 Contabilidade\TCTBA062.PRW
- **Linha 37**: `RpcSetEnv(c_EmpAnt, c_FilAnt)`

### 1.21 Contabilidade\TCTBA064.PRW
- **Linha 34**: `RpcSetEnv(c_EmpAnt, c_FilAnt)`

### 1.22 Contabilidade\TCTBA066.PRW
- **Linha 37**: `RpcSetEnv(c_EmpAnt, c_FilAnt)`

### 1.23 Contabilidade\TCTBA067.PRW
- **Linha 34**: `RpcSetEnv(c_EmpAnt, c_FilAnt)`

### 1.24 Contabilidade\TCTBA220.PRW
- **Linha 150**: `RpcSetEnv(aSM0[nEmpAtu][SM0_GRPEMP], aSM0[nEmpAtu][SM0_CODFIL],,"CTB")`
- **Linha 6230**: `RpcSetEnv(SM0->M0_CODIGO, IIf( lFWCodFil, FWGETCODFILIAL, SM0->M0_CODFIL ),,,,, aCtbFiles )`

### 1.25 Contabilidade\TCTBR003.PRW
- **Linha 266**: `RpcSetEnv(cEmpPrep,cFilPrep)`

### 1.26 Contabilidade\TCTBR009.PRW
- **Linha 929**: `RpcSetEnv( cEmp, cFilialDe )`

### 1.27 Contabilidade\TCTBR011.PRW
- **Linha 197**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.28 Contabilidade\TCTBR012.PRW
- **Linha 193**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.29 Contabilidade\TCTBR111.PRW
- **Linha 202**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.30 Contabilidade\TCTBS001.PRW
- **Linha 570**: `If RPCSetEnv(c_empresa,c_Filial)`
- **Linha 685**: `If RPCSetEnv(c_empresa,c_Filial)`
- **Linha 901**: `lRet := RpcSetEnv( cEmp,cFil,,,,,)`

### 1.31 Faturamento\FaturamentoDia\FatDia01.prw
- **Linha 10**: `RpcSetEnv(cEmpresa, cFilEmp)`
- **Linha 75**: `RpcSetEnv(cEmpresa, cFilEmp)`

### 1.32 Faturamento\FaturamentoDia\FatDia02.prw
- **Linha 7**: `RpcSetEnv(cEmpresa, cFilEmp)`
- **Linha 38**: `RpcSetEnv(cEmpresa, cFilEmp)`

### 1.33 Faturamento\FaturamentoDia\FaturaSendMail.tlpp
- **Linha 8**: `RpcSetEnv("00" ,"00202001000")`

### 1.34 Faturamento\TFATA012.PRW
- **Linha 86**: `RpcSetEnv( cEmp, cFil )`
- **Linha 178**: `RpcSetEnv( cBkpEmp, cBkpFil )`

### 1.35 Faturamento\TFATA015.prw
- **Linha 634**: `If RpcSetEnv(aParam[1],aParam[2])`
- **Linha 646**: `If RpcSetEnv(aParam[1],aEmps[nX])`

### 1.36 Faturamento\TFATA022.prw
- **Linha 725**: `If RpcSetEnv(aParam[1],aParam[2])`
- **Linha 745**: `RpcSetEnv(aParam[1],aParam[2])`

### 1.37 Faturamento\TFATA050.PRW
- **Linha 23**: `If ! RpcSetEnv("00","00001000100")`

### 1.38 Faturamento\TFATA063.PRW
- **Linha 31**: `RpcSetEnv(clEmp,clFil)`

### 1.39 Faturamento\TFATS001.prw
- **Linha 901**: `RpcSetEnv(c_empresa,c_filial)//prepara ambiente devido função Baixas`
- **Linha 1326**: `RpcSetEnv(cEmpTemp,cFilTemp)//para que TCRMX029 varre a zx5 da respectiva empresa adiante`
- **Linha 1626**: `RpcSetEnv("00","00001000100")`
- **Linha 1668**: `RpcSetEnv(c_empresa,c_filial, "PROTHEUS-AUTO","AP710")`
- **Linha 1887**: `RpcSetEnv(cEmpTemp,cFilTemp)//para que TCRMX029 varre a zx5 da respectiva empresa adiante`
- **Linha 2180**: `RpcSetEnv(_WS_EMPRESA,_WS_FILIAL)//WS traz cFilant em branco,extraimos de cNumEmp`
- **Linha 2524**: `RpcSetEnv("00","00001000100")`
- **Linha 3006**: `RpcSetEnv("00","00001000100")`
- **Linha 3467**: `RpcSetEnv("00","00001000100")`
- **Linha 3524**: `RpcSetEnv(cEmpTemp, cFilTemp)`
- **Linha 3692**: `RPCSetEnv(AllTrim(::c_empresa), AllTrim(::c_Filial))`
- **Linha 3804**: `If RPCSetEnv(::c_empresa,::c_Filial)`
- **Linha 3929**: `RpcSetEnv( AllTrim(c_empresa), AllTrim(c_filial))//WS traz cFilant em branco,extraimos de cNumEmp`
- **Linha 4278**: `RpcSetEnv( AllTrim(c_empresa), AllTrim(c_filial))//WS traz cFilant em branco,extraimos de cNumEmp`
- **Linha 4640**: `RpcSetEnv(cEmpTemp,cFilTemp)//WS traz cFilant em branco,extraimos de cNumEmp`
- **Linha 4873**: `RpcSetEnv(cEmpTemp,cFilTemp)`
- **Linha 6898**: `_lOpen := RpcSetEnv(c_Empresa, c_Filial)`
- **Linha 6978**: `_lOpen := RpcSetEnv(c_Empresa, c_Filial)`
- **Linha 7148**: `_lOpen := RpcSetEnv(c_Empresa, c_Filial)`
- **Linha 7354**: `RpcSetEnv( __cEmpresa, __cFilial )`

### 1.40 Faturamento\TFATS002.PRW
- **Linha 538**: `RpcSetEnv("00","00001000100")`

### 1.41 Faturamento\TFATS003.PRW
- **Linha 344**: `RpcSetEnv(cEmpTemp, cFilTemp)`
- **Linha 636**: `RpcSetEnv(cEmpTemp, cFilTemp)`
- **Linha 813**: `RpcSetEnv(cEmpTemp, cFilTemp)`
- **Linha 1061**: `RpcSetEnv(cEmpTemp, cFilTemp)`

### 1.42 Financeiro\Bordero_12_1_25_TDI\TILoadBor.prw
- **Linha 102**: `RpcSetEnv(cEmp,cFil)`

### 1.43 Financeiro\Gesplan\TIGESJ01.PRW
- **Linha 18**: `RpcSetEnv( '00', '00001000100')`

### 1.44 Financeiro\Gesplan\TIGESUP1.PRW
- **Linha 22**: `RpcSetEnv( "00", "00001000100")`
- **Linha 29**: `RpcSetEnv( aRetParam[1], aRetParam[2])`

### 1.45 Financeiro\Gesplan\TIGPW000.PRW
- **Linha 33**: `RpcSetEnv( '00', '00001000100')`

### 1.46 Financeiro\Gesplan\TIGPW001.PRW
- **Linha 31**: `RpcSetEnv( '00', '00001000100')`

### 1.47 Financeiro\Gesplan\TIGW01MI.PRW
- **Linha 31**: `RpcSetEnv( '00', '00001000100')`

### 1.48 Financeiro\MarketPlace\TFINA301.TLPP
- **Linha 40**: `RpcSetEnv(cEmp, cFil,,,,,,,,,)`

### 1.49 Financeiro\MarketPlace\TFINA368.PRW
- **Linha 46**: `RpcSetEnv(cEmp, cFil,,,,,,,,,)`

### 1.50 Financeiro\Receiv\Dimensa\TIRCJD00.PRW
- **Linha 41**: `RpcSetEnv( '60', '60001000100')`
- **Linha 45**: `RpcSetEnv('60', '60001000100')`
- **Linha 243**: `RpcSetEnv( '60', '60001000100')`
- **Linha 247**: `RpcSetEnv('60', '60001000100')`
- **Linha 269**: `RpcSetEnv( '60', '60001000100')`
- **Linha 273**: `RpcSetEnv('60', '60001000100')`
- **Linha 301**: `RpcSetEnv( '60', '60001000100')`
- **Linha 305**: `RpcSetEnv('60', '60001000100')`
- **Linha 324**: `RpcSetEnv( '60', '60001000100')`
- **Linha 328**: `RpcSetEnv('60', '60001000100')`
- **Linha 350**: `RpcSetEnv( '60', '60001000100')`
- **Linha 354**: `RpcSetEnv('60', '60001000100')`
- **Linha 373**: `RpcSetEnv( '60', '60001000100')`
- **Linha 377**: `RpcSetEnv('60', '60001000100')`
- **Linha 399**: `RpcSetEnv( '60', '60001000100')`
- **Linha 403**: `RpcSetEnv('60', '60001000100')`

### 1.51 Financeiro\Receiv\TIRCJ000.PRW
- **Linha 43**: `RpcSetEnv( '00', '00001000100')`
- **Linha 47**: `RpcSetEnv( '00', '00001000100')`
- **Linha 252**: `RpcSetEnv( '00', '00001000100')`
- **Linha 274**: `RpcSetEnv( '00', '00001000100')`
- **Linha 302**: `RpcSetEnv( '00', '00001000100')`
- **Linha 321**: `RpcSetEnv( '00', '00001000100')`
- **Linha 343**: `RpcSetEnv( '00', '00001000100')`
- **Linha 362**: `RpcSetEnv( '00', '00001000100')`
- **Linha 384**: `RpcSetEnv( '00', '00001000100')`

### 1.52 Financeiro\Receiv\TIRCJ011.PRW
- **Linha 24**: `RpcSetEnv('00', '00001000100')`

### 1.53 Financeiro\Receiv\TIRCJ012.PRW
- **Linha 32**: `RpcSetEnv(cEmpProc, cFilProc)`

### 1.54 Financeiro\Receiv\TIRCUPD1.PRW
- **Linha 28**: `RpcSetEnv( cEmpresa,; // c - Código da empresa.`

### 1.55 Financeiro\Receiv\TIRCV001.PRW
- **Linha 259**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.56 Financeiro\Receiv\TIRCV002.PRW
- **Linha 151**: `RpcSetEnv(cEmp, cFil)`

### 1.57 Financeiro\Receiv\TIRCV003.PRW
- **Linha 304**: `lRet := RpcSetEnv(cEmp, cFil)`

### 1.58 Financeiro\Receiv\TIRCV004.PRW
- **Linha 121**: `RpcSetEnv(cEmp, cFil)`

### 1.59 Financeiro\Receiv\TIRCV012.PRW
- **Linha 229**: `RpcSetEnv(cEmp, cFil)`

### 1.60 Financeiro\Receiv\TIRCV02B.PRW
- **Linha 137**: `RpcSetEnv(cEmp, cFil)`

### 1.61 Financeiro\Receiv\TIRCV04B.PRW
- **Linha 113**: `RpcSetEnv(cEmp, cFil)`

### 1.62 Financeiro\Receiv\TIRCX000.prw
- **Linha 506**: `If !RpcSetEnv(cEmp, cFil)`
- **Linha 514**: `If !RpcSetEnv(cEmp, cFil)`
- **Linha 629**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.63 Financeiro\Relatórios\TFINR710.PRW
- **Linha 55**: `RpcSetEnv(cEmpPar,cFilPar)`

### 1.64 Financeiro\Vadu\TIVDJ000.PRW
- **Linha 20**: `RpcSetEnv( '00', '00001000100')`
- **Linha 68**: `RpcSetEnv( '00', '00001000100')`

### 1.65 Financeiro\Vadu\TIVDU001.PRW
- **Linha 25**: `RpcSetEnv('00', '00001000100')`
- **Linha 113**: `RpcSetEnv('00', '00001000100')`

### 1.66 Financeiro\Vadu\TIVDU002.PRW
- **Linha 25**: `RpcSetEnv('00', '00001000100')`
- **Linha 113**: `RpcSetEnv('00', '00001000100')`

### 1.67 Financeiro\Vadu\TIVDU003.prw
- **Linha 27**: `RpcSetEnv('00', '00001000100')`
- **Linha 115**: `RpcSetEnv('00', '00001000100')`

### 1.68 Financeiro\Vadu\TIVDU007.PRW
- **Linha 28**: `RpcSetEnv( '00', '00001000100')`
- **Linha 110**: `RpcSetEnv( '00', '00001000100')`

### 1.69 Financeiro\Vadu\TIVDU009.PRW
- **Linha 35**: `RpcSetEnv('00', '00001000100')`

### 1.70 Financeiro\Viagens\Integracoes\TFINJ001.prw
- **Linha 190**: `If RPCSetEnv(cGrupo, cFil,,,"FIN")`
- **Linha 226**: `RPCSetEnv(cGrupo, cFil,,,"FIN")`

### 1.71 Financeiro\Viagens\Integracoes\TFINJ002.prw
- **Linha 16**: `RPCSetEnv(_aParam[1],_aParam[2],,,"FIN")`
- **Linha 36**: `RPCSetEnv(_cEmpresa,_cFilial,,,"FIN")`

### 1.72 Financeiro\Viagens\Integracoes\TFINJ003.prw
- **Linha 24**: `RpcSetEnv("00","00001000100")`
- **Linha 60**: `If !RpcSetEnv( _cEmp , _cFil )`

### 1.73 Financeiro\Viagens\Integracoes\TFINJ004.prw
- **Linha 48**: `RpcSetEnv("00","00001000100")`
- **Linha 363**: `RpcSetEnv("00","00001000100")`

### 1.74 Financeiro\Viagens\Integracoes\TFINJCSV.prw
- **Linha 164**: `If RPCSetEnv(cGrupo, cFil,,,"FIN")`
- **Linha 200**: `RPCSetEnv(cGrupo, cFil,,,"FIN")`

### 1.75 Financeiro\Viagens\Integracoes\TFINJINS.prw
- **Linha 163**: `If RPCSetEnv(cGrupo, cFil,,,"FIN")`
- **Linha 199**: `RPCSetEnv(cGrupo, cFil,,,"FIN")`

### 1.76 Financeiro\TFCRR002.prw
- **Linha 155**: `RpcSetEnv(pEmpAnt,pFilAnt, , , "FIN", , {"SE1"})`

### 1.77 Financeiro\TFINA014.prw
- **Linha 1856**: `RpcSetEnv(aParam[1], aParam[2])`

### 1.78 Financeiro\TFINA050.prw
- **Linha 108**: `If !RpcSetEnv(__cEmpresa,__cFilial)`
- **Linha 1000**: `If !RpcSetEnv(__cEmpresa,__cFilial)`

### 1.79 Financeiro\TFINA055.prw
- **Linha 141**: `If !RpcSetEnv(__cEmpresa,__cFilial)`
- **Linha 1006**: `If !RpcSetEnv(cEmpProc, cFilProc)`
- **Linha 2938**: `If !RpcSetEnv(cEmpProc, cFilProc)`
- **Linha 4569**: `If !RpcSetEnv(cEmp, cFil)`
- **Linha 4752**: `If !RpcSetEnv(cEmpProc, cFilProc)`
- **Linha 4874**: `If !RpcSetEnv('00', '00001000100')`
- **Linha 5387**: `If !RpcSetEnv('00', '00001000100')`

### 1.80 Financeiro\TFINA059.prw
- **Linha 76**: `If !RpcSetEnv(__cEmpresa,__cFilial)`

### 1.81 Financeiro\TFINA071.PRW
- **Linha 1354**: `RpcSetEnv(cEmpPrep, cFilPrep)`
- **Linha 2297**: `RpcSetEnv(cEmpPrep, cFilPrep)`

### 1.82 Financeiro\TFINA081.PRW
- **Linha 122**: `RpcSetEnv( cCodEmp, cCodFil )`
- **Linha 1326**: `RpcSetEnv( cCodEmp, cCodFil )`
- **Linha 2904**: `RpcSetEnv( cCodEmp, cCodFil )`

### 1.83 Financeiro\TFINA082.PRW
- **Linha 36**: `RpcSetEnv("00")`
- **Linha 107**: `RpcSetEnv(aParam[1])`
- **Linha 2979**: `RpcSetEnv(cCodEmp,cCodFil)`
- **Linha 3252**: `RpcSetEnv(cCodEmp, cCodFil)`
- **Linha 3630**: `RpcSetEnv( cCodEmp, cCodFil)`
- **Linha 4168**: `RpcSetEnv( cCodEmp, cCodFil)`
- **Linha 4573**: `RpcSetEnv( cCodEmp, cCodFil )`
- **Linha 4745**: `RpcSetEnv( cParEmp, cParFil )`
- **Linha 4975**: `RpcSetEnv( cCodEmp, cCodFil)`
- **Linha 5206**: `RpcSetEnv( cCodEmp, cCodFil)`

### 1.84 Financeiro\TFINA085.PRW
- **Linha 43**: `RpcSetEnv( cCodEmp, cCodFil )`
- **Linha 422**: `If !RpcSetEnv(_cEmp,_cFil)`

### 1.85 Financeiro\TFINA086.PRW
- **Linha 53**: `RpcSetEnv(aParam[1],aParam[2])`

### 1.86 Financeiro\TFINA087.PRW
- **Linha 56**: `RpcSetEnv( cCodEmp, cCodFil)`
- **Linha 369**: `RpcSetEnv( cCodEmp, cCodFil)`
- **Linha 665**: `RpcSetEnv( cCodEmp, cCodFil )`
- **Linha 967**: `RpcSetEnv(cCodEmp, cCodFil)`

### 1.87 Financeiro\TFINA088.PRW
- **Linha 124**: `RpcSetEnv(cParEmp)`
- **Linha 1974**: `RpcSetEnv( cCodEmp, cCodFil)`
- **Linha 2044**: `RpcSetEnv(cEmpPos,aDados[nX][1])`

### 1.88 Financeiro\TFINA091.PRW
- **Linha 59**: `RpcSetEnv(cParEmp, cParFil)`

### 1.89 Financeiro\TFINA095.prw
- **Linha 55**: `RpcSetEnv(cEmpIni,cFilIni,,,"FIN",,{"SE1","SA1","ZRM","ZXS"})`

### 1.90 Financeiro\TFINA098.PRW
- **Linha 1149**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.91 Financeiro\TFINA099.PRW
- **Linha 451**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.92 Financeiro\TFINA102.PRW
- **Linha 46**: `RpcSetEnv(cCodEmp,cCodFil)`

### 1.93 Financeiro\TFINA103.prw
- **Linha 20**: `IF !RpcSetEnv('00', '00001000100')`

### 1.94 Financeiro\TFINA105.PRW
- **Linha 882**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.95 Financeiro\TFINA106.prw
- **Linha 19**: `If !RpcSetEnv('00', '00001000100')`
- **Linha 87**: `RpcSetEnv(cEmp,cFil)`

### 1.96 Financeiro\TFINA202.PRW
- **Linha 83**: `RpcSetEnv(cParEmp)`
- **Linha 925**: `RpcSetEnv( cParEmp, cParFil )`

### 1.97 Financeiro\TFINA940.PRW
- **Linha 903**: `RpcSetEnv( cEmpX, cFilX,,,'FIN')`
- **Linha 1447**: `RpcSetEnv( cEmpX, cFilX,,,'FIN')`
- **Linha 1943**: `RpcSetEnv( cEmpX, cFilX,,,'FIN')`
- **Linha 2508**: `RpcSetEnv( cEmpX, cFilX,,,'FIN')`
- **Linha 2720**: `RpcSetEnv( cEmpX, cFilX,,,'FIN')`
- **Linha 3182**: `RpcSetEnv( cEmpX, cFilX,,,'FIN')`

### 1.98 Financeiro\TFINAAPF.PRW
- **Linha 969**: `RpcSetEnv(cEmpPrep, cFilPrep)`

### 1.99 Financeiro\TFINAREV.PRW
- **Linha 445**: `RpcSetEnv(cEmpPrep, cFilPrep)`

### 1.100 Financeiro\TFINJ005.PRW
- **Linha 48**: `RpcSetEnv(cEmpJob,cFilJob)`
- **Linha 285**: `RpcSetEnv(cEmpJob,cFilJob)`
- **Linha 513**: `RpcSetEnv(cEmpJob,cFilJob)`
- **Linha 728**: `RpcSetEnv(cCodEmp,cCodFil)`
- **Linha 1064**: `RpcSetEnv("00","00001000100")`
- **Linha 1098**: `RpcSetEnv(cEmp,cFil)`

### 1.101 Financeiro\TFINJ006.PRW
- **Linha 29**: `RpcSetEnv("00","00001000100")`

### 1.102 Financeiro\TFINJ011.PRW
- **Linha 775**: `lRet := RpcSetEnv( cEmpContr, cFilContr,,,,, aTabOpen )`
- **Linha 809**: `If RpcSetEnv( uEmpOp, uFilOp )`
- **Linha 1947**: `If !RpcSetEnv('00', '00001000100')`
- **Linha 2032**: `If !RpcSetEnv(__cEmpresa,__cFilial)`

### 1.103 Financeiro\TFINJ013.prw
- **Linha 110**: `RpcSetEnv("00","00001000100")`

### 1.104 Financeiro\TFINJ014.PRW
- **Linha 47**: `If !RpcSetEnv(cEmpProc, cFilProc)`
- **Linha 325**: `If !RpcSetEnv(aParam[5], aParam[6])`
- **Linha 404**: `If !RpcSetEnv(aParam[5], aParam[6])`

### 1.105 Financeiro\TFINJ020.prw
- **Linha 28**: `If RPCSetEnv(cEmpGr,cFilGr,,,"FIN")`

### 1.106 Financeiro\TFINJ035.prw
- **Linha 42**: `RpcSetEnv( '00', '00001000100')`

### 1.107 Financeiro\TFINJ036.prw
- **Linha 31**: `RpcSetEnv( '00', '00001000100')`

### 1.108 Financeiro\TFINJ037.PRW
- **Linha 29**: `RpcSetEnv( '00', '00001000100')`

### 1.109 Financeiro\TFINJ038.PRW
- **Linha 23**: `If !RpcSetEnv('00', '00001000100')`

### 1.110 Financeiro\TFINJ039.PRW
- **Linha 29**: `If !RpcSetEnv(cEmpPDD,cFilPDD)`

### 1.111 Financeiro\TFINJ098.PRW
- **Linha 54**: `If !RpcSetEnv(cEmpPDD,cFilPDD)`

### 1.112 Financeiro\TFINJ100.prw
- **Linha 83**: `If !RpcSetEnv(__cEmpresa,__cFilial)`
- **Linha 457**: `If !RpcSetEnv(__cEmpresa,__cFilial)`

### 1.113 Financeiro\TFINJ101.PRW
- **Linha 29**: `If RpcSetEnv( cEmpProc, cFilProc )`
- **Linha 251**: `If RpcSetEnv( "00", "00001000100" )`

### 1.114 Financeiro\TFINR023.PRW
- **Linha 1842**: `RpcSetEnv(cEmpPrep, cFilPrep)`

### 1.115 Financeiro\TFINR024.PRW
- **Linha 587**: `RpcSetEnv(cEmpPrep, cFilPrep)`

### 1.116 Financeiro\TFINR034.prw
- **Linha 225**: `RpcSetEnv(cEmpAnt, cFilAnt)`
- **Linha 763**: `RpcSetEnv(cEmpAnt, cFilAnt)`
- **Linha 965**: `RpcSetEnv(cEmpAnt, cFilAnt)`
- **Linha 2004**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.117 Financeiro\TFINR098.PRW
- **Linha 45**: `If !RpcSetEnv(cEmp, cFil)`
- **Linha 222**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.118 Financeiro\TFINR099.PRW
- **Linha 176**: `RpcSetEnv(cEmpAnt, cFilAnt)`

### 1.119 Financeiro\TFINS001.PRW
- **Linha 988**: `If (Type("cFilAnt") == "C" .And. cFilAnt == cFilPag) .Or. RPCSetEnv(cEmpPag,cFilPag)`
- **Linha 1719**: `RPCSetEnv( c_empresa, c_Filial )`
- **Linha 1722**: `If cFilAnt == c_filial .Or. RPCSetEnv(c_empresa,c_Filial)`
- **Linha 2208**: `lRet := RpcSetEnv( cEmp,cFil,,,,,)`

### 1.120 Financeiro\TFINS010.prw
- **Linha 565**: `If !RPCSetEnv("00","00001000100")`
- **Linha 601**: `If !RPCSetEnv("00","00001000100")`

### 1.121 Financeiro\TFINSC01.PRW
- **Linha 45**: `RpcSetEnv(cParEmp, cParFil)`

### 1.122 Financeiro\TFINX005.prw
- **Linha 290**: `RpcSetEnv( _cEmpAnt, _cFilAnt)`
- **Linha 454**: `RpcSetEnv( _cEmpAnt, _cFilAnt)`
- **Linha 560**: `RpcSetEnv( _cEmpAnt, _cFilAnt)`
- **Linha 674**: `RpcSetEnv( _cEmpAnt, _cFilAnt)`
- **Linha 732**: `RpcSetEnv( _cEmpAnt, _cFilAnt)`
- **Linha 796**: `RpcSetEnv( _cEmpAnt, _cFilAnt)`
- **Linha 866**: `RpcSetEnv( _cEmpAnt, _cFilAnt)`
- **Linha 958**: `RpcSetEnv( _cEmpAnt, _cFilAnt)`
- **Linha 1317**: `RpcSetEnv("00","00001000100")`
- **Linha 1478**: `If !RpcSetEnv('00', '00001000100')`
- **Linha 1602**: `If !RpcSetEnv(cEmp, cFil)`

### 1.123 Financeiro\TFINX200.PRW
- **Linha 1839**: `RpcSetEnv(cCodEmp,cCodFil)`
- **Linha 2242**: `RpcSetEnv("00","00001000100")`

### 1.124 Financeiro\TLCAJCONT.prw
- **Linha 489**: `If !RpcSetEnv(cEmp, cFil)`
- **Linha 565**: `If !RpcSetEnv(aParam[5], aParam[6])`

### 1.125 Financeiro\TRFR130X.PRW
- **Linha 620**: `RpcSetEnv("00", __cFil)`

### 1.126 Gestão de Contratos\TGCTA001.PRW
- **Linha 235**: `RPCSetEnv(cEmpPc,AllTrim(cFilPc))`
- **Linha 378**: `If RPCSetEnv(cEmpPc,AllTrim(cFilPc))`

### 1.127 Interceptor\Classes\TIINTERCEPTOR.PRW
- **Linha 680**: `RpcSetEnv( '00', '00001000100')`
- **Linha 742**: `RpcSetEnv( '00', '00001000100')`
- **Linha 1224**: `RpcSetEnv( '00', '00001000100')`

### 1.128 Interceptor\Funcoes\TIINTXFUN.PRW
- **Linha 99**: `If !RpcSetEnv(cEmp, cFil)`
- **Linha 134**: `RpcSetEnv(cEmp,cFil)`

### 1.129 Interceptor\Jobs\Dimensa\TIINTJD00.PRW
- **Linha 25**: `RpcSetEnv( '60', '60001000100')`
- **Linha 75**: `RpcSetEnv( '60', '60001000100')`
- **Linha 118**: `RpcSetEnv( '60', '60001000100')`

### 1.130 Interceptor\Jobs\TIINTJ000.PRW
- **Linha 25**: `RpcSetEnv( '00', '00001000100')`
- **Linha 81**: `RpcSetEnv( '00', '00001000100')`
- **Linha 126**: `RpcSetEnv( '00', '00001000100')`
- **Linha 170**: `RpcSetEnv( cEmp, cEmp)`

### 1.131 Interceptor\WebServices\Dimensa\TIINTERCPTDIMEN.PRW
- **Linha 41**: `RpcSetEnv( '60', '60001000100')`
- **Linha 113**: `RpcSetEnv( '60', '60001000100')`

### 1.132 Interceptor\WebServices\TIWSINTERCEPTOR.PRW
- **Linha 41**: `RpcSetEnv( '00', '00001000100')`
- **Linha 114**: `RpcSetEnv( '00', '00001000100')`

### 1.133 MarketPlace\TMarketPlaceRevenueAllocation.tlpp
- **Linha 715**: `RpcSetEnv('00', '00001000100')`

### 1.134 MarketPlace\TMKTX003.PRW
- **Linha 76**: `RpcSetEnv("00")`

### 1.135 Marketplace_Checkout\service\functionToProcess.tlpp
- **Linha 42**: `RpcSetEnv("00", "00001000100")`
- **Linha 152**: `RpcSetEnv("00", "00001000100")`
- **Linha 231**: `RpcSetEnv("00", "00001000100")`
- **Linha 345**: `RpcSetEnv("00", "00001000100")`
- **Linha 373**: `RpcSetEnv("00", "00001000100")`
- **Linha 394**: `RpcSetEnv("00", "00001000100")`
- **Linha 439**: `RPCSetEnv("00","00001000100")`
- **Linha 515**: `RpcSetEnv("90", "90001000100")`

### 1.136 Marketplace_Checkout\service\getTokenAfterCheckoutService.tlpp
- **Linha 104**: `RpcSetEnv("90", "90001000100")`
- **Linha 167**: `RpcSetEnv("00", "00001000100")`

### 1.137 Marketplace_Checkout\service\jobsToControlCheckoutProcessService.tlpp
- **Linha 14**: `RpcSetEnv("90", "90001000100")`
- **Linha 53**: `RpcSetEnv("90", "90001000100")`
- **Linha 92**: `RpcSetEnv("90", "90001000100")`
- **Linha 125**: `RpcSetEnv("00", "00001000100")`
- **Linha 152**: `RpcSetEnv("90", "90001000100")`
- **Linha 182**: `RpcSetEnv("90", "90001000100")`
- **Linha 238**: `RpcSetEnv("90", "90001000100")`

### 1.138 Telecobranca\TTMKA180.prw
- **Linha 1569**: `RpcSetEnv( aParam[1], aParam[2] )`

### 1.139 Viagens\JOB\STJOBCLI.prw
- **Linha 35**: `If !RPCSetEnv("00","00001000100")`

### 1.140 Viagens\JOB\STJOBRES.prw
- **Linha 37**: `If !RPCSetEnv("00","00001000100")`

### 1.141 Viagens\JOB\STJOBVGM.PRW
- **Linha 47**: `If ! RPCSetEnv("00","00001000100")`
- **Linha 149**: `If ! RPCSetEnv("00","00001000100")`

### 1.142 Viagens\JOB\VIAGEMXFUN.PRW
- **Linha 43**: `RPCSETENV("00","00001000100")`
- **Linha 394**: `RPCSETENV("00","00001000100")`

---

## 2. FUNÇÕES QUE ALTERAM A VARIÁVEL cEmpAnt

### 2.1 Compras\TCOMJ004.PRW
- **Linha 352**: `cEmpAnt := cEmpPC`
- **Linha 819**: `cEmpAnt := cEmpBKP`

### 2.2 Compras\TCOMJ010.PRW
- **Linha 419**: `cEmpAnt := cEmpSC`
- **Linha 760**: `cEmpAnt := cEmpBKP`

### 2.3 Contabilidade\TCTBA019.prw
- **Linha 335**: `cEmpAnt := LerStr(204,2)`
- **Linha 365**: `cEmpAnt := LerStr(204,2)`
- **Linha 427**: `cEmpAnt := cEmpOri`

### 2.4 Contabilidade\TCTBA053.PRW
- **Linha 745**: `cEmpAnt := U_TIRCVEMP(cFilAnt)`
- **Linha 747**: `cEmpAnt := cEmpBkp`
- **Linha 921**: `cEmpAnt:= U_TIRCVEMP(cFilAnt)`
- **Linha 926**: `cEmpAnt:= U_TIRCVEMP(cFilAnt)`
- **Linha 931**: `cEmpAnt:=cEmpBkp`
- **Linha 1162**: `cEmpAnt := U_TIRCVEMP(cFilAnt)`
- **Linha 1165**: `cEmpAnt := U_TIRCVEMP(cFilAnt)`
- **Linha 1169**: `cEmpAnt:=cEmpBkp`

### 2.5 Contabilidade\TCTBA058.PRW
- **Linha 1219**: `cEmpAnt := U_TIRCVEMP(cFilAnt)`
- **Linha 1221**: `cEmpAnt := cEmpBkp`

### 2.6 Contabilidade\TCTBA220.PRW
- **Linha 101**: `Private __cEmpAnt := cEmpAnt`
- **Linha 130**: `cEmpAnt := aSM0[nContFil][SM0_GRPEMP]`
- **Linha 153**: `cEmpAnt := SM0->M0_CODIGO`
- **Linha 295**: `cEmpAnt := __cEmpAnt`
- **Linha 685**: `cEmpAnt := cEmpX`
- **Linha 727**: `cEmpAnt := cEmpBack`
- **Linha 3494**: `Local cEmpAnt := " "`
- **Linha 3519**: `cEmpAnt := cEmp`

### 2.7 Faturamento\envia_email.tlpp
- **Linha 134**: `::cEmpAnt := ''`
- **Linha 298**: `::cEmpAnt := cEmpParam`

### 2.8 Faturamento\TFATS001.prw
- **Linha 897**: `cEmpant := c_empresa`
- **Linha 1092**: `cEmpant := c_empresa`
- **Linha 3698**: `cEmpant := c_empresa`
- **Linha 5782**: `cEmpAnt := aDados[nx,8,nv,31]`
- **Linha 6900**: `cEmpAnt := c_Empresa`
- **Linha 6980**: `cEmpAnt := c_Empresa`
- **Linha 7150**: `cEmpAnt := c_Empresa`

### 2.9 Financeiro\Receiv\TIRCV001.PRW
- **Linha 250**: `Default cEmpAnt := ''`

### 2.10 Financeiro\Receiv\TIRCV007.PRW
- **Linha 84**: `cEmpAnt := cEmpCur`
- **Linha 182**: `cEmpAnt := cEmpBkp`

### 2.11 Financeiro\Receiv\TIRCV013.PRW
- **Linha 81**: `cEmpAnt := cEmpCur`
- **Linha 166**: `cEmpAnt := cEmpBkp`

### 2.12 Financeiro\Receiv\TIRCV020.PRW
- **Linha 60**: `cEmpAnt := cEmpCur`
- **Linha 68**: `cEmpAnt := cEmpBkp`

### 2.13 Financeiro\Receiv\TIRCX000.prw
- **Linha 620**: `Default cEmpAnt := ''`

### 2.14 Financeiro\Vadu\TIVDU001.PRW
- **Linha 104**: `Default cEmpAnt := ''`

### 2.15 Financeiro\Vadu\TIVDU002.PRW
- **Linha 104**: `Default cEmpAnt := ''`

### 2.16 Financeiro\Vadu\TIVDU003.prw
- **Linha 106**: `Default cEmpAnt := ''`

### 2.17 Financeiro\TFINA016.PRW
- **Linha 3530**: `cEmpAnt := aEmpDaFil[nIndex][1]`

### 2.18 Financeiro\TFINJ014.PRW
- **Linha 51**: `cEmpAnt := cEmpProc//houve circunstância em que apesar de preparada com dados corretos estas variaveis nao estavam de acordo`

---

## 3. FUNÇÕES QUE ALTERAM A VARIÁVEL __cUserId

### 3.1 Contabilidade\TCTBA068.PRW
- **Linha 37**: `__cUserID := "000000"`

### 3.2 Contabilidade\TCTBA069.PRW
- **Linha 49**: `__cUserID := "000000"`

### 3.3 Contabilidade\TCTBA072.PRW
- **Linha 41**: `__cUserID := "000000"`

### 3.4 Faturamento\TFATA461C.PRW
- **Linha 542**: `__cUserId := cUserId`

### 3.5 Financeiro\TFCRR002.prw
- **Linha 157**: `__cUserId:=p__cUserId`

### 3.6 Financeiro\TFINA050.prw
- **Linha 117**: `__cUserId := cUserId`

### 3.7 Financeiro\TFINA055.prw
- **Linha 147**: `__cUserId := '000000'`
- **Linha 996**: `Local __cUserId := ''`
- **Linha 1011**: `__cUserId := aPrep[4]`
- **Linha 1062**: `__cUserId := aParamIPC[IPC__CUSERID]`
- **Linha 2945**: `__cUserId := aPrep[3]`

### 3.8 Financeiro\TFINA085.PRW
- **Linha 426**: `__cUserId := '005297'`

### 3.9 Financeiro\TFINJ102.prw
- **Linha 64**: `__cUserId := "000000"`

### 3.10 Financeiro\TFINR034.prw
- **Linha 235**: `__cUserID := cUsrId`

### 3.11 Financeiro\TFINR098.PRW
- **Linha 49**: `__cUserId := '005297'`

### 3.12 Financeiro\TFINSC01.PRW
- **Linha 322**: `__cUserId := "000000"`

### 3.13 Financeiro\TFINW067.PRW
- **Linha 190**: `__cUserID := aUser[2]`

### 3.14 Financeiro\TWFFINA677.prw
- **Linha 342**: `__cUserID := AllTrim((cAliasTrb)->RD0_CODIGO)`

### 3.15 Financeiro\WFFIA785.prw
- **Linha 206**: `__cUserID := aUser[2]`
- **Linha 339**: `__cUserID := aUser[2]`

### 3.16 Financeiro\WFFINA40.prw
- **Linha 258**: `__cUserID := ''`
- **Linha 261**: `__cUserID := AllTrim((cAliasTrb)->RD0_USER)`
- **Linha 380**: `__cUserID := ''`
- **Linha 383**: `__cUserID := AllTrim((cAliasTrb)->RD0_CODIGO)`

### 3.17 Financeiro\WFFINA667.prw
- **Linha 172**: `__cUserID := aUser[2]`

### 3.18 Telecobranca\TTMKA180.prw
- **Linha 172**: `__CUSERID := cDfCdUser`

---

## 4. RESUMO ESTATÍSTICO

- **Total de arquivos com RPCSetEnv**: 142 arquivos
- **Total de ocorrências de RPCSetEnv**: 350+ ocorrências
- **Total de arquivos que alteram cEmpAnt**: 18 arquivos
- **Total de arquivos que alteram __cUserId**: 18 arquivos

---

## 5. OBSERVAÇÕES IMPORTANTES

1. **Módulo Financeiro**: Concentra a maior quantidade de ocorrências (mais de 200)
2. **Ambiente Padrão**: Predominância da empresa "00" e filial "00001000100"
3. **Empresa Dimensa**: Várias funções específicas para empresa "60" (60001000100)
4. **Marketplace**: Funções específicas para empresa "90" (90001000100)
5. **Jobs**: Grande quantidade de jobs que necessitam configurar ambiente
6. **Módulos Específicos**: Muitas chamadas especificam módulos como "FIN", "CTB", "FAT"
7. **Segurança**: Várias funções alteram __cUserId para valores específicos ou genéricos
8. **Integrações**: Muitas funções relacionadas a integrações e WebServices

---

**Data de criação**: 05/08/2025  
**Responsável**: Documentação automática via análise de código
