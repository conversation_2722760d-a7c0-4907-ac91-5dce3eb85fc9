#Include "Protheus.ch"
#Include "cmsxfun.ch"
#Include 'FWMVCDef.ch'
#INCLUDE "TOPCONN.CH"

/****************************************************************************\
//+-------------------------------------------------------------------------+
//| Arquivo fonte para agrupar funções genéricas utilizadas por Comissões   |
//+---------------------------+---------------------------------------------+
//| Autor: <PERSON><PERSON><PERSON>   | Data: 22/09/2014                            |
//+---------------------------+---------------------------------------------+
//| Sempre que possível, utilizar como nomenclatura padrão:                 |
//| XXZZZZZZZZ()                                                            |
//| XX - Prefixo do módulo da linha Microsiga Protheus (TDI),               |
//|      exemplo: CM para o módulo de Comissões,                            |
//|               CO para o módulo de Contratos, etc.                       |
//| ZZZZZZZZ - Abreviação descritiva do objetivo da função                  |
//|      Exemplo: Função genérica do módulo Comissões: CMRetCom()           |
//+-------------------------------------------------------------------------+
\****************************************************************************/


//-------------------------------------------------------------------
/*/{Protheus.doc} CMGerX5T
Criar o índice H (INPC-TJSP) na tabela X3 no cadastro da SX5

<AUTHOR> dos Santos

@since 23/09/2014
@version P11

@return lRet, lógico, Compatibilidade

@obs CMGerX5T --> jargão --> CoMissões Gera sX5 Tabela X3
/*/
//-------------------------------------------------------------------
User Function CMGerX5T()
Local lRet     := .T.
Local aAreaSX5 := {}

DBSelectArea("SX5")
aAreaSX5 := SX5->(GetArea())
SX5->(DBSetOrder(1)) //X5_FILIAL+X5_TABELA+X5_CHAVE

BEGIN TRANSACTION

If !SX5->(DBSeek(xFilial("SX5")+"X3H"))
	SX5->(RecLock("SX5", .T.))
		SX5->(FieldPut(FieldPos("X5_FILIAL"), xFilial("SX5")))
		SX5->(FieldPut(FieldPos("X5_TABELA"), "X3"))
		SX5->(FieldPut(FieldPos("X5_CHAVE"), "H"))
		SX5->(FieldPut(FieldPos("X5_DESCRI"), "INPC-TJSP"))
		SX5->(FieldPut(FieldPos("X5_DESCSPA"), "INPC-TJSP"))
		SX5->(FieldPut(FieldPos("X5_DESCENG"), "INPC-TJSP"))
	SX5->(MsUnLock())
EndIf

END TRANSACTION

RestArea(aAreaSX5)

Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} CMGerPBL
Criar os registros do índice H (INPC-TJSP) na tabela PBL

<AUTHOR> dos Santos

@since 23/09/2014
@version P11

@return lRet, lógico, Compatibilidade

@obs CMGerPBL --> jargão --> CoMissões Gera PBL índice H
     A base para auto preenchimento em caso de novas filiais será a empresa 00 filial 01
/*/
//-------------------------------------------------------------------
User Function CMGerPBL()
Local lRet      := .T.
Local cAliasQ   := ""
Local cFil01    := "00001000100"
Local cIndice   := "H"
Local cDescri   := ""
Local aAreaPBD  := {}
Local aAreaPBL  := {}
Local cMes      := ""
Local cAno      := ""
Local dDatIni   := SToD("")
Local dDatFim   := SToD("")

If !(cEmpAnt == "00" .And. cFilAnt == "00001000100")
	
	DBSelectArea("PBD")
	aAreaPBD := PBD->(GetArea())
	PBD->(DBSetOrder(1)) //PBD_FILIAL+PBD_COMPET
	
	DBSelectArea("PBL")
	aAreaPBL := PBL->(GetArea())
	PBL->(DBSetOrder(1)) //PBL_FILIAL+PBL_COMPET+PBL_INDICE
	
	cAliasQ := GetNextAlias()
	
	//+------------------------------------------------------------------+
	//| Consulta os índices existentes na empresa 00 filial 00001000100  |
	//+------------------------------------------------------------------+
	BeginSQL Alias cAliasQ
		SELECT * 
		FROM PBL000 AS PBL 
		WHERE PBL.PBL_FILIAL = %Exp:cFil01% 
			AND PBL.PBL_INDICE = %Exp:cIndice% 
			AND PBL.%NotDel% 
	EndSQL
	(cAliasQ)->(DBGoTop())
	
	BEGIN TRANSACTION
	
	//--> Replica os índices para a empresa e filial corrente
	While !(cAliasQ)->(EOF())
		
		//--> Cria a competência na PBD
		If !PBD->(DBSeek(xFilial("PBD")+(cAliasQ)->PBL_COMPET))
			
			cMes    := Left((cAliasQ)->PBL_COMPET, 2)
			cAno    := Right((cAliasQ)->PBL_COMPET, 4)
			cDescri := CMRetMesEx(Val(cMes)) + cAno
			
			dDatIni := SToD(cAno + cMes + "01")
			dDatFim := SToD(cAno + cMes + CMRetUDMes(Val(cMes), Val(cAno)))
			
			PBD->(RecLock("PBD", .T.))
				PBD->PBD_FILIAL := xFilial("PBD")
				PBD->PBD_COMPET := (cAliasQ)->PBL_COMPET
				PBD->PBD_DESCRI := cDescri
				PBD->PBD_DATINI := dDatIni
				PBD->PBD_DATFIM := dDatFim
			PBD->(MsUnLock())
		EndIf
		
		//--> Cria o índice na PBL
		If !PBL->(DBSeek(xFilial("PBL")+(cAliasQ)->PBL_COMPET+cIndice))
			PBL->(RecLock("PBL", .T.))
				PBL->PBL_FILIAL := xFilial("PBL")
				PBL->PBL_COMPET := (cAliasQ)->PBL_COMPET
				PBL->PBL_INDICE := cIndice
				PBL->PBL_PERC   := (cAliasQ)->PBL_PERC
			PBL->(MsUnLock())
		EndIf
		
		(cAliasQ)->(DBSkip())
	EndDo
	
	END TRANSACTION
	
	(cAliasQ)->(DBCloseArea())
EndIf

Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} CMRetMesEx
Recebe o número do mês e devolve o mês por extenso em MAIÚSCULO

<AUTHOR> dos Santos

@param nMes, numérico, Número do mês

@since 23/09/2014
@version P11

@return cMes, caractere, O mês por extenso
/*/
//-------------------------------------------------------------------
Static Function CMRetMesEx(nMes)
Local cMes := ""

Default nMes := 0

Do Case
	Case nMes == 1
		cMes := "JANEIRO "
	Case nMes == 2
		cMes := "FEVEREIRO "
	Case nMes == 3
		cMes := "MARCO "
	Case nMes == 4
		cMes := "ABRIL "
	Case nMes == 5
		cMes := "MAIO "
	Case nMes == 6
		cMes := "JUNHO "
	Case nMes == 7
		cMes := "JULHO "
	Case nMes == 8
		cMes := "AGOSTO "
	Case nMes == 9
		cMes := "SETEMBRO "
	Case nMes == 10
		cMes := "OUTUBRO "
	Case nMes == 11
		cMes := "NOVEMBRO "
	Case nMes == 12
		cMes := "DEZEMBRO "
EndCase

Return cMes


//-------------------------------------------------------------------
/*/{Protheus.doc} CMRetUDMes
Recebe o número do mês e devolve o último dia do mês

<AUTHOR> dos Santos

@param nMes, numérico, Número do Mês
@param nAno, numérico, Número do Ano

@since 23/09/2014
@version P11

@return cDia, caractere, O último do dia do mês
/*/
//-------------------------------------------------------------------
Static Function CMRetUDMes(nMes, nAno)
Local cDia := ""

Default nMes := 0
Default nAno := 0

If nMes > 0 .And. nAno > 0
	Do Case
		Case nMes == 1
			cDia := "31"
		Case nMes == 2
			If (nAno % 4) == 0
				cDia := "29"
			Else
				cDia := "28"
			EndIf 
		Case nMes == 3
			cDia := "31"
		Case nMes == 4
			cDia := "30"
		Case nMes == 5
			cDia := "31"
		Case nMes == 6
			cDia := "30"
		Case nMes == 7
			cDia := "31"
		Case nMes == 8
			cDia := "31"
		Case nMes == 9
			cDia := "30"
		Case nMes == 10
			cDia := "31"
		Case nMes == 11
			cDia := "30"
		Case nMes == 12
			cDia := "31"
	EndCase
EndIf

Return cDia


//-------------------------------------------------------------------
/*/{Protheus.doc} CMRetApro
Função genérica para processar as aprovações de processo via Fluig

<AUTHOR> dos Santos

@param cIDProc, caractere, Identificador do processo no Fluig
@param nOperacao, numérico, Operação da aprovação
@param cMotivo, caractere, Motivo para tabela ZXS
@param cAprovador, caractere, Aprovador do processo
@param cObservacao, caractere, Texto da observação digitada pelo aprovador

@since 03/10/2014
@version P11

@return aRet, array, Vetor de duas posições indicando processo OK e texto de mensagem
/*/
//-------------------------------------------------------------------
User Function CMRetApro(cIDProc, nOperacao, cMotivo, cObservacao, cAprovador)
Local aRet     := {.T., ""}
Local aAreaZXS := {}

If Empty(cIDProc) .Or. Empty(cAprovador)
	aRet[01] := .F.
	aRet[02] := STR0001 //"Dados de ID do processo ou Aprovador inválidos."
EndIf

If aRet[01]
	DBSelectArea("ZXS")
	aAreaZXS := ZXS->(GetArea())
	ZXS->(DBSetOrder(2)) //ZXS_FILIAL+ZXS_IDPROC
	
	If ZXS->(DBSeek(xFilial("ZXS")+cIDProc))
		If ZXS->ZXS_TIPO == "C1"
			aRet := U_M18RETAPV(cIDProc, nOperacao, cMotivo, cObservacao, cAprovador)
		EndIf
	Else
		aRet[01] := .F.
		aRet[02] := STR0002 //"Processo de aprovação não encontrado."
	EndIf
	
	RestArea(aAreaZXS)
EndIf

Return aRet


//-------------------------------------------------------------------
/*/{Protheus.doc} ADYTPROP
Definir o modo de edição do campo ADY__TPROP

<AUTHOR> dos Santos
@since 17/06/2014
@version P11

@return lRet, lógico, Chave que define se pode editar ou não o campo
/*/
//-------------------------------------------------------------------
User Function ADYTPROP()
Local lRet     := .F.
Local aAreaSA1 := {}

Local cCliente := IIf(Type("M->ADY_CODIGO") # "U", M->ADY_CODIGO, "")
Local cLoja    := IIf(Type("M->ADY_LOJA") # "U"  , M->ADY_LOJA  , "")

If !Empty(cCliente) .And. !Empty(cLoja)
	DBSelectArea("SA1")
	aAreaSA1 := SA1->(GetArea())
	SA1->(DBSetOrder(1)) //A1_FILIAL+A1_COD+A1_LOJA
	
	If SA1->(FieldPos("A1_SATIV6")) > 0 
		If SA1->(DBSeek(xFilial("SA1")+cCliente+cLoja))
			//+-------------------------------------+
			//| Empresa pública? (1=Nao;2=Sim)      |
			//+-------------------------------------+
			If AllTrim(SA1->A1_SATIV6) == "2"
				lRet := .T.
			EndIf
		EndIf
	EndIf
	
	RestArea(aAreaSA1)
EndIf

Return lRet


/*/{Protheus.doc} GetLinRec
Retorna Linha de Receita do Produto
<AUTHOR>
@since 22/08/2016
@version undefined
@param cCodProd, characters, descricao
@param lOnly, logical, descricao
@type function
/*/
User Function GetLinRec(cCodProd,lOnly)
Local cLinRec := ""
Local aArea   := GetArea()
Local aAreaSB1 := SB1->(GetArea())
Local aAreaSBM := SBM->(GetArea())
Local aAreaAOM := AOM->(GetArea())

Default lOnly := .F.
Default cCodProd := ""


DbSelectArea("SB1")
SB1->(dbSetOrder(1))

DbSelectArea("SBM")
SBM->(dbSetOrder(1))

DbSelectArea("AOM")
AOM->(dbSetOrder(1))

If !Empty(cCodProd)
	
	If SB1->(DbSeek(xFilial("SB1")+cCodProd))
	
		If SBM->(DbSeek(xFilial("SBM")+SB1->B1_GRUPO))
			
			//Somente Linha de Receita ou Linha + Descrição
			If lOnly
				cLinRec := SBM->BM_XLINREC 
			Else
				
				If AOM->(DbSeek(xFilial("AOM")+"000001"+SBM->BM_XLINREC))
					cLinRec := AllTrim(SBM->BM_XLINREC) + "-" + AllTrim(AOM->AOM_DESCRI)
				EndIf
			EndIf
		
		EndIf
	
	EndIf

EndIf

//sRestArea(aArea)

RestArea(aAreaSB1)
RestArea(aAreaSBM)
RestArea(aAreaAOM)
RestArea(aArea)


Return cLinRec

/*/{Protheus.doc} FGetSupN
//Verifica se possui Suporte Ativo no período
<AUTHOR>
@since 27/10/2016
@version undefined
@param cAliasId, characters, descricao
@param cCodVU, characters, descricao
@param dEmissaoNF, date, descricao
@type function
/*/
User Function FGetSupN(cAliasId,cCodVU,dEmissaoNF)
Local aRet	:= {.F.,""}
Local aArea	:= GetArea()
Local cAliasG	:= GetNextAlias()
Local cQuery	:= ""

cQuery := " SELECT PLL.PLL_CODIGO, PLL.PLL_DTINI, PLL.PLL_DTFIM "
cQuery += " FROM " + RetSqlName("PLL") + " PLL "
cQuery += " WHERE PLL.PLL_FILIAL = '" + xFilial("PLL") + "' "
cQuery += " AND PLL.PLL_ALIAS = '" + cAliasId + "' "
cQuery += " AND PLL.PLL_CODIGO = '" + cCodVU + "' "
cQuery += " AND " +ValToSql(dEmissaoNF) + " BETWEEN PLL.PLL_DTINI AND PLL.PLL_DTFIM "
cQuery += " AND PLL.D_E_L_E_T_ = ' ' "

TCQUERY cQuery NEW ALIAS (cAliasG)

While !(cAliasG)->(Eof())
	
	 aRet[1] := .T.
	 aRet[2] := "Período ativo De: "+(cAliasG)->PLL_DTINI+" Ate: "+(cAliasG)->PLL_DTFIM +" ."
	
	Exit
	
	(cAliasG)->(DbSkip())
			
EndDo

(cAliasG)->(dbCloseArea())

RestArea(aArea)

Return aRet
/*/{Protheus.doc} FGetSupN
//Busca Unidades Vinculadas
<AUTHOR>
@since 27/10/2016
@version undefined
@param cUnid, characters, descricao
@type function
/*/
User Function FUndVinc(cUnid)
Local aRet		:= {}
Local aArea		:= GetArea()
Local cAliasG	:= GetNextAlias()
Local cQuery	:= ""

cQuery := " SELECT  ADK1.ADK_COD  FROM "+RetSqlName("ADK")+" ADK1 "
cQuery += " WHERE ADK1.ADK_FILIAL= '"+xFilial("ADK")+"' " 
cQuery += " AND ADK1.ADK_XSUPER = '" +cUnid+ "'"
cQuery += " AND ADK1.D_E_L_E_T_=' '"

cQuery := ChangeQuery(cQuery)

TCQUERY cQuery NEW ALIAS (cAliasG)

While !(cAliasG)->(Eof())
	
	 Aadd(aRet,AllTrim((cAliasG)->ADK_COD))
	
	(cAliasG)->(DbSkip())
			
EndDo

(cAliasG)->(dbCloseArea())

RestArea(aArea)

Return aRet

/*/{Protheus.doc} GetCmsEmp
//Retorna o campo chave da ZX5 concatenado  
<AUTHOR>
@since 05/01/2018
@version undefined

@type function
/*/
User Function GetCmsEmp(cTab,cSepara)
Local cRet		:= ""
Local aAreaSX5	:= SX5->(GetArea())
Local aX5Stru	:= {}
Local _nX

dbSelectArea("SX5")

SX5->(DbSetOrder(1))
SX5->(DBSeek(xFilial("SX5")+cTab))
aX5Stru:= FWGetSX5(AllTrim(cTab))
For _nX:= 1 To Len(aX5Stru)
		cRet += AllTrim(aX5Stru[_nX][4])+cSepara
Next

RestArea(aAreaSX5)
Return(cRet)



/*/{Protheus.doc} BuscaGc
//Retorna o gc de um determinado produto pela vigência  
<AUTHOR>
@since 05/01/2018
@version undefined

@type function
/*/
User Function BuscaGc( cProd )
Local aPZ5   := {}
Local cQuery := ""
LOCAL cTRB := "T_PZ5"

Default cProd := ""

cQuery := " SELECT PZ5_GCOMIS, PZ5_DEV " 
cQuery += " FROM " + RetSqlName("PZ5") + " WHERE PZ5_FILIAL = '"+xFilial('PZ5') + "'" 
cQuery += " AND PZ5_PROD = '" + cProd + "' 
cQuery += " AND '" + Dtos(SD2->D2_EMISSAO) + "' BETWEEN PZ5_DVIGDE AND PZ5_DVIGAT "
cQuery += " AND PZ5_MSBLQL = '2' "
cQuery += " AND D_E_L_E_T_ = ' ' " 
DbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQuery), cTRB , .F., .T.)

If !Empty( (cTRB)->PZ5_GCOMIS )
    aAdd( aPZ5, (cTRB)->PZ5_GCOMIS )  
    aAdd( aPZ5, (cTRB)->PZ5_DEV    )
EndIf

(cTRB)->(DBCLOSEAREA())

Return aPZ5
