# Documentação - Funções que utilizam RPCSetEnv e/ou alteram variáveis de sistema

## Descrição
Este documento lista todas as funções encontradas na pasta `adm_vendas\Contratos` e suas subpastas que:
- Utilizam a função **RPCSetEnv** ou **RpcSetEnv**
- Alteram o valor da variável **cEmpAnt**
- Alteram o valor da variável **__cUserId**

---

## 1. FUNÇÕES QUE UTILIZAM RPCSetEnv

### 1.1 Corporativo\Atualizações\TGCVA022.prw
- **Linha 18**: `RpcSetEnv( '00', '00001000100')`

### 1.2 Corporativo\Job\TGCVJ001.prw
- **<PERSON>ha 245**: `_lContinua := RpcSetEnv('00',"00001000100")`
- **Linha 880**: `If !RpcSetEnv(clEmp,clFil)`

### 1.3 Corporativo\WebService\TGCVS001.prw
- **Linha 1792**: `RpcSetEnv( cEmpAtu, cFilAtu )`
- **Linha 1806**: `RpcSetEnv( cEmpAtu, cFilAtu )`

### 1.4 IntegracaoPSA\TGCPSA03.PRW
- **Linha 37**: `RpcSetEnv(cEmp, cFil,,,,,)`

### 1.5 MultThread\ti.multthread.tlpp
- **Linha 639**: `RpcSetEnv(::cEmp, ::cFil)`
- **Linha 682**: `RpcSetEnv(cEmpBkp, cFilBkp)`
- **Linha 1070**: `RpcSetEnv(cEmp, cFil)`

### 1.6 Novo Intera\TGCIIA06.PRW
- **Linha 1555**: `RpcSetEnv(cEmp, cFil)`
- **Linha 2525**: `RpcSetEnv(cEmp, cFil)`
- **Linha 2691**: `RpcSetEnv(cEmp,cFil, , ,'FAT',,)`
- **Linha 2873**: `RpcSetEnv(cEmp, cFil)`

### 1.7 Novo Intera\TGCIIA12.PRW
- **Linha 485**: `RpcSetEnv("00", "00001000100")`

### 1.8 Novo Intera\TGCIIA13.PRW
- **Linha 532**: `RpcSetEnv("00", "00001000100")`

### 1.9 Novo Intera\TGCIIA14.PRW
- **Linha 20**: `RpcSetEnv(cEmp, cFil,,,,,)`

### 1.10 Novo Intera\TGCIIA15.PRW
- **Linha 489**: `RpcSetEnv("00", "00001000100")`

### 1.11 Novo Intera\TGCIIA16.PRW
- **Linha 55**: `RpcSetEnv("00","00001000100")`

### 1.12 Novo Intera\TGCIIIPQA.PRW
- **Linha 31**: `RpcSetEnv("00","00001000100")`

### 1.13 Novo Intera\TGCIIWF1.PRW
- **Linha 23**: `RpcSetEnv("00", "00001000100")`
- **Linha 219**: `RpcSetEnv(cEmp,cFil,,,,,)`
- **Linha 621**: `RpcSetEnv("00", "00001000100")`

### 1.14 Novo Intera\TGCIIWF2.PRW
- **Linha 188**: `RpcSetEnv(cEmp,cFil,,,,,)`

### 1.15 Novo Intera\TGCIIWF4.prw
- **Linha 56**: `RpcSetEnv(cEmp,cFil,,,,,)`

### 1.16 Webservice\TGCTS012.PRW
- **Linha 41**: `RpcSetEnv(_cEmp,_cFil,,,,,)`

### 1.17 TGCVA004.prw
- **Linha 113**: `If !RpcSetEnv(cEmpJob, cFilJob, NIL, NIL, "GCT", NIL, aTabelas)`
- **Linha 733**: `If !RpcSetEnv(aParam[2], aParam[3])`

### 1.18 TGCVA037.PRW
- **Linha 34**: `If !RpcSetEnv(clEmp,clFil)`
- **Linha 221**: `If !RpcSetEnv(clEmp,clFil)`
- **Linha 430**: `If !RpcSetEnv(clEmp,clFil)`

### 1.19 TGCVA041.PRW
- **Linha 83**: `If !RpcSetEnv(clEmp,clFil)`
- **Linha 928**: `If !RpcSetEnv(clEmp,clFil)`

### 1.20 TGCVA045.PRW
- **Linha 80**: `If !RpcSetEnv(clEmp,clFil)`

### 1.21 TGCVA078.PRW
- **Linha 262**: `RpcSetEnv(cEmpPrc, cFilPrc)`

### 1.22 TGCVA085.PRW
- **Linha 63**: `RpcSetEnv(aParam[1],aParam[2])`

### 1.23 TGCVA088.PRW
- **Linha 2440**: `RpcSetEnv("00", "00001000100")`

### 1.24 TGCVA102.PRW
- **Linha 77**: `RpcSetEnv(cEmpresa, cFilEmp)`
- **Linha 842**: `RpcSetEnv("00", "00001000100")`
- **Linha 918**: `RpcSetEnv("00", "00001000100")`

### 1.25 TGCVA108.prw
- **Linha 63**: `If !RpcSetEnv( "00", _cFilial )`

### 1.26 TGCVA109.prw
- **Linha 42**: `RpcSetEnv( "00", "00001000100" )`
- **Linha 129**: `_lOpen := RpcSetEnv( "00", _cFilial )`

### 1.27 TGCVA110.prw
- **Linha 26**: `If RpcSetEnv( "00", "00001000100" )`

### 1.28 TGCVA121.PRW
- **Linha 1284**: `RpcSetEnv("00", "00001000100")`
- **Linha 1566**: `RpcSetEnv("00", "00001000100")`
- **Linha 1607**: `RpcSetEnv(cEmpProc, cFil)`

### 1.29 TGCVA123.PRW
- **Linha 124**: `If !RpcSetEnv(cEmpAnt,cFilAnt, , , "GCT", , {"CN9","CNB","CXL","PH5","PH6","PH7"})`
- **Linha 284**: `RpcSetEnv(pEmpAnt,pFilAnt, , , "GCT", , {"CN9","CNB","CXL","PH5","PH6","PH7"})`

### 1.30 TGCVA127.PRW
- **Linha 557**: `RpcSetEnv("00","00001000100")`
- **Linha 887**: `RpcSetEnv('00', '00001000100')`

### 1.31 TGCVA137.prw
- **Linha 759**: `RpcSetEnv(cEmp, cFil)`

### 1.32 TGCVA141.PRW
- **Linha 513**: `RpcSetEnv( SM0->M0_CODIGO, SM0->M0_CODFIL )`

### 1.33 TGCVA144.PRW
- **Linha 1042**: `RpcSetEnv("00", "00001000100")`

### 1.34 TGCVA147.PRW
- **Linha 491**: `lRet := RpcSetEnv(cEmpProc, cFil)`

### 1.35 TGCVA197.PRW
- **Linha 2274**: `RpcSetEnv(_cEmp,_cFil,,,,,)`
- **Linha 2341**: `RpcSetEnv(_cEmp,_cFil,,,,,)`

### 1.36 TGCVA264.PRW
- **Linha 719**: `RpcSetEnv(cEmpresa,cFilEmp)`

### 1.37 TGCVJ000.PRW
- **Linha 165**: `If !RpcSetEnv('00', '00001000100')`

### 1.38 TGCVJ003.PRW
- **Linha 62**: `If !RpcSetEnv(clEmp,clFil)`

### 1.39 TGCVJ004.PRW
- **Linha 51**: `If !RpcSetEnv(clEmp,clFil)`

### 1.40 TGCVJ006.PRW
- **Linha 143**: `If !RpcSetEnv(cEmpJob, cFilJob, NIL, NIL, "GCT", NIL, aTabelas)`

### 1.41 TGCVJ007.PRW
- **Linha 50**: `If !RpcSetEnv(clEmp,clFil)`

### 1.42 TGCVJ008.PRW
- **Linha 50**: `If !RpcSetEnv(clEmp,clFil)`

### 1.43 TGCVJ009.PRW
- **Linha 48**: `If !RpcSetEnv(clEmp,clFil)`

### 1.44 TGCVJ010.PRW
- **Linha 59**: `If !RpcSetEnv(clEmp,clFil)`

### 1.45 TGCVJ011.PRW
- **Linha 59**: `If !RpcSetEnv(clEmp,clFil)`

### 1.46 TGCVJ012.PRW
- **Linha 42**: `If !RpcSetEnv(clEmp,clFil)`

### 1.47 TGCVJ013.PRW
- **Linha 60**: `If !RpcSetEnv(clEmp,clFil)`

### 1.48 TGCVJ014.PRW
- **Linha 70**: `If !RpcSetEnv(clEmp,clFil)`

### 1.49 TGCVJ015.prw
- **Linha 33**: `If !RpcSetEnv( __cEmp, __cFil )`

### 1.50 TGCVJ017.PRW
- **Linha 53**: `If !RpcSetEnv(clEmp,clFil)`

### 1.51 TGCVJ018.PRW
- **Linha 45**: `If !RpcSetEnv(aParams[01],aParams[02])`
- **Linha 437**: `If !RpcSetEnv(aParm[1], aParm[2])`

### 1.52 TGCVJ019.PRW
- **Linha 45**: `If !RpcSetEnv(aParams[01],aParams[02])`
- **Linha 479**: `If !RpcSetEnv(aParm[1], aParm[2])`

### 1.53 TGCVJ023.PRW
- **Linha 32**: `If !RpcSetEnv(clEmp,clFil)`

### 1.54 TGCVJ025.PRW
- **Linha 28**: `RpcSetEnv(_cEmp,_cFil,,,,,)`

### 1.55 TGCVJ027.PRW
- **Linha 36**: `RpcSetEnv(_cEmp,_cFil,,,,,)`

### 1.56 TGCVJ029.prw
- **Linha 42**: `RpcSetEnv(_cEmp,_cFil,,,,,)`

### 1.57 TGCVJ030.prw
- **Linha 25**: `If !RpcSetEnv(clEmp,clFil)`

### 1.58 TGCVJ031.PRW
- **Linha 156**: `RpcSetEnv(cEmpProc, cFil)`

### 1.59 TGCVJ032.PRW
- **Linha 19**: `RpcSetEnv( '00', '00001000100')`
- **Linha 45**: `RpcSetEnv( '00', '00001000100')`
- **Linha 70**: `RpcSetEnv( '00', '00001000100')`

### 1.60 TGCVJ20.PRW
- **Linha 76**: `If !RpcSetEnv(clEmp,clFil)`

### 1.61 TGCVJ21.PRW
- **Linha 149**: `RpcSetEnv( SM0->M0_CODIGO, SM0->M0_CODFIL )`

### 1.62 TGCVJ22.PRW
- **Linha 149**: `RpcSetEnv( SM0->M0_CODIGO, SM0->M0_CODFIL )`

### 1.63 TGCVMJOB.prw
- **Linha 366**: `RpcSetEnv(::cEmpExe,::cFilExe, , ,'FAT',,)`
- **Linha 1732**: `RpcSetEnv (cCodEmp, cFilEmp,,,'FAT',,)`
- **Linha 1862**: `RpcSetEnv(cEmpx,cFilx, , ,'FAT',,)`

### 1.64 TGCVR031.prw
- **Linha 31**: `RpcSetEnv( "00", "00001000100" )`

### 1.65 TGCVR032.prw
- **Linha 20**: `RpcSetEnv( "00", "00001000100" )`

### 1.66 TGCVR037.PRW
- **Linha 406**: `RpcSetEnv(cEmp, cFil)`

### 1.67 TGCVXC34.TLPP
- **Linha 1041**: `RpcSetEnv(cEmpPar, cFilPar)`

### 1.68 TGCVXFUN.PRW
- **Linha 1729**: `RpcSetEnv( uEmp, uFil )`
- **Linha 1737**: `RpcSetEnv( ___xEmpOld, ___xFilOld )`
- **Linha 2988**: `If !RpcSetEnv( cEmpGr , cFilGr )`
- **Linha 3627**: `If !RpcSetEnv(clEmp,clFil)`

### 1.69 TGCVXJOB.prw
- **Linha 101**: `RpcSetEnv(cEmp, cFil )`
- **Linha 976**: `RpcSetEnv(cEmp, cFil)`

---

## 2. FUNÇÕES QUE ALTERAM A VARIÁVEL cEmpAnt

### 2.1 MultThread\ti.multthread.tlpp
- **Linha 627**: `Default cEmpAnt := ""`

### 2.2 TGCVA123.PRW
- **Linha 98**: `Local cEmpAnt   :="00"`
- **Linha 118**: `cEmpAnt   :=aParam[1]`

### 2.3 TGCVMJOB.prw
- **Linha 353**: `cEmpAnt := ""`
- **Linha 364**: `cEmpAnt:= ::cEmpExe`

### 2.4 TGCVXJOB.prw
- **Linha 93**: `cEmpAnt := ""`

---

## 3. FUNÇÕES QUE ALTERAM A VARIÁVEL __cUserId

### 3.1 AjusteFinanceiro\gct.ajustefinanceiro.tlpp
- **Linha 90**: `__cUserID  := cUserId`

### 3.2 Corporativo\Atualizações\TGCVA008.prw
- **Linha 506**: `__cUserId := _cCodUsr`

### 3.3 Novo Intera\TGCIIA12.PRW
- **Linha 892**: `__cUserId := cUserId`

### 3.4 Novo Intera\TGCIIA13.PRW
- **Linha 960**: `__cUserId := cUserId`

### 3.5 Novo Intera\TGCIIA15.PRW
- **Linha 735**: `__cUserId := cUserId`

### 3.6 TGCVA123.PRW
- **Linha 35**: `Local __cUserId :="000000"`
- **Linha 100**: `Local __cUserId :="000000"`
- **Linha 120**: `__cUserId :=aParam[3]`
- **Linha 285**: `__cUserId:=p__cUserId`

### 3.7 TGCVA132.prw
- **Linha 1679**: `__cUserId := aPar[10]`

### 3.8 TGCVA144.PRW
- **Linha 1684**: `__cUserId := cUserId`

### 3.9 TGCVA146.PRW
- **Linha 1493**: `__cUserId := cUserId`

### 3.10 TGCVJ012.PRW
- **Linha 444**: `__cUserId := cUserProc`

### 3.11 TGCVXFUN.PRW
- **Linha 2881**: `__CUSERID := ""`
- **Linha 3686**: `__cUserId := cUserProc`
- **Linha 3987**: `__cUserId := cUserProc`

---

## 4. RESUMO ESTATÍSTICO

- **Total de arquivos com RPCSetEnv**: 69 arquivos
- **Total de ocorrências de RPCSetEnv**: 95 ocorrências
- **Total de arquivos que alteram cEmpAnt**: 4 arquivos
- **Total de arquivos que alteram __cUserId**: 11 arquivos

---

## 5. OBSERVAÇÕES IMPORTANTES

1. **Jobs**: Grande quantidade de jobs (TGCVJ*) que necessitam configurar ambiente via RpcSetEnv
2. **Ambiente Padrão**: Maioria usa empresa "00" e filial "00001000100"
3. **Módulos**: Muitas chamadas especificam módulo "GCT" ou "FAT"
4. **Segurança**: Várias funções alteram __cUserId para valores específicos ou genéricos
5. **Integração**: Muitas funções relacionadas a integrações (PSA, Intera, WebServices)

---

**Data de criação**: 05/08/2025  
**Responsável**: Documentação automática via análise de código
