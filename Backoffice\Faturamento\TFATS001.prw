#INCLUDE "ap5mail.ch"

#INCLUDE "apwebsrv.ch"
#INCLUDE "fileio.ch"
#INCLUDE "protheus.ch"
#INCLUDE "sigawin.ch"
#INCLUDE "tbiconn.ch"
#INCLUDE "topconn.ch"

static cMaskPfsp   := ''
static cMaskPfre   := ''
static cMaskPfjv   := ''
static _cMaringMsk := ''
static cMaskPfFL   := ''
static cMaskPfbh   := ''
static cMaskPfrj   := ''
static cMaskPfba   := ''
static cMaskPfcp   := ''
static cMaskPfWS   := ''
static cMPfCaxias  := ''
static cMPfAssis   := ''
static _cMaskPfMa  := ''
static _cMaskPfDf  := ''
static cMaskPfPa   := ''
static _cMaskGoias := ''
static _cMaskCuri  := ''
static _cMaskRPrt  := '' 
static _cUrlLimei  := ''
static cDtbarue    := ''
static cPrefRecif  := ''
static cPrefSC     := ''
static cPrefFL     := ''
static cPrefBH     := ''
static cPrefRJ     := ''
static cPrefCam    := ''
static cPrefWealth := ''
static cPrefAlp    := ''
static lUsaAlpSP   := ''
static cPrefAlp1   := ''
static cPrefMac    := ''
static cPrefDF     := ''
static cPrefPOA    := ''
static cPrefASS    := ''
static _cPrefPCGoi := ''
static _cPrefPCBh  := ''
static _cPrefCian  := ''
static cMaringa    := ''
static cPrefSSABA  := ''
static cPrefCurit  := ''
static cPrefRibPre := ''
static cPrefDimSP  := ''
static cPrefCAX	   := ''
static _cPrefLime  := '' 


User Function _AUTOATENDIMENTO ; Return  // "dummy" function - Internal Use
/*/
	ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
	±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
	±±ÉÍÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
	±±º Autor     ³ F.Alves Projetos e Sistemas                               º±±
	±±º           ³ SI2901 - Cleyton Ferreira Alves                           º±±
	±±º           ³ www.falves.com                                            º±±
	±±ÌÍÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
	±±º Programa  ³ AutoAtendimento                   º  Data  ³   25/10/10   º±±
	±±ÌÍÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
	±±º Descricao ³ WSDL Servide WSAUTOATENDIMENTO                            º±±
	±±º           ³ WebService do protal de autoatendimento da Totvs.         º±±
	±±ÌÍÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
	±±º Uso       ³ TDI - Totvs                                               º±±
	±±ÈÍÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
	±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
	ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
/*/
	WsService AUTOATENDIMENTO description "Portal de auto-atendimento."

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³   - Variaveis comuns                  ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData c_empresa 	     As String
		WsData c_filial  		 As String
		WsData c_cliente 		 As String
		WsData c_loja	   		 As String
		WsData c_datade 		 As Date
		WsData c_dataate 		 As Date
		WsData c_CGCforn   	 As String
		WsData c_titulo  		 As String
		WsData c_unidade 		 As String

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ a - Variaveis metodo FLAG_LOGIN ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData ca_situacao		 As String

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ b - Variaveis metodo CONSULTA_TITULOS ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData cb_de_titulo		As String
		WsData cb_ate_titulo	 	As String
		WsData cb_de_emissao	 	As String
		WsData cb_ate_emissao	 	As String
		WsData cb_de_vencimento	As String
		WsData cb_ate_vencimento As String
		WsData cb_posicao		 	As String
		WsData ab_titulos		 	As Array of listatitulos
		Wsdata ab_titulo2	 	 	As Array of listtitulos

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ c - Variaveis metodo CONSULTA_NFISCAL ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData cc_de_nfiscal	 As String
		WsData cc_ate_nfiscal As String
		WsData cc_de_emissao	 As String
		WsData cc_ate_emissao	 As String
		WsData cc_de_nfEletr	 As String
		WsData cc_ate_nfEletr	 As String
		WsData ac_nfiscal 	 As Array of listanotasfiscais

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ d - Variaveis metodo RE_IMPRESSAO     ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData cd_documento		 As String
		WsData cd_serie	   		 As String
		WsData cd_impnfiscal	     As String

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ e - Variaveis metodo DADOS_EXECUTIVO  ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData res_executivo	 As listaexecutivos

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ f - Variaveis metodo RE_IMP_BOLETO    ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData nf_recno			 As Integer
		WsData cf_link           As String

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ g - Variaveis metodo DADOS_CADASTRAIS ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData ag_cadastrais	 As Array of listacadastrais

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ h - Variaveis metodo GRAVA_DADOS      ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData ch_resultado		 As String
		WsData res_cadastrais	 As gravadados


		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ i - Variaveis metodo CONSULTA_CONTRATOS ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData ai_contratos	   	As Array of listacontratos
		WsData n_portal			As Integer
		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ q - Variaveis metodo CONSULTA_CONTRATOS_DATA ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ


		WsData aq_contratos	     As Array of listacontratosData

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ i - Variaveis metodo CONTAS_PAGAR		  ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData aj_pagtos	     As Array of listapagtos
		WsData cj_vencde 		 As String
		WsData cj_vencate		 As String

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ J - Variaveis metodo PROX_VENCTO		|
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

		WsData aj_Titulo		 As String


		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ L - Variaveis metodo STAT_VIAGEM	  |
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

		WsData cl_login			 As String
		WsData cl_prest			 As String
		WsData a_stviagem		     As  stviag

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ M - Variaveis metodo EMP_FILIAL		  |
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

		WsData cm_marca			 As String
		WsData am_empfil		 As Array of empfil

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ N - Variaveis metodo CONF_FORNEC	  |
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

		WsData cn_cnpj			 As String
		WsData ln_exist			 As Boolean

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ O - Variaveis metodo CONS_CABECALHO   ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData co_de_nfiscal	 As String
		WsData co_ate_nfiscal	 As String
		WsData co_de_emissao	 As String
		WsData co_ate_emissao	 As String
		WsData co_de_nfEletr	 As String
		WsData co_ate_nfEletr	 As String
		WsData o_cabec 			 As Array of o_cabecalho
		WsData o_Cloud_tit 		 As Array of o_CloudTitulos


		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ P - Variaveis metodo CONS_DETALHE	  ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

		WsData ap_detalhe		 	As Array of listadetalhe

		//ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Ä¿
		//ï¿½ P - Variaveis metodo CONS_CONT_COLIG	  ï¿½
		//ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½

		WsData atc_cColig		 	As Array of listacontColig



		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ R - Variaveis metodo CONS_CLOUD_CABEC ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		WsData Cloud_de_nfiscal	 	As String
		WsData Cloud_ate_nfiscal	As String
		WsData Cloud_de_emissao	 	As String
		WsData Cloud_ate_emissao	As String
		WsData Cloud_de_nfEletr	 	As String
		WsData Cloud_ate_nfEletr	As String
		WsData o_Cloud_cabec	 	As Array of o_Cloudcabecalho

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ S - Variaveis do metodo CONS_CLIPCSIST    ³
		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//WsData c_Empresa		As String
		//WsData c_Filial			As String
		WsData c_ClientePC		As String
		WsData o_RetCliTotvs	As Array Of o_RetCli

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ T - Variaveis do metodo CONS_TITABERTOS   ³
		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//WsData c_Empresa		As String
		//WsData c_Filial			As String
		//WsData c_Cliente		As String
		//WsData c_Loja			As String
		WsData o_RetTitAb		As Array Of o_RetTit

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ U - Variaveis do metodo CONS_GETXML       ³
		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//WsData c_Empresa		As String
		//WsData c_Filial			As String
		WsData c_Entidade		As String
		WsData c_SerieNota		As String
		WsData o_RetXml			As Array Of o_XMLPROT

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ V - Variaveis metodo CONS_PCCABEC     ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		//WsData co_de_nfiscal		As String
		//WsData co_ate_nfiscal		As String
		//WsData co_de_emissao		As String
		//WsData co_ate_emissao		As String
		//WsData co_de_nfEletr		As String
		//WsData co_ate_nfEletr		As String
		WsData co_de_CodNfe			As String
		WsData co_ate_CodNfe		As String
		WsData o_pccabec			As Array of o_pccabecalho

		//ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Ä¿
		//ï¿½ Metodos Publicados no WS              ï¿½
		//ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
		WsMethod FLAG_LOGIN
		WsMethod CONSULTA_TITULOS
		WsMethod CONSULTA_NFISCAL
		WsMethod RE_IMPRESSAO
		WsMethod DADOS_EXECUTIVO
		WsMethod RE_IMP_BOLETO
		WsMethod DADOS_CADASTRAIS
		WsMethod GRAVA_DADOS
		WsMethod CONSULTA_CONTRATOS
		WsMethod CONSULTA_CONTRATOS_DATA

		WsMethod CONS_PENDENCIAS  	DESCRIPTION "RETORNA RELAÇÃO DE TITULOS PENDENTES PARA DETERMINADO CLIENTE"
		WsMethod PROX_VENCTO	 	DESCRIPTION "RETORNA PRIMEIRO VENCIMENTO DIA UTIL DO CLIENTE "
		WsMethod CONTAS_PAGAR	 	DESCRIPTION "RETORNA RELACAO DE PAGAMENTOS A UM DETERMINADO FORNECEDOR "
		WsMethod CONS_CABECALHO	 	DESCRIPTION "RETORNA CABECALHO PARA CONSULTA DE NOTA "
		WsMethod CONS_DETALHE	 	DESCRIPTION "RETORNA DETALHE PARA CONSULTA DE NOTA "
		WsMethod STAT_VIAGEM		DESCRIPTION "ALTERA STATUS PRESTACAO DE CONTAS DE VIAGENS"
		WsMethod EMP_FILIAL			DESCRIPTION "RETORNA RELACAO DE EMPRESAS FILIAIS"
		WsMethod CONF_FORNEC		DESCRIPTION "VALIDA SE FORNECEDOR EXISTE EM ALGUMA DAS EMPRESAS"
		WsMethod CONS_CONT_COLIG    DESCRIPTION "CONSULTA CONTRATO E COLIGADAS VINGULADOS A PARTIR DA PROPOSTA"
		WsMethod CONS_CLOUD_CABEC	DESCRIPTION "RETORNA CABECALHO PARA CONSULTA DE NOTA FISLCAL CLOUD"
		WsMethod CONS_CLOUD_PENDE  	DESCRIPTION "RETORNA RELAÇÃO DE TITULOS PENDENTES PARA DETERMINADO CLIENTE DE CLOUD"
		WsMethod CONS_CLIPCSIST		DESCRIPTION "RETORNA CLIENTE TOTVS E SUA RESPECTIVA LOJA A PARTIR DO DE/PARA PC SISTEMAS"
		WsMethod CONS_TITABERTOS	DESCRIPTION "TITULOS EM ABERTO NO FINANCEIRO (SERÃO LISTADOS NO PORTAL FINANCEIRO PC SISTEMAS)"
		WsMethod CONS_GETXML		DESCRIPTION "CAPTURA XML DE NOTAS DE ACORDO COM PARAMETRO ENVIADO"
		WsMethod CONS_PCCABEC	 	DESCRIPTION "PC SISTEMAS - RETORNA CABECALHO PARA CONSULTA DE NOTA "

	EndWsService

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ b - Estrutura metodo CONSULTA_TITULOS ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct listatitulos
		WsData cb_empresa	    	 As String
		WsData cb_filial	     	 As String
		WsData cb_unidade	     	 As String
		WsData cb_vencimento    	 As String
		WsData cb_nome		     	 As String
		WsData cb_prefixo	    	 As String
		WsData cb_numero	    	 As String
		WsData cb_parcela	    	 As String
		WsData cb_tipo		    	 As String
		WsData cb_emissao	    	 As String
		WsData cb_prorrogacao	     As String
		WsData cb_original_valor	 As String
		WsData cb_baixa_valor	     As String
		WsData cb_ir		    	 As String
		WsData cb_pis		    	 As String
		WsData cb_cofins	    	 As String
		WsData cb_csll		    	 As String
		WsData cb_inss		    	 As String
		WsData cb_nfe		     	 As String
		WsData cb_observacao    	 As String
		WsData cb_empori	      	 As String
		WsData cb_filori		     As String
		WsData cb_cnpj		   	     As String
		WsData cb_nomecom	   	     As String
		WsData cb_descServ		 As String
		WsData cb_outInfo			 As String
		WsData cb_totDedu			 As String
		WsData nb_recno 	   	     As Integer

		WsData cb_rpsEmp			As String
		WsData cb_rpsEnd			As String
		WsData cb_rpsBai			As String
		WsData cb_rpsCid			As String
		WsData cb_rpsUf			As String
		WsData cb_rpsCep			As String
		WsData cb_rpsTel			As String
		WsData cb_rpsCgc			As String
		WsData cb_rpsInc			As String

	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ c - Estrutura metodo CONSULTA_NFISCAL ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct listanotasfiscais
		WsData cc_empresa	     	As String
		WsData cc_filial	     	As String
		WsData cc_unidade	     	As String
		WsData cc_cliente	     	As String
		WsData cc_cond_pag	    As String
		WsData cc_documento	   	As String
		WsData cc_serie		   	As String
		WsData cc_nf_eletronica  As String
		WsData cc_codigo_nfe	    As String
		WsData cc_emissao	   	    As String
		WsData cc_descricaonf   	As String
		WsData cc_emisnfe			As String
		WsData ac_itens_nfiscal	As Array of itens_nfiscal
		WsData ac_nf_imp_acum		As Array of nf_imp_acum
	EndWsStruct

	WsStruct itens_nfiscal
		WsData cc_produto	 		As String
		WsData cc_descricao			As String
		WsData cc_preco_unitario	As String
		WsData cc_quantidade 		As String
		WsData cc_total	 			As String
		WsData cc_unidademedida 	As String
		WsData cc_aliquota_iss	 	As String
		WsData cc_grupo_tributario	As String

	EndWsStruct

	WsStruct nf_imp_acum
		WsData cc_prefixo	 		As String OPTIONAL
		WsData cc_numero			As String OPTIONAL
		WsData cc_emissao			As String OPTIONAL
		WsData cc_vencimento 		As String OPTIONAL
		WsData cc_valor	 		As String OPTIONAL
		WsData cc_empresatitulo 	As String OPTIONAL
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ e - Estrutura metodo DADOS_EXECUTIVO  ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct listaexecutivos
		WsData ce_codigo   	    As String
		WsData ce_nome	   	     	As String
		WsData ce_endereco 	    As String
		WsData ce_bairro   	    As String
		WsData ce_municipio	    As String
		WsData ce_cep	            As String
		WsData ce_cgc   	    	As String
		WsData ce_telefone	    As String
		WsData ce_email			As String
		WsData ce_ddd	     		As String
		WsData ce_celular   		As String
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ g - Estrutura metodo DADOS_CADASTRAIS ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct listacadastrais
		WsData cg_campo   	     	As String
		WsData cg_titulo   	     	As String
		WsData cg_tipo  	     		As String
		WsData ng_tamanho  	     	As Integer
		WsData ng_decimais	    	As Integer
		WsData cg_conteudo	        As String
		WsData cg_folder   	     	As String
		WsData cg_flag   	     		As String
		WsData cg_combobox	    	As String
		WsData cg_permissao	    	As String
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ h - Estrutura metodo GRAVA_DADOS      ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct gravadados
		WsData ah_dadosalterados As Array Of estrutura
	EndWsStruct

	WsStruct estrutura
		WsData ch_campo   	     	As String
		WsData ch_titulo   	    	As String
		WsData ch_tipo  	     		As String
		WsData nh_tamanho  	     	As Integer
		WsData nh_decimais	    	As Integer
		WsData ch_conteudo	        As String
		WsData ch_folder   	     	As String
		WsData ch_flag   	    		As String
		WsData ch_combobox	    	As String
		WsData ch_permissao	    	As String
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ i - Estrutura metodo CONSULTA_CONTRATOS ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct listacontratos
		WsData ci_empresa  			As String
		WsData ci_filial  			As String
		WsData ci_unidade  			As String
		WsData ci_numero   			As String
		WsData ci_versao  			As String
		WsData ci_cliente 			As String
		WsData ci_loja 				As String
		WsData ai_itens_contrato   	As Array of itens_contrato
	EndWsStruct

	WsStruct itens_contrato
		WsData ci_item 				As String
		WsData ci_produto				As String
		WsData ci_quantidade   		As String
		WsData ci_valorunitario		As String
		WsData ci_vencimento   		As String
		WsData ci_dtareajuste  		As String
		WsData ci_condicaopagamento	As String
		WsData ci_sitauacao			As String
		WsData ci_dataassinatura		As String
		WsData ci_imposto				As String
		WsData ci_indice   			As String
		WsData ci_tes					As String
		WsData ci_startrm				As String
		WsData ci_unidademedida		As String
		WsData ci_notasep				As String
		WsData ci_proposta			As String
		WsData ci_versaoproposta		As String
		WsData ci_origem   			As String
		WsData ci_grupo				As String
		WsData ci_linha				As String
		WsData ni_recno				As Integer
		WsData ci_reajuste			As String
		WsData ci_mesreaj				As String
		WsData ci_diavencto			As String
		WsData ci_valtotal			As String
		WsData ci_formaPgto			As String
		WsData ci_numCartao			As String
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ Q - Estrutura metodo CONSULTA_CONTRADATA ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct listacontratosdata
		WsData cq_empresa  				As String
		WsData cq_filial  				As String
		WsData cq_unidade  				As String
		WsData cq_numero   				As String
		WsData cq_versao  				As String
		WsData cq_cliente 				As String
		WsData cq_loja 					As String
		WsData aq_itens_contrato   		As Array of itens_contratodata
		WsData aq_itens_contant		As Array of itens_contrdataant
	EndWsStruct

	WsStruct itens_contratodata
		WsData cq_item 				As String
		WsData cq_produto				As String
		WsData cq_quantidade   		As String
		WsData cq_valorunitario		As String
		WsData cq_vencimento   		As String
		WsData cq_dtareajuste  		As String
		WsData cq_condicaopagamento	As String
		WsData cq_sitauacao			As String
		WsData cq_dataassinatura		As String
		WsData cq_imposto				As String
		WsData cq_indice   			As String
		WsData cq_tes					As String
		WsData cq_startrm				As String
		WsData cq_unidademedida		As String
		WsData cq_notasep				As String
		WsData cq_proposta			As String
		WsData cq_versaoproposta		As String
		WsData cq_origem   			As String
		WsData cq_grupo				As String
		WsData cq_linha				As String
		WsData nq_recno				As Integer
		WsData cq_reajuste			As String
		WsData cq_mesreaj				As String
		WsData cq_diavencto			As String
		WsData cq_valtotal			As String
		WsData cq_obsmotivo			As String
		WsData cq_codvend				As String
		WsData cq_nmvend				As String
	EndWsStruct

	WsStruct itens_contrdataant
		WsData cq_itemant 				As String
		WsData cq_produtoant			As String
		WsData cq_quantidadeant   		As String
		WsData cq_valorunitarioant		As String
		WsData cq_vencimentoant   		As String
		WsData cq_dtareajusteant  		As String
		WsData cq_condicaopagamentoant	As String
		WsData cq_sitauacaoant			As String
		WsData cq_dataassinaturaant	As String
		WsData cq_impostoant			As String
		WsData cq_indiceant   			As String
		WsData cq_tesant					As String
		WsData cq_startrmant			As String
		WsData cq_unidademedidaant		As String
		WsData cq_notasepant			As String
		WsData cq_propostaant			As String
		WsData cq_versaopropostaant	As String
		WsData cq_origemant   			As String
		WsData cq_grupoant				As String
		WsData cq_linhaant				As String
		WsData nq_recnoant				As Integer
		WsData cq_reajusteant			As String
		WsData cq_mesreajant			As String
		WsData cq_diavenctoant			As String
		WsData cq_valtotalant			As String
		WsData cq_obsmotivoant			As String
		WsData cq_codvendant			As String
		WsData cq_nmvendant				As String
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ b - Estrutura metodo CONS_PENDENCIAS  ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct listtitulos
		WsData cb_empresa	    	 As String
		WsData cb_filial	     	 As String
		WsData cb_unidade	     	 As String
		WsData cb_vencimento    	 As String
		WsData cb_nome		     	 As String
		WsData cb_prefixo	    	 As String
		WsData cb_numero	    	 As String
		WsData cb_parcela	    	 As String
		WsData cb_tipo		    	 As String
		WsData cb_emissao	    	 As String
		WsData cb_prorrogacao	     As String
		WsData cb_original_valor  As String
		WsData cb_baixa_valor	     As String
		WsData cb_ir		    	 As String
		WsData cb_pis		    	 As String
		WsData cb_cofins	    	 As String
		WsData cb_csll		    	 As String
		WsData cb_inss		    	 As String
		WsData cb_nfe		     	 As String
		WsData cb_observacao    	 As String
		WsData cb_empori	      	 As String
		WsData cb_filori		     As String
		WsData cb_cnpj		   	     As String
		WsData cb_supplier	   	     As String //Incluid Joao Gabriel
		WsData cb_nomecom	   	     As String
		WsData nb_recno 	   	     As Integer
	EndWsStruct


//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ j - Estrutura metodo CONTAS_PAGAR ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct listapagtos
		WsData cj_empresa  			As String
		WsData cj_filial  			As String
		WsData cj_codfornec			As String
		WsData cj_descrforn			As String
		WsData aj_itens_pagto   	As Array of itens_pagto
	EndWsStruct

	WsStruct itens_pagto
		WsData cj_prfnro 			As String
		WsData cj_pctip			As String
		WsData cj_valorig   		As String
		WsData cj_emissao			As String
		WsData cj_vencto   		As String
		WsData cj_baixa  			As String
		WsData cj_lei10925		As String
		WsData cj_descontos		As String
		WsData cj_abatimentos		As String
		WsData cj_juros			As String
		WsData cj_multa   		As String
		WsData cj_corrmonet		As String
		WsData cj_valpago			As String
		WsData cj_pagtoantec		As String
		WsData cj_saldoatu		As String
	EndWsStruct



//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ L - Estrutura metodo STAT_VIAGEM  ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct stviag
		WsData cl_existe		 As Boolean
		WsData cl_alterado		 As Boolean
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ M - Estrutura metodo EMP_FILIAL   ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct empfil
		WsData cm_empresa		 As String
		WsData cm_filial		 As String
		WsData cm_unidade		 As String
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ O - Estrutura metodo CONS_CABECALHO   ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct o_cabecalho
		WsData co_empresa	     	As String
		WsData co_filial	     	As String
		WsData co_unidade	     	As String
		WsData co_cliente	     	As String
		WsData co_documento	    	As String
		WsData co_serie		    	As String
		WsData co_nf_eletronica		As String
		WsData co_emissao	   	    As String
		WsData co_vencimento   		As String
		WsData co_supplier   		As String
	EndWsStruct


//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ R - Estrutura metodo CONS_CLOUD_CABEC ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct o_Cloudcabecalho
		WsData cloud_empresa	     	As String
		WsData cloud_filial	     		As String
		WsData cloud_unidade	     	As String
		WsData cloud_cliente	     	As String
		WsData cloud_documento	    	As String
		WsData cloud_serie		    	As String
		WsData cloud_nf_eletronica		As String
		WsData cloud_emissao	   	    As String
		WsData cloud_vencimento   		As String
		WsData cloud_Valor   			As String
		WsData cloud_Status	 	  		As String

	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ S - Estrutura metodo CONS_CLIPCSIST   ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct o_RetCli
		WsData c_CodTotvs		As String
		WsData c_LojTotvs		As String
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ T - Estrutura metodo CONS_TITABERTOS  ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct o_RetTit
		WsData c_Titulo		As String
		WsData c_Parcela	As String
		WsData c_Vencto		As String
		WsData c_VencRea	As String
		WsData c_ValorBruto	As String
		WsData c_ValorLiq	As String
		WsData c_Situacao	As String
		WsData c_CNPJ		As String
		WsData c_RazaoSoc	As String
		WsData c_CnpjPC		As String
		WsData c_RazaoSocPC	As String
		WsData c_Prefix		As String
		WsData c_Emissao	As String
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ U - Variaveis do metodo CONS_GETXML       ³
//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
	WsStruct o_XMLPROT
		WsData c_XmlProt		As String
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ V - Estrutura metodo CONS_PCCABEC     ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct o_pccabecalho
		WsData co_empresa		As String
		WsData co_filial		As String
		WsData co_unidade		As String
		WsData co_cliente		As String
		WsData co_documento		As String
		WsData co_serie			As String
		WsData co_nf_eletronica	As String
		WsData co_emissao		As String
		WsData co_vencimento	As String
		WsData co_valbruto		As String
		WsData co_chavenfe		As String
		WsData co_cnpjpc		As String
	EndWsStruct

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ Estrutura metodo CONS_CLOUD_PEND  ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct o_CloudTitulos
		WsData cb_cliente	     	 As String
		WsData cb_loja		     	 As String
		WsData cb_vencimento    	 As String
		WsData cb_prefixo	    	 As String
		WsData cb_numero	    	 As String
		WsData cb_parcela	    	 As String
		WsData cb_emissao	    	 As String
		WsData cb_original_valor  	 As String
	EndWsStruct


//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ P - Estrutura metodo CONS_DETALHE		³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	WsStruct listadetalhe
		WsData cp_empresa	    	 As String
		WsData cp_filial	     	 As String
		WsData cp_unidade	     	 As String
		WsData cp_vencimento    	 As String
		WsData cp_nome		     	 As String
		WsData cp_prefixo	    	 As String
		WsData cp_numero	    	 As String
		WsData cp_parcela	    	 As String
		WsData cp_tipo		    	 As String
		WsData cp_emissao	    	 As String
		WsData cp_original_valor	 As String
		WsData cp_baixa_valor	     As String
		WsData cp_boleto_valor	     As String
		WsData cp_ir		    	 As String
		WsData cp_pis		    	 As String
		WsData cp_cofins	    	 As String
		WsData cp_csll		    	 As String
		WsData cp_inss		    	 As String
		WsData cp_nfe		     	 As String
		WsData cp_observacao    	 As String
		WsData cp_cnpj		   	     As String
		WsData cp_nomecom	   	     As String
		WsData cp_descServ		 	 As String
	EndWsStruct

//ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Ä¿
//ï¿½ i - Estrutura metodo CONS_CONT_COLIG ï¿½
//ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
	WsStruct listacontColig
		WsData ctc_empresa  		As String
		WsData ctc_filial  			As String
		WsData ctc_unidade  		As String
		WsData ctc_numero   		As String
		WsData ctc_versao  			As String
		WsData ctc_cliente 			As String
		WsData ctc_loja 			As String
		WsData atc_itens_contrato  	As Array of itens_contColig
	EndWsStruct

	WsStruct itens_contColig
		WsData ctc_item 				As String
		WsData ctc_produto				As String
		WsData ctc_quantidade   		As String
		WsData ctc_valorunitario		As String
		WsData ctc_vencimento   		As String
		WsData ctc_dtareajuste  		As String
		WsData ctc_condicaopagamento	As String
		WsData ctc_sitauacao			As String
		WsData ctc_dataassinatura		As String
		WsData ctc_imposto				As String
		WsData ctc_indice   			As String
		WsData ctc_tes					As String
		WsData ctc_startrm				As String
		WsData ctc_unidademedida		As String
		WsData ctc_notasep				As String
		WsData ctc_proposta				As String
		WsData ctc_versaoproposta		As String
		WsData ctc_origem   			As String
		WsData ctc_grupo				As String
		WsData ctc_linha				As String
		WsData ntc_recno				As Integer
		WsData ctc_reajuste				As String
		WsData ctc_mesreaj				As String
		WsData ctc_diavencto			As String
		WsData ctc_valtotal				As String
		WsData atc_coligada		  		As Array of itens_Coligada
	EndWsStruct

	WsStruct itens_Coligada
		WsData cti_proposta 				As String
		WsData cti_ItemProp 				As String
		WsData cti_cgc	 					As String
		WsData cti_valor 					As String
		WsData cti_produto 					As String
		WsData cti_codcli 					As String
		WsData cti_lojacli 					As String
		WsData cti_vigde	 				As String
		WsData cti_vigate 					As String
		WsData cti_percrat 					As String
		WsData cti_clipri 					As String
		WsData cti_lojapri 					As String
		WsData cti_sitefat 					As String
	EndWsStruct
/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ CONS_DETALHE 	³ Autor ³ Cleyton Ferreira ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONS_DETALHE WsReceive c_empresa, c_filial, c_unidade, c_cliente, c_loja, c_titulo WsSend ap_detalhe WsService AUTOATENDIMENTO

	Local _WS_EMPRESA  	:= ""
	Local _WS_FILIAL   	:= ""
	Local _WS_UNIDADE  	:= ""
	Local _WS_NOME     	:= ""
	Local _WS_PREFIXO  	:= ""
	Local _WS_NUMERO   	:= ""
	Local _WS_PARCELA  	:= ""
	Local _WS_TIPO     	:= ""
	Local _WS_EMISSAO  	:= ""
	Local _WS_VENCTO   	:= ""
	Local _WS_VALFAT   	:= 0
	Local _WS_VALBAIXA 	:= 0
	Local _WS_VALBOLET 	:= 0
	Local _WS_IR       	:= 0
	Local _WS_PIS      	:= 0
	Local _WS_COFINS   	:= 0
	Local _WS_CSLL     	:= 0
	Local _WS_INSS     	:= 0
	Local _WS_OBSERV   	:= ""
	Local _WS_NFE      	:= ""
	Local _WS_CGC      	:= ""
	Local _WS_NOMECOM  	:= ""
	Local _WS_DESCSERV 	:= ""

	Local lCancelado	:= .F.
	Local cQuery     	:= ""
	Local nMoeda	   	:= 1
	Local nX          	:= 0
	Local cPosSF3	   	:= ""
	Local aDados	   	:= {}
	Local aValor     	:= {}
	Local cod_cliente 	:= c_cliente
	Local cod_loja     	:= c_loja
	Local cParDime		:= SuperGetMv("TI_DIMEON",,"2") 
	Local oListaDetalhe
	Local lAchou   := .F. 
//-------------------------------------------------------------------------------------------
// Variaveis para verificacao dos titulos do PCC na consulta e reimpressao de NF - Portal  //
//-------------------------------------------------------------------------------------------
	Local _nRet	:= 0							// Variavel para verificacao dos impostos do PCC
	Local _lAtiva	:= GetMv("TI_#AADETA",,.T.)	// Verifica se o processo de verificacao esta ativo
	Local _cTipoV	:= GetMv("TI_#AATPVL",,"V")	// Verifica o tipo de valor a ser passado para o SumAbatRec()

	Public oMsgItem3
	If cParDime =='1'
		
		If Len(c_empresa) != 2 .or. Len(c_filial) != TamSx3("A1_FILIAL")[1] 
			cFilAnt := c_filial
			cEmpant := c_empresa
		Else 
			If c_empresa != cEmpAnt 
				RpcClearEnv()
				RpcSetEnv(c_empresa,c_filial)//prepara ambiente devido função Baixas
			Else 
				cFilAnt := c_filial 
			EndIf 
		EndIf 
		
		_WS_EMPRESA := c_empresa
		_WS_FILIAL  := c_filial
		_WS_UNIDADE := c_unidade

		U_xCONOUT("AUTOATENDIMENTO","CONS_DETALHE","REQUISICAO","")
		lAchou := U_AvalCli(_WS_EMPRESA, @cod_cliente, @cod_loja)
			
		cQuery := "SELECT "
		cQuery += "SE1.E1_FILIAL, SE1.E1_CLIENTE, SE1.E1_LOJA, SE1.E1_PREFIXO, SE1.E1_NUM, SE1.E1_PARCELA, SE1.E1_TIPO, SE1.E1_NOMCLI, "
		cQuery += "SE1.E1_NATUREZ, SE1.E1_SITUACA, SE1.E1_PORTADO, SE1.E1_SALDO, SE1.E1_MOEDA, SE1.E1_RECIBO, SE1.E1_ORIGEM, "
		cQuery += "SE1.E1_EMISSAO, SE1.E1_VENCTO, SE1.E1_BAIXA, SE1.E1_FATURA, SE1.E1_DTFATUR, SE1.E1_VALLIQ, "
		cQuery += "SE1.E1_VALOR,SE1.E1_SDACRES,SE1.E1_SDDECRE,SE1.E1_TXMOEDA,SE1.E1_ACRESC,SE1.E1_DECRESC,SE1.R_E_C_N_O_ RECNO, "
		cQuery += "SE1.E1_INSS, SE1.E1_CSLL, SE1.E1_COFINS, SE1.E1_PIS, SE1.E1_IRRF, E1_FILORIG "
		cQuery += "FROM "
		cQuery += RetFullName("SE1",_WS_EMPRESA)+" SE1 "
		cQuery += "WHERE "
		cQuery += "SE1.E1_FILIAL   	= '" +_WS_FILIAL+ "' 	AND "
		cQuery += "SE1.E1_CLIENTE 	= '" +cod_cliente	+ "' 		AND "
		cQuery += "SE1.E1_LOJA    	= '" +cod_loja	+ "' 		AND "
		cQuery += "SE1.E1_NUM     	= '" +c_titulo	+ "' 		AND "
		cQuery += "SE1.E1_TIPO <> 'PR ' AND "
		cQuery += "SE1.E1_TIPO <> 'RA ' AND "
		cQuery += "SE1.E1_TIPO NOT LIKE '%-'	AND "
		cQuery += "SE1.D_E_L_E_T_ <> '*'		"

		cQuery := ChangeQuery(cQuery)

		cQueryx := cQuery

		aValor    := {}
			
		If lAchou
			dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_E1",.T.,.T.)

			//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
			//³ Carrega array para trabalho ³
			//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

			While  T_E1->(!Eof())
				SE1->(dbgoto(T_E1->RECNO))
				aValor	:= Baixas(T_E1->E1_NATUREZ,T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,nMoeda,"R",T_E1->E1_CLIENTE,dDataBase,T_E1->E1_LOJA,,,,.T., @lCancelado)

				//-------------------------------------------------------------------------------------------
				// Para verificar se existem impostos em um determinado titulo, chamo a funcao SumAbatRec, //
				// porem eh necessario posicionar na SE1 para que funcione corretamente.                   //
				//-------------------------------------------------------------------------------------------
				If _lAtiva
					dbSelectArea("SE1")
					SE1->(dbSetOrder(1)) //E1_FILIAL+E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO
					If SE1->(dbSeek(xFilial("SE1") + T_E1->( E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO )))
						_nRet := SumAbatRec(SE1->E1_PREFIXO, SE1->E1_NUM, SE1->E1_PARCELA, SE1->E1_MOEDA, _cTipoV, SE1->E1_VENCTO)
					EndIf
				EndIf

				//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
				//³ Carrega avariaveis para facilitar manutencao no fonte ³
				//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
				_WS_FILIAL    	:= T_E1->E1_FILIAL
				_WS_NOME    	:= ""
				_WS_PREFIXO 	:= Iif(!Empty(T_E1->E1_PREFIXO),"'"+T_E1->E1_PREFIXO+"'",CriaVar("E1_PREFIXO"))
				_WS_NUMERO 	:= Iif(!Empty(T_E1->E1_NUM)    ,"'"+T_E1->E1_NUM    +"'",CriaVar("E1_NUMERO"))
				_WS_PARCELA 	:= Iif(!Empty(T_E1->E1_PARCELA),"'"+T_E1->E1_PARCELA+"'",CriaVar("E1_PARCELA"))
				_WS_TIPO 		:= Iif(!Empty(T_E1->E1_TIPO)   ,"'"+T_E1->E1_TIPO   +"'",CriaVar("E1_TIPO"))
				_WS_EMISSAO 	:= Iif(!Empty(T_E1->E1_EMISSAO),DTOC(STOD(T_E1->E1_EMISSAO)),"  /  /  ")
				_WS_VENCTO 	:= Iif(!Empty(T_E1->E1_VENCTO),DTOC(STOD(T_E1->E1_VENCTO)),"  /  /  ")
				//_WS_PRORROG 	:= Iif(!Empty(T_E1->E1_VENCREA),DTOC(STOD(T_E1->E1_VENCREA)),"  /  /  ")
				_WS_VALFAT 	:= xMoeda(T_E1->E1_VALOR,T_E1->E1_MOEDA,nMoeda,T_E1->E1_EMISSAO,,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0)) //* Iif(Alltrim(T_E1->E1_TIPO)$(MVRECANT+","+MV_CRNEG),-1,1)
				_WS_VALBAIXA 	:= aValor[5]
				_WS_IR 			:= T_E1->E1_IRRF
				_WS_PIS 		:= IIf(!_lAtiva, T_E1->E1_PIS		,IIf(_nRet > 0, T_E1->E1_PIS		,0))
				_WS_COFINS 	:= IIf(!_lAtiva, T_E1->E1_COFINS	,IIf(_nRet > 0, T_E1->E1_COFINS	,0))
				_WS_CSLL 		:= IIf(!_lAtiva, T_E1->E1_CSLL		,IIf(_nRet > 0, T_E1->E1_CSLL	,0))
				_WS_INSS 		:= T_E1->E1_INSS
				_WS_NFE 		:= Posicione("SF3",5,T_E1->E1_FILIAL+T_E1->(E1_PREFIXO+E1_NUM+E1_CLIENTE+E1_LOJA),"F3_NFELETR")
				_WS_OBSERV 	:= "-"
				nPosFil        := aScan(aEmpresas,{ |x| AllTrim(x[1])+AllTrim(x[2]) == AllTrim(_WS_EMPRESA)+AllTrim(T_E1->E1_FILIAL)})
				_WS_CGC        := Posicione("SM0",1,_WS_EMPRESA+_WS_FILIAL,"M0_CGC")
				_WS_NOMECOM   	:= Posicione("SM0",1,_WS_EMPRESA+_WS_FILIAL,"M0_NOMECOM")
				_WS_VALBOLET 	:= ( _WS_VALFAT - _WS_IR - _WS_PIS - _WS_COFINS - _WS_CSLL )

				cPosSF3	:= Posicione("SF3",5,T_E1->E1_FILIAL+T_E1->(E1_PREFIXO+E1_NUM+E1_CLIENTE+E1_LOJA),"F3_CODISS")
				SX5->(dbSetOrder(1))
				If SX5->(dbSeek(xFilial("SX5")+"60"+cPosSF3))
					_WS_DESCSERV := cPosSF3 +" - "+ SX5->(FieldGet(FieldPos("X5_DESCRI")))
				Endif

				dbSelectArea("T_E1")

				aAdd(aDados,Array(25))

				aDados[Len(aDados),01]	:= _WS_EMPRESA
				aDados[Len(aDados),02]	:= _WS_FILIAL
				aDados[Len(aDados),03]	:= _WS_UNIDADE
				aDados[Len(aDados),04]	:= _WS_VENCTO
				aDados[Len(aDados),05] 	:= _WS_NOME
				aDados[Len(aDados),06] 	:= _WS_PREFIXO
				aDados[Len(aDados),07] 	:= _WS_NUMERO
				aDados[Len(aDados),08] 	:= _WS_PARCELA
				aDados[Len(aDados),09] 	:= _WS_TIPO
				aDados[Len(aDados),10] 	:= _WS_EMISSAO
				aDados[Len(aDados),11] 	:= _WS_VALFAT
				aDados[Len(aDados),12] 	:= _WS_VALBAIXA
				aDados[Len(aDados),13] 	:= _WS_VALBOLET
				aDados[Len(aDados),14] 	:= _WS_IR
				aDados[Len(aDados),15] 	:= _WS_PIS
				aDados[Len(aDados),16] 	:= _WS_COFINS
				aDados[Len(aDados),17] 	:= _WS_CSLL
				aDados[Len(aDados),18] 	:= _WS_INSS
				aDados[Len(aDados),19] 	:= _WS_NFE
				aDados[Len(aDados),20] 	:= _WS_OBSERV
				aDados[Len(aDados),21] 	:= _WS_CGC
				aDados[Len(aDados),22] 	:= _WS_NOMECOM
				aDados[Len(aDados),23] 	:= _WS_DESCSERV



				T_E1->(dbSkip())
			EndDo

			T_E1->(dbCloseArea())
		EndIf 

		If Len(aDados) = 0

			aAdd(aDados,Array(23))
			aDados[Len(aDados),01]	:= ""
			aDados[Len(aDados),02]	:= ""
			aDados[Len(aDados),03]	:= ""
			aDados[Len(aDados),04]	:= ""
			aDados[Len(aDados),05] 	:= ""
			aDados[Len(aDados),06] 	:= ""
			aDados[Len(aDados),07] 	:= ""
			aDados[Len(aDados),08] 	:= ""
			aDados[Len(aDados),09] 	:= ""
			aDados[Len(aDados),10] 	:= ""
			aDados[Len(aDados),11] 	:= ""
			aDados[Len(aDados),12] 	:= ""
			aDados[Len(aDados),13] 	:= ""
			aDados[Len(aDados),14] 	:= ""
			aDados[Len(aDados),15] 	:= ""
			aDados[Len(aDados),16] 	:= ""
			aDados[Len(aDados),17] 	:= ""
			aDados[Len(aDados),18] 	:= ""
			aDados[Len(aDados),19] 	:= ""
			aDados[Len(aDados),20] 	:= ""
			aDados[Len(aDados),21] 	:= ""
			aDados[Len(aDados),22] 	:= ""
			aDados[Len(aDados),23] 	:= ""

		EndIf

		For nX := 1 To Len(aDados)

			oListaDetalhe := wsclassnew("listadetalhe")

			oListaDetalhe:cp_empresa		:= aDados[nX,01]
			oListaDetalhe:cp_filial	    	:= aDados[nX,02]
			oListaDetalhe:cp_unidade		:= aDados[nX,03]
			oListaDetalhe:cp_vencimento 	:= aDados[nX,04]
			oListaDetalhe:cp_nome			:= AllTrim(aDados[nX,05])
			oListaDetalhe:cp_prefixo		:= StrTran(StrTran(StrTran(AllTrim(aDados[nX,06]), CHR(10), ""), CHR(32), ""), CHR(9), "")//Retirado espaços, enter, tab
			oListaDetalhe:cp_numero	    	:= aDados[nX,07]
			oListaDetalhe:cp_parcela		:= aDados[nX,08]
			oListaDetalhe:cp_tipo			:= aDados[nX,09]
			oListaDetalhe:cp_emissao	    := aDados[nX,10]
			oListaDetalhe:cp_original_valor	:= Transform(aDados[nX,11],"@E 999,999,999.99")
			oListaDetalhe:cp_baixa_valor	:= Transform(aDados[nX,12],"@E 999,999,999.99")
			oListaDetalhe:cp_boleto_valor	:= Transform(aDados[nX,13],"@E 999,999,999.99")
			oListaDetalhe:cp_ir		   		:= Transform(aDados[nX,14],"@E 999,999,999.99")
			oListaDetalhe:cp_pis		    := Transform(aDados[nX,15],"@E 999,999,999.99")
			oListaDetalhe:cp_cofins	    	:= Transform(aDados[nX,16],"@E 999,999,999.99")
			oListaDetalhe:cp_csll			:= Transform(aDados[nX,17],"@E 999,999,999.99")
			oListaDetalhe:cp_inss			:= Transform(aDados[nX,18],"@E 999,999,999.99")
			oListaDetalhe:cp_nfe			:= aDados[nX,19]
			oListaDetalhe:cp_observacao 	:= aDados[nX,20]
			oListaDetalhe:cp_cnpj			:= aDados[nX,21]
			oListaDetalhe:cp_nomecom		:= aDados[nX,22]
			oListaDetalhe:cp_descServ		:= aDados[nX,23]

			aAdd(::ap_detalhe,oListaDetalhe)

		Next
		
	Else
		cFilAnt := c_filial
		cEmpant := c_empresa

		_WS_EMPRESA := c_empresa
		_WS_FILIAL  := c_filial
		_WS_UNIDADE := c_unidade
		U_xCONOUT("AUTOATENDIMENTO","CONS_DETALHE","REQUISICAO","")

		cQuery := "SELECT "
		cQuery += "SE1.E1_FILIAL, SE1.E1_CLIENTE, SE1.E1_LOJA, SE1.E1_PREFIXO, SE1.E1_NUM, SE1.E1_PARCELA, SE1.E1_TIPO, SE1.E1_NOMCLI, "
		cQuery += "SE1.E1_NATUREZ, SE1.E1_SITUACA, SE1.E1_PORTADO, SE1.E1_SALDO, SE1.E1_MOEDA, SE1.E1_RECIBO, SE1.E1_ORIGEM, "
		cQuery += "SE1.E1_EMISSAO, SE1.E1_VENCTO, SE1.E1_BAIXA, SE1.E1_FATURA, SE1.E1_DTFATUR, SE1.E1_VALLIQ, "
		cQuery += "SE1.E1_VALOR,SE1.E1_SDACRES,SE1.E1_SDDECRE,SE1.E1_TXMOEDA,SE1.E1_ACRESC,SE1.E1_DECRESC, "
		cQuery += "SE1.E1_INSS, SE1.E1_CSLL, SE1.E1_COFINS, SE1.E1_PIS, SE1.E1_IRRF, E1_FILORIG "
		cQuery += "FROM "
		cQuery += RetFullName("SE1",_WS_EMPRESA)+" SE1 "
		cQuery += "WHERE "
		cQuery += "SE1.E1_FILIAL   	= '" +_WS_FILIAL+ "' 	AND "
		cQuery += "SE1.E1_CLIENTE 	= '" +c_cliente	+ "' 		AND "
		cQuery += "SE1.E1_LOJA    	= '" +c_loja	+ "' 		AND "
		cQuery += "SE1.E1_NUM     	= '" +c_titulo	+ "' 		AND "
		cQuery += "SE1.E1_TIPO <> 'PR ' AND "
		cQuery += "SE1.E1_TIPO <> 'RA ' AND "
		cQuery += "SE1.E1_TIPO NOT LIKE '%-'	AND "
		cQuery += "SE1.D_E_L_E_T_ <> '*'		"

		cQuery := ChangeQuery(cQuery)

		cQueryx := cQuery

		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_E1",.T.,.T.)

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ Carrega array para trabalho ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

		aValor    := {}

		While T_E1->(!Eof())

			aValor	:= Baixas(T_E1->E1_NATUREZ,T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,nMoeda,"R",T_E1->E1_CLIENTE,dDataBase,T_E1->E1_LOJA,,,,.T., @lCancelado)

			//-------------------------------------------------------------------------------------------
			// Para verificar se existem impostos em um determinado titulo, chamo a funcao SumAbatRec, //
			// porem eh necessario posicionar na SE1 para que funcione corretamente.                   //
			//-------------------------------------------------------------------------------------------
			If _lAtiva
				dbSelectArea("SE1")
				SE1->(dbSetOrder(1)) //E1_FILIAL+E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO
				If SE1->(dbSeek(xFilial("SE1") + T_E1->( E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO )))
					_nRet := SumAbatRec(SE1->E1_PREFIXO, SE1->E1_NUM, SE1->E1_PARCELA, SE1->E1_MOEDA, _cTipoV, SE1->E1_VENCTO)
				EndIf
			EndIf

			//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
			//³ Carrega avariaveis para facilitar manutencao no fonte ³
			//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
			_WS_FILIAL    	:= T_E1->E1_FILIAL
			_WS_NOME    	:= ""
			_WS_PREFIXO 	:= Iif(!Empty(T_E1->E1_PREFIXO),"'"+T_E1->E1_PREFIXO+"'",CriaVar("E1_PREFIXO"))
			_WS_NUMERO 	:= Iif(!Empty(T_E1->E1_NUM)    ,"'"+T_E1->E1_NUM    +"'",CriaVar("E1_NUMERO"))
			_WS_PARCELA 	:= Iif(!Empty(T_E1->E1_PARCELA),"'"+T_E1->E1_PARCELA+"'",CriaVar("E1_PARCELA"))
			_WS_TIPO 		:= Iif(!Empty(T_E1->E1_TIPO)   ,"'"+T_E1->E1_TIPO   +"'",CriaVar("E1_TIPO"))
			_WS_EMISSAO 	:= Iif(!Empty(T_E1->E1_EMISSAO),DTOC(STOD(T_E1->E1_EMISSAO)),"  /  /  ")
			_WS_VENCTO 	:= Iif(!Empty(T_E1->E1_VENCTO),DTOC(STOD(T_E1->E1_VENCTO)),"  /  /  ")
			//_WS_PRORROG 	:= Iif(!Empty(T_E1->E1_VENCREA),DTOC(STOD(T_E1->E1_VENCREA)),"  /  /  ")
			_WS_VALFAT 	:= xMoeda(T_E1->E1_VALOR,T_E1->E1_MOEDA,nMoeda,T_E1->E1_EMISSAO,,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0)) //* Iif(Alltrim(T_E1->E1_TIPO)$(MVRECANT+","+MV_CRNEG),-1,1)
			_WS_VALBAIXA 	:= aValor[5]
			_WS_IR 			:= T_E1->E1_IRRF
			_WS_PIS 		:= IIf(!_lAtiva, T_E1->E1_PIS		,IIf(_nRet > 0, T_E1->E1_PIS		,0))
			_WS_COFINS 	:= IIf(!_lAtiva, T_E1->E1_COFINS	,IIf(_nRet > 0, T_E1->E1_COFINS	,0))
			_WS_CSLL 		:= IIf(!_lAtiva, T_E1->E1_CSLL		,IIf(_nRet > 0, T_E1->E1_CSLL	,0))
			_WS_INSS 		:= T_E1->E1_INSS
			_WS_NFE 		:= Posicione("SF3",5,T_E1->E1_FILIAL+T_E1->(E1_PREFIXO+E1_NUM+E1_CLIENTE+E1_LOJA),"F3_NFELETR")
			_WS_OBSERV 	:= "-"
			nPosFil        := aScan(aEmpresas,{ |x| AllTrim(x[1])+AllTrim(x[2]) == AllTrim(_WS_EMPRESA)+AllTrim(T_E1->E1_FILIAL)})
			_WS_CGC        := Posicione("SM0",1,_WS_EMPRESA+_WS_FILIAL,"M0_CGC")
			_WS_NOMECOM   	:= Posicione("SM0",1,_WS_EMPRESA+_WS_FILIAL,"M0_NOMECOM")
			_WS_VALBOLET 	:= ( _WS_VALFAT - _WS_IR - _WS_PIS - _WS_COFINS - _WS_CSLL )

			cPosSF3	:= Posicione("SF3",5,T_E1->E1_FILIAL+T_E1->(E1_PREFIXO+E1_NUM+E1_CLIENTE+E1_LOJA),"F3_CODISS")
			SX5->(dbSetOrder(1))
			If SX5->(dbSeek(xFilial("SX5")+"60"+cPosSF3))
				_WS_DESCSERV := cPosSF3 +" - "+ SX5->(FieldGet(FieldPos("X5_DESCRI")))
			Endif

			dbSelectArea("T_E1")

			aAdd(aDados,Array(25))

			aDados[Len(aDados),01]	:= _WS_EMPRESA
			aDados[Len(aDados),02]	:= _WS_FILIAL
			aDados[Len(aDados),03]	:= _WS_UNIDADE
			aDados[Len(aDados),04]	:= _WS_VENCTO
			aDados[Len(aDados),05] 	:= _WS_NOME
			aDados[Len(aDados),06] 	:= _WS_PREFIXO
			aDados[Len(aDados),07] 	:= _WS_NUMERO
			aDados[Len(aDados),08] 	:= _WS_PARCELA
			aDados[Len(aDados),09] 	:= _WS_TIPO
			aDados[Len(aDados),10] 	:= _WS_EMISSAO
			aDados[Len(aDados),11] 	:= _WS_VALFAT
			aDados[Len(aDados),12] 	:= _WS_VALBAIXA
			aDados[Len(aDados),13] 	:= _WS_VALBOLET
			aDados[Len(aDados),14] 	:= _WS_IR
			aDados[Len(aDados),15] 	:= _WS_PIS
			aDados[Len(aDados),16] 	:= _WS_COFINS
			aDados[Len(aDados),17] 	:= _WS_CSLL
			aDados[Len(aDados),18] 	:= _WS_INSS
			aDados[Len(aDados),19] 	:= _WS_NFE
			aDados[Len(aDados),20] 	:= _WS_OBSERV
			aDados[Len(aDados),21] 	:= _WS_CGC
			aDados[Len(aDados),22] 	:= _WS_NOMECOM
			aDados[Len(aDados),23] 	:= _WS_DESCSERV



			T_E1->(dbSkip())
		EndDo

		T_E1->(dbCloseArea())

		If Len(aDados) = 0

			aAdd(aDados,Array(23))
			aDados[Len(aDados),01]	:= ""
			aDados[Len(aDados),02]	:= ""
			aDados[Len(aDados),03]	:= ""
			aDados[Len(aDados),04]	:= ""
			aDados[Len(aDados),05] 	:= ""
			aDados[Len(aDados),06] 	:= ""
			aDados[Len(aDados),07] 	:= ""
			aDados[Len(aDados),08] 	:= ""
			aDados[Len(aDados),09] 	:= ""
			aDados[Len(aDados),10] 	:= ""
			aDados[Len(aDados),11] 	:= ""
			aDados[Len(aDados),12] 	:= ""
			aDados[Len(aDados),13] 	:= ""
			aDados[Len(aDados),14] 	:= ""
			aDados[Len(aDados),15] 	:= ""
			aDados[Len(aDados),16] 	:= ""
			aDados[Len(aDados),17] 	:= ""
			aDados[Len(aDados),18] 	:= ""
			aDados[Len(aDados),19] 	:= ""
			aDados[Len(aDados),20] 	:= ""
			aDados[Len(aDados),21] 	:= ""
			aDados[Len(aDados),22] 	:= ""
			aDados[Len(aDados),23] 	:= ""

		EndIf

		For nX := 1 To Len(aDados)

			oListaDetalhe := wsclassnew("listadetalhe")

			oListaDetalhe:cp_empresa		:= aDados[nX,01]
			oListaDetalhe:cp_filial	    	:= aDados[nX,02]
			oListaDetalhe:cp_unidade		:= aDados[nX,03]
			oListaDetalhe:cp_vencimento 	:= aDados[nX,04]
			oListaDetalhe:cp_nome			:= AllTrim(aDados[nX,05])
			oListaDetalhe:cp_prefixo		:= StrTran(StrTran(StrTran(AllTrim(aDados[nX,06]), CHR(10), ""), CHR(32), ""), CHR(9), "")//Retirado espaços, enter, tab
			oListaDetalhe:cp_numero	    	:= aDados[nX,07]
			oListaDetalhe:cp_parcela		:= aDados[nX,08]
			oListaDetalhe:cp_tipo			:= aDados[nX,09]
			oListaDetalhe:cp_emissao	    := aDados[nX,10]
			oListaDetalhe:cp_original_valor	:= Transform(aDados[nX,11],"@E 999,999,999.99")
			oListaDetalhe:cp_baixa_valor	:= Transform(aDados[nX,12],"@E 999,999,999.99")
			oListaDetalhe:cp_boleto_valor	:= Transform(aDados[nX,13],"@E 999,999,999.99")
			oListaDetalhe:cp_ir		   		:= Transform(aDados[nX,14],"@E 999,999,999.99")
			oListaDetalhe:cp_pis		    := Transform(aDados[nX,15],"@E 999,999,999.99")
			oListaDetalhe:cp_cofins	    	:= Transform(aDados[nX,16],"@E 999,999,999.99")
			oListaDetalhe:cp_csll			:= Transform(aDados[nX,17],"@E 999,999,999.99")
			oListaDetalhe:cp_inss			:= Transform(aDados[nX,18],"@E 999,999,999.99")
			oListaDetalhe:cp_nfe			:= aDados[nX,19]
			oListaDetalhe:cp_observacao 	:= aDados[nX,20]
			oListaDetalhe:cp_cnpj			:= aDados[nX,21]
			oListaDetalhe:cp_nomecom		:= aDados[nX,22]
			oListaDetalhe:cp_descServ		:= aDados[nX,23]

			aAdd(::ap_detalhe,oListaDetalhe)

		Next

	EndIF
	U_xCONOUT("AUTOATENDIMENTO","CONS_DETALHE","RETORNO","")

Return .T.



/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ CONS_CABECALHO   ³ Autor ³ Cleyton Ferreira ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONS_CABECALHO WsReceive c_cliente, c_loja, cc_de_emissao, cc_ate_emissao, cc_de_nfiscal, cc_ate_nfiscal,cc_de_nfEletr,cc_ate_nfEletr WsSend o_cabec WsService AUTOATENDIMENTO

	Local cQuery     	:= ""
	Local cQryComp   	:= ""
	Local cEmpTemp   	:= ""
	Local cFilTemp   	:= ""
	Local nPos	   	:= 0
	Local nV         	:= 0
	Local nX         	:= 0
	Local aSigaMat   	:= WS_Sw_SigaMat() //Alinhado que tabela ZX5 deverá conter todas as empresa\filiais sempre
	Local aDados     	:= {}
	Local oCabecalho
	Local aFilInc       := {} 
	Local nAscan
	Local cSeriMigrada := ""
	Local aSeriMigrada := {}
	Local cSupplier    := ""
	Local cod_cliente  :=c_cliente
	Local cod_loja	   :=c_loja
	Local lAchou       := .F. 
	Local cStaSup	   := SuperGetMv("TI_SUPSTA", , "8")
	Local cParDime	   := SuperGetMv("TI_DIMEON",,"2")
	Local cEmpOld      := '' 
	Public oMsgItem3
	If cParDime =='1'

		U_xCONOUT("AUTOATENDIMENTO","CONS_CABECALHO","REQUISICAO","")

		For nV := 1 To Len(aSigaMat)

			cEmpTemp := aSigaMat[nV,1]
			cFilTemp := aSigaMat[nV,2]

			If cEmpOld <> cEmpTemp 
				RpcClearEnv()
				RpcSetEnv(cEmpTemp,cFilTemp)//para que TCRMX029 varre a zx5 da respectiva empresa adiante
				cStaSup	   := RetMvParam("TI_SUPSTA")  
				cod_cliente :=c_cliente
				cod_loja	:=c_loja
				lAchou      := U_AvalCli(cEmpTemp,  @cod_cliente, @cod_loja)
				cEmpOld     := cEmpTemp 
			Else 
				cFilAnt     := cFilTemp 
			EndIf 			
			
			If !lAchou     
				Loop 
			EndIf 

			aFilInc := U_TCRMX029("FILINC",{},{},6) // Obtem as filiais incorporadas
			cQuery += "SELECT '"+ cEmpTemp +"' EMPRESA, '" + aSigaMat[nV,3] +"' UNIDADE, "
			cQuery += "SF2.F2_CLIENTE, SF2.F2_LOJA   , SF2.F2_DOC   , "
			cQuery += "SF2.F2_SERIE  , SF2.F2_NFELETR, SF2.F2_EMISSAO, SF2.F2_FILIAL, "
			cQuery += "(SELECT MIN(SE1.E1_VENCTO) FROM "+ RetFullName("SE1",cEmpTemp)+" SE1 WHERE E1_FILIAL = F2_FILIAL  "
			cQuery += "AND E1_CLIENTE = F2_CLIENTE AND E1_LOJA = F2_LOJA AND E1_PREFIXO = F2_SERIE "
			cQuery += "AND E1_NUM = F2_DOC AND E1_TIPO = 'NF') E1_VENCREA "
			cQuery += "FROM  " + RetFullName("SF2",cEmpTemp)+" SF2 "
			cQuery += "WHERE "
			cQuery += "SF2.F2_FILIAL  = '"+cFilTemp+"'	AND "
			cQuery += "SF2.F2_CLIENTE = '"+cod_cliente+"'	AND "
			cQuery += "SF2.F2_LOJA    = '"+cod_loja+"' 	AND "
			cQuery += "SF2.F2_EMISSAO BETWEEN '"+cc_de_emissao+"' AND '"+cc_ate_emissao+"' AND "
			cQuery += "SF2.F2_DOC     BETWEEN '"+cc_de_nfiscal+"' AND '"+cc_ate_nfiscal+"' AND "
			cQuery += "SF2.F2_NFELETR BETWEEN '"+cc_de_nfEletr+"' AND '"+cc_ate_nfEletr+"' AND "
			cQuery += "SF2.D_E_L_E_T_ = ' ' "

			// Pesquisa se a filial consta na lista de incorporadas, se constar, verifica se existe o mesmo titulo
			// na filial de destino, mas com prefixo BMA. Se não existir, a NF deve aparecer no portal
			If (nAscan :=  Ascan(aFilInc, {|e| Alltrim(e[1]) == Alltrim(cFilTemp) })) > 0
				ZX5->(DbSetOrder(1))
				// Obtem as series utilizadas na migração de todas as filiais incorporadas
				aSeriMigrada := U_TCRMX029("XN",{},{},1,aFilInc[nAscan,2]) // Obtem as series das filiais incorporadas (filial de destino)
				If !Empty(aSeriMigrada)
					cSeriMigrada := Alltrim(U_Arr2Str(aSeriMigrada,"/"))
					// Retira a ultima "/"
					cSeriMigrada := If(Right(cSeriMigrada,1) == "/",SubStr(cSeriMigrada,1,Rat("/",cSeriMigrada)-1),cSeriMigrada)
					cQuery += " AND (SELECT COUNT(*) FROM " + RetFullName("SF2",cEmpTemp)+;
						" SF2B WHERE SF2B.F2_FILIAL = '" + aFilInc[nAscan,2] + ;
						"' AND SF2B.F2_DOC = SF2.F2_DOC AND SF2B.F2_SERIE IN "+FormatIn(cSeriMigrada,"/") +;
						" AND SF2B.F2_CLIENTE = SF2.F2_CLIENTE AND SF2B.F2_LOJA = SF2.F2_LOJA) = 0 "
				EndIf
			Endif

			cQuery := ChangeQuery(cQuery)

			dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQryComp+cQuery), "T_F2" , .F., .T.)

			While T_F2->(!Eof())

				nPos := aScan(aDados,{|x| 	AllTrim(x[1])			+AllTrim(x[2])				+AllTrim(x[6])			+AllTrim(x[7])	== ;
					AllTrim(aSigaMat[nV,1])	+AllTrim(T_F2->F2_FILIAL)	+AllTrim(T_F2->F2_DOC)	+AllTrim(T_F2->F2_SERIE)})

				If nPos == 0

					aAdd(aDados,{ AllTrim(aSigaMat[nV,1]) ,;
						AllTrim(T_F2->F2_FILIAL)		,;
						aSigaMat[nV,3]					,;
						T_F2->F2_CLIENTE				,;
						AllTrim(T_F2->F2_DOC)			,;
						AllTrim(T_F2->F2_SERIE)			,;
						T_F2->F2_NFELETR				,;
						DtoC(StoD(T_F2->F2_EMISSAO))	,;
						DtoC(StoD(T_F2->E1_VENCREA))    ,;
						U_TFX005STA(AllTrim(T_F2->F2_FILIAL),AllTrim(T_F2->F2_DOC),AllTrim(T_F2->F2_SERIE), T_F2->F2_CLIENTE,c_loja,'Z',.F.,.F.) })
					nPos := Len(aDados)
				EndIf

				T_F2->(dbSkip())

			EndDo

			T_F2->(dbCloseArea())

			cQuery := ""
		Next nV

		If Len(aDados) == 0
			aAdd(aDados,{"","","","","","","","","",""})
		EndIf

		aSort(aDados,,,{|x,y| CtoD(X[8]) > CtoD(y[8]) })

		For nx := 1 To Len(aDados)

			oCabecalho:=wsclassnew("o_cabecalho")
			oCabecalho:co_empresa	   := aDados[nx,01]  //AllTrim(aSigaMat[nV,1]
			oCabecalho:co_filial	   := aDados[nx,02]  //AllTrim(T_F2->F2_FILIAL)
			oCabecalho:co_unidade	   := aDados[nx,03]  //aSigaMat[nV,3]
			oCabecalho:co_cliente	   := aDados[nx,04]  //T_F2->F2_CLIENTE
			oCabecalho:co_documento	   := aDados[nx,05]  //AllTrim(T_F2->F2_DOC)
			oCabecalho:co_serie		   := aDados[nx,06]  //AllTrim(T_F2->F2_SERIE)
			oCabecalho:co_nf_eletronica:= aDados[nx,07]  //T_F2->F2_NFELETR
			oCabecalho:co_emissao	   := aDados[nx,08]  //DtoC(StoD(T_F2->F2_EMISSAO))
			oCabecalho:co_vencimento   := aDados[nx,09]  //DtoC(StoD(T_F2->E1_VENCREA))
			cSupplier				   := aDados[nx,10] //Indica se a NF é Supplier ou nao
			
			If cSupplier $ cStaSup
				cSupplier:="S"
			Else
				cSupplier:="N"
			EndIF
			oCabecalho:co_supplier     :=cSupplier
			aAdd(::o_cabec,oCabecalho)

		Next nx

	Else

		U_xCONOUT("AUTOATENDIMENTO","CONS_CABECALHO","REQUISICAO","")

		For nV := 1 To Len(aSigaMat)

			cEmpTemp := aSigaMat[nV,1]
			cFilTemp := aSigaMat[nV,2]

			cQuery += "SELECT '"+ cEmpTemp +"' EMPRESA, '" + aSigaMat[nV,3] +"' UNIDADE, "
			cQuery += "SF2.F2_CLIENTE, SF2.F2_LOJA   , SF2.F2_DOC   , "
			cQuery += "SF2.F2_SERIE  , SF2.F2_NFELETR, SF2.F2_EMISSAO, SF2.F2_FILIAL, "
			cQuery += "(SELECT MIN(SE1.E1_VENCTO) FROM "+ RetFullName("SE1",cEmpTemp)+" SE1 WHERE E1_FILIAL = F2_FILIAL  "
			cQuery += "AND E1_CLIENTE = F2_CLIENTE AND E1_LOJA = F2_LOJA AND E1_PREFIXO = F2_SERIE "
			cQuery += "AND E1_NUM = F2_DOC AND E1_TIPO = 'NF') E1_VENCREA"
			cQuery += "FROM  " + RetFullName("SF2",cEmpTemp)+" SF2 "
			cQuery += "WHERE "
			cQuery += "SF2.F2_FILIAL  = '"+cFilTemp+"'	AND "
			cQuery += "SF2.F2_CLIENTE = '"+c_cliente+"'	AND "
			cQuery += "SF2.F2_LOJA    = '"+c_loja+"' 	AND "
			cQuery += "SF2.F2_EMISSAO BETWEEN '"+cc_de_emissao+"' AND '"+cc_ate_emissao+"' AND "
			cQuery += "SF2.F2_DOC     BETWEEN '"+cc_de_nfiscal+"' AND '"+cc_ate_nfiscal+"' AND "
			cQuery += "SF2.F2_NFELETR BETWEEN '"+cc_de_nfEletr+"' AND '"+cc_ate_nfEletr+"' AND "
			cQuery += "SF2.D_E_L_E_T_ = ' ' "

			// Pesquisa se a filial consta na lista de incorporadas, se constar, verifica se existe o mesmo titulo
			// na filial de destino, mas com prefixo BMA. Se não existir, a NF deve aparecer no portal
			If (nAscan :=  Ascan(aFilInc, {|e| Alltrim(e[1]) == Alltrim(cFilTemp) })) > 0
				ZX5->(DbSetOrder(1))
				// Obtem as series utilizadas na migração de todas as filiais incorporadas
				aSeriMigrada := U_TCRMX029("XN",{},{},1,aFilInc[nAscan,2]) // Obtem as series das filiais incorporadas (filial de destino)
				If !Empty(aSeriMigrada)
					cSeriMigrada := Alltrim(U_Arr2Str(aSeriMigrada,"/"))
					// Retira a ultima "/"
					cSeriMigrada := If(Right(cSeriMigrada,1) == "/",SubStr(cSeriMigrada,1,Rat("/",cSeriMigrada)-1),cSeriMigrada)
					cQuery += " AND (SELECT COUNT(*) FROM " + RetFullName("SF2",cEmpTemp)+;
						" SF2B WHERE SF2B.F2_FILIAL = '" + aFilInc[nAscan,2] + ;
						"' AND SF2B.F2_DOC = SF2.F2_DOC AND SF2B.F2_SERIE IN "+FormatIn(cSeriMigrada,"/") +;
						" AND SF2B.F2_CLIENTE = SF2.F2_CLIENTE AND SF2B.F2_LOJA = SF2.F2_LOJA) = 0 "
				EndIf
			Endif

			cQuery := ChangeQuery(cQuery)

			dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQryComp+cQuery), "T_F2" , .F., .T.)

			While T_F2->(!Eof())

				nPos := aScan(aDados,{|x| 	AllTrim(x[1])			+AllTrim(x[2])				+AllTrim(x[6])			+AllTrim(x[7])	== ;
					AllTrim(aSigaMat[nV,1])	+AllTrim(T_F2->F2_FILIAL)	+AllTrim(T_F2->F2_DOC)	+AllTrim(T_F2->F2_SERIE)})

				If nPos == 0

					aAdd(aDados,{ AllTrim(aSigaMat[nV,1]) ,;
						AllTrim(T_F2->F2_FILIAL)		,;
						aSigaMat[nV,3]					,;
						T_F2->F2_CLIENTE				,;
						AllTrim(T_F2->F2_DOC)			,;
						AllTrim(T_F2->F2_SERIE)			,;
						T_F2->F2_NFELETR				,;
						DtoC(StoD(T_F2->F2_EMISSAO))	,;
						DtoC(StoD(T_F2->E1_VENCREA))    })
					nPos := Len(aDados)
				EndIf

				T_F2->(dbSkip())

			EndDo

			T_F2->(dbCloseArea())

			cQuery := ""
		Next nV

		If Len(aDados) == 0
			aAdd(aDados,{"","","","","","","","",""})
		EndIf

		aSort(aDados,,,{|x,y| CtoD(X[8]) > CtoD(y[8]) })

		For nx := 1 To Len(aDados)

			oCabecalho:=wsclassnew("o_cabecalho")
			oCabecalho:co_empresa	   := aDados[nx,01]  //AllTrim(aSigaMat[nV,1]
			oCabecalho:co_filial	   := aDados[nx,02]  //AllTrim(T_F2->F2_FILIAL)
			oCabecalho:co_unidade	   := aDados[nx,03]  //aSigaMat[nV,3]
			oCabecalho:co_cliente	   := aDados[nx,04]  //T_F2->F2_CLIENTE
			oCabecalho:co_documento	   := aDados[nx,05]  //AllTrim(T_F2->F2_DOC)
			oCabecalho:co_serie		   := aDados[nx,06]  //AllTrim(T_F2->F2_SERIE)
			oCabecalho:co_nf_eletronica:= aDados[nx,07]  //T_F2->F2_NFELETR
			oCabecalho:co_emissao	   := aDados[nx,08]  //DtoC(StoD(T_F2->F2_EMISSAO))
			oCabecalho:co_vencimento   := aDados[nx,09]  //DtoC(StoD(T_F2->E1_VENCREA))
			cSupplier				   := U_TFX005STA(oCabecalho:co_filial,oCabecalho:co_documento,oCabecalho:co_serie,oCabecalho:co_cliente,c_loja,'Z',.F.,.F.) //Indica se a NF é Supplier ou nao
			If cSupplier $ cStaSup
				cSupplier:="S"
			Else
				cSupplier:="N"
			EndIF
			oCabecalho:co_supplier     :=cSupplier
			aAdd(::o_cabec,oCabecalho)

		Next nx
	EndIf	

/*If Select ("T_F2") >0
	T_F2->(dbCloseArea())
EndIf*/

U_xCONOUT("AUTOATENDIMENTO","CONS_CABECALHO","RETORNO","")

Return .T.


/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³CONF_FORNEC		³ Autor ³ Humberto Reis	   ³ Data ³ 12/08/13 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/

WsMethod CONF_FORNEC WsReceive cn_CNPJ  WsSend ln_exist WsService AUTOATENDIMENTO

	Local aSigaMat   := WS_Sw_SigaMat()
	Local cQuery     := ""
	Local cEmpTemp   := ""
	Local cFilTemp   := ""

	Local nV         := 0

	::ln_exist:= .F.
	U_xCONOUT("AUTOATENDIMENTO","CONF_FORNEC","REQUISICAO","")

	For nV := 1 To Len(aSigaMat)

		cEmpTemp   := aSigaMat[nV,1]
		cFilTemp   := aSigaMat[nV,2]

		cQuery := "SELECT SA2.* " 	+CRLF
		cQuery += "FROM "+RetFullName("SA2",cEmpTemp)+" SA2 "	+CRLF
		cQuery += "WHERE " 									+CRLF
		cQuery += "A2_FILIAL = "+cFilTemp+" AND "   		+CRLF
		cQuery += "A2_CGC = "+ ::cn_CNPJ +" AND "   		+CRLF
		cQuery += "SA2.D_E_L_E_T_ <> '*' "					+CRLF

		cQuery := ChangeQuery(cQuery)
		dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQuery), "T_A2" , .F., .T.)

		If T_E1->(!Eof())

			::ln_exist:= .T.

		EndIf

	Next

	U_xCONOUT("AUTOATENDIMENTO","CONF_FORNEC","RETORNO","")

Return .T.

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ EMP_FILIAL  ³ Autor ³ Humberto Reis   	| Data ³ 10/09/13 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/

WsMethod EMP_FILIAL WsReceive cm_marca WsSend am_empfil WsService AUTOATENDIMENTO

	Local nV := 0
	Local nX := 0
	Local aDados	   := {}
	Local aSigaMat     := WS_Sw_SigaMat()

	Public oMsgItem3

	U_xCONOUT("AUTOATENDIMENTO","EMP_FILIAL","REQUISICAO","")

	RpcClearEnv()
	RpcSetType(2)
	RpcSetEnv("00","00001000100")

	For nV := 1 To Len(aSigaMat)

		aAdd(aDados,Array(3))
		aDados[Len(aDados),01]	:= aSigaMat[nV,1] //_WS_EMPRESA
		aDados[Len(aDados),02]	:= aSigaMat[nV,2] //_WS_FILIAL
		aDados[Len(aDados),03]	:= aSigaMat[nV,3] //_WS_UNIDADE

	Next

	For nX := 1 To Len(aDados)

		oListEmpresas := wsclassnew("empfil")
		oListEmpresas:cm_empresa	:= aDados[nX,1]
		oListEmpresas:cm_filial		:= aDados[nX,2]
		oListEmpresas:cm_unidade	:= aDados[nX,3]

		aAdd(::am_empfil ,oListEmpresas)
	Next

	U_xCONOUT("AUTOATENDIMENTO","EMP_FILIAL","RETORNO","")

Return .T.
/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ STAT_VIAGEM ³ Autor ³ Humberto Reis   	| Data ³ 10/09/13 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/

WsMethod STAT_VIAGEM WsReceive cl_login, cl_prest, c_empresa, c_filial WsSend a_stviagem WsService AUTOATENDIMENTO

	Local cLogViag

	RpcClearEnv()
	RpcSetType(2)
	RpcSetEnv(c_empresa,c_filial, "PROTHEUS-AUTO","AP710")

	::a_stviagem:cl_existe := .F.
	::a_stviagem:cl_alterado := .F.
	U_xCONOUT("AUTOATENDIMENTO","STAT_VIAGEM","REQUISICAO","")

	dbSelectArea("LHT")
	dbSetOrder(6)

	If LHT->(dbSeek(xFilial("LHT")+AllTrim(cl_login)))
		cLogViag:= LHT->LHT_CODMAT
	EndIf

	dbSelectArea("LHQ")
	dbSetOrder(1)
	dbSeek(xFilial("LHQ")+AllTrim(cl_prest))

	If LHQ->LHQ_FUNC == cLogViag
		::a_stviagem:cl_existe := .T.
		If !Empty(LHQ->LHQ_SOLICI).And. LHQ->LHQ_FLAG == "E"
			LHQ->( RecLock( "LHQ", .F. ) )
			LHQ->LHQ_FLAG := "S"
			LHQ->(MsUnLock())
			::a_stviagem:cl_alterado := .T.
		ElseIf Empty(LHQ->LHQ_SOLICI).And. LHQ->LHQ_FLAG == "E"
			LHQ->( RecLock( "LHQ", .F. ) )
			LHQ->LHQ_FLAG := "N"
			LHQ->(MsUnLock())
			::a_stviagem:cl_alterado := .T.
		EndIf
	EndIf

	U_xCONOUT("AUTOATENDIMENTO","STAT_VIAGEM","RETORNO","")

Return .T.


/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³CONTAS_PAGAR	    ³ Autor ³ Humberto Reis	   ³ Data ³ 12/08/13 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONTAS_PAGAR WsReceive c_CGCforn, cj_vencde, cj_vencate  WsSend aj_pagtos WsService AUTOATENDIMENTO

	Local aSigaMat   := WS_Sw_SigaMat()
	Local aDados     := {}
	Local cQuery     := ""
	Local cEmpTemp   := ""
	Local cFilTemp   := ""
	Local nPos	    := 0
	Local nV          := 0
	Local nX          := 0
	Local nTotalAbat := 0
	Local nPagtoAnt	:= 0
	Local oCabecalho
	Local oItens

	Prepare Environment Empresa "00" Filial "00001000100"
	U_xCONOUT("AUTOATENDIMENTO","CONTAS_PAGAR","REQUISICAO","")

	For nV := 1 To Len(aSigaMat)

		cEmpTemp   := aSigaMat[nV,1]
		cFilTemp   := aSigaMat[nV,2]

		cQuery := "SELECT  A2_FILIAL,A2_COD,A2_NOME,A2_NREDUZ,A2_LOJA,SE2.* " 	+CRLF
		cQuery += "FROM "+RetFullName("SA2",cEmpTemp)+" SA2, "+RetFullName("SE2",cEmpTemp)+" SE2 "	+CRLF
		cQuery += "LEFT OUTER JOIN "+RetFullName("SED",cEmpTemp)+" SED " +CRLF
		cQuery += "ON (ED_FILIAL 	=  '"+cFilTemp+"' AND " +CRLF
		cQuery += "    ED_CODIGO 	=	E2_NATUREZ AND "	+CRLF
		cQuery += "    SED.D_E_L_E_T_ <> '*' ) "    		+CRLF
		cQuery += "WHERE " 									+CRLF
		cQuery += "A2_FILIAL = "+cFilTemp+" AND "   		+CRLF
		cQuery += "E2_FILIAL = "+cFilTemp+" AND"   			+CRLF
		cQuery += "E2_FORNECE 	= A2_COD AND "   	+CRLF
		cQuery += "E2_LOJA 	= A2_LOJA AND "   		+CRLF
		cQuery += "A2_CGC = '"+ ::c_CGCforn+"' AND" +CRLF
		cQuery += "E2_VENCTO BETWEEN '"+ ::cj_vencde+"' AND '" + ::cj_vencate+"'" +CRLF
		cQuery += "AND SE2.D_E_L_E_T_ <> '*'"	+CRLF
		cQuery += "AND SA2.D_E_L_E_T_ <> '*'"	+CRLF


		cQuery := ChangeQuery(cQuery)

		dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQuery), "T_E2" , .F., .T.)

		dbSelectArea("T_E2")
		dbGoTop()

		While T_E2->(!Eof())


			aAdd(aDados,{aSigaMat[nV,1],T_E2->A2_FILIAL,T_E2->A2_COD,T_E2->A2_NOME,{}})
			nPos := Len(aDados)


			aAdd(aDados[nPos,5],{	AllTrim(T_E2->E2_PREFIXO)+"-"+AllTrim(T_E2->E2_NUM)		,;
				AllTrim(T_E2->E2_PREFIXO)+"-"+AllTrim(T_E2->E2_TIPO)	,;
				Transform(T_E2->E2_VALOR,"@E 999,999,999.99")  			,;
				DTOC(StoD(T_E2->E2_EMISSAO))							,;
				DTOC(StoD(T_E2->E2_VENCTO))								,;
				DTOC(StoD(T_E2->E2_BAIXA))								,;
				Transform((T_E2->E2_COFINS+T_E2->E2_PIS+T_E2->E2_CSLL),"@E 999,999,999.99") ,;
				Transform(T_E2->E2_DESCONT,"@E 999,999,999.99")  		,;
				Transform(nTotalAbat		,"@E 999,999,999.99")  		,; //mantido padrao 0 para desenvolvimento inicial
			Transform(T_E2->E2_JUROS,"@E 999,999,999.99")  			,;
				Transform(T_E2->E2_MULTA,"@E 999,999,999.99")  			,;
				Transform(T_E2->E2_CORREC,"@E 999,999,999.99")  		,;
				Transform(T_E2->E2_VALLIQ,"@E 999,999,999.99")  		,;
				Transform(nPagtoAnt		,"@E 999,999,999.99")  			,; //mantido padrao 0 para desenvolvimento inicial
			Transform(T_E2->E2_SALDO,"@E 999,999,999.99")  			})

			T_E2->(dbSkip())

		EndDo

		T_E2->(dbCloseArea())

//	RpcClearEnv()
//	RpcSetType(2)

	Next

	If nPos == 0
		aAdd(aDados,{"","","","",{}})
		aAdd(aDados[1,5],{"",""	,"0","  /  /  ","  /  /  ","  /  /  ","0","0","0","0","0","0","0","0","0"})
	EndIf

	For nx := 1 To Len(aDados)

		oCabecalho:=wsclassnew("listapagtos")

		oCabecalho:cj_empresa	   	:= aDados[nx,1]
		oCabecalho:cj_filial	   	:= aDados[nx,2]
		oCabecalho:cj_codfornec	   	:= aDados[nx,3]
		oCabecalho:cj_descrforn	   	:= aDados[nx,4]
		oCabecalho:aj_itens_pagto:= {}

		For nv := 1 To Len(aDados[nx,5])
			oItens:=wsclassnew("itens_pagto")
			oItens:cj_prfnro 		:= aDados[nx,5,nv,01] //AllTrim(T_E2->E2_PREFIXO)+"-"+AllTrim(T_E2->E2_NUM)		,;
				oItens:cj_pctip			:= aDados[nx,5,nv,02] //AllTrim(T_E2->E2_PREFIXO)+"-"+AllTrim(T_E2->E2_TIPO)	,;
				oItens:cj_valorig   	:= aDados[nx,5,nv,03] //Transform(T_E2->E2_VALOR,"@E 999,999,999.99")  			,;
				oItens:cj_emissao		:= aDados[nx,5,nv,04] //DTOC(StoD(T_E2->E2_EMISSAO))							,;
				oItens:cj_vencto   		:= aDados[nx,5,nv,05] //DTOC(StoD(T_E2->E2_VENCTO))								,;
				oItens:cj_baixa  		:= aDados[nx,5,nv,06] //DTOC(StoD(T_E2->E2_BAIXA))								,;
				oItens:cj_lei10925		:= aDados[nx,5,nv,07] //Transform((SE2->E2_COFINS+SE2->E2_PIS+SE2->E2_CSLL),"@E 999,999,999.99") ,;
				oItens:cj_descontos		:= aDados[nx,5,nv,08] //Transform(T_E2->E2_DESCONT,"@E 999,999,999.99")  		,;
				oItens:cj_abatimentos	:= aDados[nx,5,nv,09] //Transform(nTotalAbat		,"@E 999,999,999.99")  		,;
				oItens:cj_juros			:= aDados[nx,5,nv,10] //Transform(T_E2->E2_JUROS,"@E 999,999,999.99")  			,;
				oItens:cj_multa   		:= aDados[nx,5,nv,11] //Transform(T_E2->E2_MULTA,"@E 999,999,999.99")  			,;
				oItens:cj_corrmonet		:= aDados[nx,5,nv,12] //Transform(T_E2->E2_CORREC,"@E 999,999,999.99")  		,;
				oItens:cj_valpago		:= aDados[nx,5,nv,13] //Transform(T_E2->E2_VALLIQ,"@E 999,999,999.99")  		,;
				oItens:cj_pagtoantec	:= aDados[nx,5,nv,14]
			oItens:cj_saldoatu		:= aDados[nx,5,nv,15] //Transform(T_E2->E2_SALDO,"@E 999,999,999.99")  			,;

			aAdd(oCabecalho:aj_itens_pagto,oItens)
		Next nv

		aAdd(::aj_pagtos,oCabecalho)

	Next nx

	U_xCONOUT("AUTOATENDIMENTO","CONTAS_PAGAR","RETORNO","")

Return .T.

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ PROX_VENCTO ³ Autor ³ TDI Sistemas       | Data ³ 29/05/14 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ Traz os proximos vencimentos perto da database do sistema  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod PROX_VENCTO WsReceive c_cliente, c_loja WsSend o_cabec WsService AUTOATENDIMENTO
	Local cQuery     	:= ""
	Local cQryComp   	:= ""
	Local cEmpTemp   	:= ""
	Local cFilTemp   	:= ""
	//Local cDatOld     := ""
	Local nPos	   	    := 0
	Local nV         	:= 0
	Local nX         	:= 0
	Local aSigaMat   	:= WS_Sw_SigaMat()
	Local aDados     	:= {}
	Local oCabecalho	:= NIL
	Local lAlter		:= GetMv("TI_S01USDT",,.T.)
	Local cDatas 		:= GetMv("TI_S01DATA",,"20000101|20491231")
	Local aDatas 		:= Separa(cDatas, "|", .T.)
	Local cSupplier     := ""
	Local cStaSup	    := SuperGetMv("TI_SUPSTA", , "8")
	
	Local lAchou		:= .F. 
	Local cod_cliente   := c_cliente
	Local cod_loja      := c_loja
	Local cParDime		:= SuperGetMv("TI_DIMEON",,"2")
	Local cEmpOld       := "" 
	
	Public oMsgItem3

	If cParDime =='1'
		U_xCONOUT("AUTOATENDIMENTO","PROX_VENCTO","REQUISICAO","")

		For nV := 1 To Len(aSigaMat)
			cEmpTemp := aSigaMat[nV,1]
			cFilTemp := aSigaMat[nV,2]

			If cEmpOld <> cEmpTemp 
				RpcClearEnv()
				RpcSetEnv(cEmpTemp,cFilTemp)//para que TCRMX029 varre a zx5 da respectiva empresa adiante
				
				cod_cliente :=c_cliente
				cod_loja	:=c_loja
				lAchou      := U_AvalCli(cEmpTemp, @cod_cliente, @cod_loja)
				cEmpOld     := cEmpTemp 
				lAlter		:= RetMvParam("TI_S01USDT")  
				cDatas 		:= RetMvParam("TI_S01DATA")
				
				aDatas 		:= Separa(cDatas, "|", .T.)
				cStaSup	    := RetMvParam("TI_SUPSTA")  
			Else 
				cFilAnt     := cFilTemp 
			EndIf

			SA1->(dbSetOrder(1))
			SA1->(dbSeek(xFilial("SA1")+cod_cliente+cod_loja))
			
			If !lAchou     
				Loop 
			EndIf 

			cQuery += " SELECT '"+ cEmpTemp +"' EMPRESA, '" + aSigaMat[nV,3] +"' UNIDADE, "
			cQuery += " SF2.F2_CLIENTE, SF2.F2_LOJA   , SF2.F2_DOC   , "
			cQuery += " SF2.F2_SERIE  , SF2.F2_NFELETR, SF2.F2_EMISSAO, SF2.F2_FILIAL,SE1.E1_VENCREA "
			cQuery += " FROM  " + RetFullName("SF2",cEmpTemp)+" SF2 "
			cQuery += " INNER JOIN "+ RetFullName("SE1",cEmpTemp)+" SE1 "
			cQuery += " ON E1_NUM = F2_DOC  "
			//cQuery += " AND E1_FILIAL = F2_FILIAL  "/Comentado Joao Gabriel
			cQuery += " AND E1_CLIENTE = F2_CLIENTE  AND E1_LOJA = F2_LOJA AND E1_TIPO = 'NF' AND "
			cQuery += "  E1_SALDO    <> 0 	AND "
			If !lAlter
				cQuery += "  E1_VENCREA  >= '" + DTOS(dDataBase)+ "'	AND "
			Else
				cQuery +=       "SE1.E1_EMISSAO>='"+aDatas[1]+"' AND "
				cQuery +=       "SE1.E1_EMISSAO<='"+aDatas[2]+"' AND "
				cQuery +=       "SE1.E1_VENCREA>='"+aDatas[1]+"' AND "
				cQuery +=       "SE1.E1_VENCREA<='"+aDatas[2]+"' AND "
			EndIf
			cQuery += "  E1_TIPO <> '"+MVPROVIS+"' AND "
			cQuery += "  E1_TIPO <> 'PR ' AND "
			cQuery += "  E1_TIPO <> 'RA ' AND "
			cQuery += "  E1_TIPO NOT LIKE '%-'	AND "
			cQuery += "  SE1.D_E_L_E_T_ <> '*' "
			cQuery += " WHERE "
			cQuery += " SF2.F2_FILIAL  = '"+cFilTemp+"'	AND "
			cQuery += " SF2.F2_CLIENTE = '"+cod_cliente+"'	AND "
			cQuery += " SF2.F2_LOJA    = '"+cod_loja+"' 	AND "
			cQuery += " SF2.D_E_L_E_T_ = ' ' "

			cQuery := ChangeQuery(cQuery)
						
			dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQryComp+cQuery), "T_F2" , .F., .T.)

			While T_F2->(!Eof())

				nPos := aScan( aDados, {|x| x[1]+x[2]+x[3]+x[6]+x[7] == AllTrim(aSigaMat[nV,1])+AllTrim(T_F2->F2_FILIAL)+aSigaMat[nV,3]+AllTrim(T_F2->F2_SERIE)+T_F2->F2_NFELETR})

				If !Empty(T_F2->E1_VENCREA) .And. nPos == 0

					aAdd(aDados,{ 	AllTrim(aSigaMat[nV,1]) 		,;  //1
					AllTrim(T_F2->F2_FILIAL)		,;  //2
					aSigaMat[nV,3]					,;  //3
					T_F2->F2_CLIENTE				,;  //4
					AllTrim(T_F2->F2_DOC)			,;  //5
					AllTrim(T_F2->F2_SERIE)			,;  //6
					T_F2->F2_NFELETR				,;  //7
					DtoC(StoD(T_F2->F2_EMISSAO))	,;  //8
					DtoC(StoD(T_F2->E1_VENCREA))    ,;  //9
					U_TFX005STA(AllTrim(T_F2->F2_FILIAL) , AllTrim(T_F2->F2_DOC), AllTrim(T_F2->F2_SERIE), T_F2->F2_CLIENTE,c_loja,'Z',.F.,.F.) }) //10 Indica se a NF é Supplier ou nao
					
				EndIf

				T_F2->(dbSkip())
			EndDo

				T_F2->(dbCloseArea())
			 
			cQuery := ""
		Next nV

		If Len(aDados) == 0
			aAdd(aDados,{"","","","","","","","","",""})
		EndIf

		aSort(aDados,,,{|x,y| CtoD(X[9]) < CtoD(y[9]) })

		For nx := 1 To Len(aDados)
			//If Empty(cDatOld) .Or. aDados[nx,09] == cDatOld
			//	cDatOld := aDados[nx,09]
			oCabecalho:=wsclassnew("o_cabecalho")
			oCabecalho:co_empresa	    := aDados[nx,01]  //AllTrim(aSigaMat[nV,1]
			oCabecalho:co_filial	    := aDados[nx,02]  //AllTrim(T_F2->F2_FILIAL)
			oCabecalho:co_unidade	    := aDados[nx,03]  //aSigaMat[nV,3]
			oCabecalho:co_cliente	    := aDados[nx,04]  //T_F2->F2_CLIENTE
			oCabecalho:co_documento	    := aDados[nx,05]  //AllTrim(T_F2->F2_DOC)
			oCabecalho:co_serie		    := aDados[nx,06]  //AllTrim(T_F2->F2_SERIE)
			oCabecalho:co_nf_eletronica := aDados[nx,07]  //T_F2->F2_NFELETR
			oCabecalho:co_emissao	    := aDados[nx,08]  //DtoC(StoD(T_F2->F2_EMISSAO))
			oCabecalho:co_vencimento    := aDados[nx,09]  //DtoC(StoD(T_F2->E1_VENCREA))
			cSupplier				    := aDados[nx,10]  //Indica se a NF é Supplier ou nao, Calculado no loop de preparo da empresa
			
			If cSupplier $ cStaSup
				cSupplier:="S"
			Else
				cSupplier:="N"
			EndIF
			oCabecalho:co_supplier     :=cSupplier
			aAdd(::o_cabec,oCabecalho)
			//EndIf
		Next nx
	else
		U_xCONOUT("AUTOATENDIMENTO","PROX_VENCTO","REQUISICAO","")

		For nV := 1 To Len(aSigaMat)

			cEmpTemp := aSigaMat[nV,1]
			cFilTemp := aSigaMat[nV,2]

			cQuery += " SELECT '"+ cEmpTemp +"' EMPRESA, '" + aSigaMat[nV,3] +"' UNIDADE, "
			cQuery += " SF2.F2_CLIENTE, SF2.F2_LOJA   , SF2.F2_DOC   , "
			cQuery += " SF2.F2_SERIE  , SF2.F2_NFELETR, SF2.F2_EMISSAO, SF2.F2_FILIAL,SE1.E1_VENCREA "
			cQuery += " FROM  " + RetFullName("SF2",cEmpTemp)+" SF2 "
			cQuery += " INNER JOIN "+ RetFullName("SE1",cEmpTemp)+" SE1 "
			cQuery += " ON E1_FILIAL = F2_FILIAL  "
			cQuery += " AND E1_NUM = F2_DOC AND E1_CLIENTE = F2_CLIENTE  AND E1_LOJA = F2_LOJA AND E1_TIPO = 'NF' AND "
			cQuery += "  E1_SALDO    <> 0 	AND "
			If !lAlter
				cQuery += "  E1_VENCREA  >= '" + DTOS(dDataBase)+ "'	AND "
			Else
				cQuery +=       "SE1.E1_EMISSAO>='"+aDatas[1]+"' AND "
				cQuery +=       "SE1.E1_EMISSAO<='"+aDatas[2]+"' AND "
				cQuery +=       "SE1.E1_VENCREA>='"+aDatas[1]+"' AND "
				cQuery +=       "SE1.E1_VENCREA<='"+aDatas[2]+"' AND "
			EndIf
			cQuery += "  E1_TIPO <> '"+MVPROVIS+"' AND "
			cQuery += "  E1_TIPO <> 'PR ' AND "
			cQuery += "  E1_TIPO <> 'RA ' AND "
			cQuery += "  E1_TIPO NOT LIKE '%-'	AND "
			cQuery += "  SE1.D_E_L_E_T_ <> '*' "
			cQuery += " WHERE "
			cQuery += " SF2.F2_FILIAL  = '"+cFilTemp+"'	AND "
			cQuery += " SF2.F2_CLIENTE = '"+c_cliente+"'	AND "
			cQuery += " SF2.F2_LOJA    = '"+c_loja+"' 	AND "
			cQuery += " SF2.D_E_L_E_T_ = ' ' "

			cQuery := ChangeQuery(cQuery)

			dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQryComp+cQuery), "T_F2" , .F., .T.)

			While T_F2->(!Eof())

				nPos := aScan( aDados, {|x| x[1]+x[2]+x[3]+x[6]+x[7] == AllTrim(aSigaMat[nV,1])+AllTrim(T_F2->F2_FILIAL)+aSigaMat[nV,3]+AllTrim(T_F2->F2_SERIE)+T_F2->F2_NFELETR})

				If !Empty(T_F2->E1_VENCREA) .And. nPos == 0

					aAdd(aDados,{ 	AllTrim(aSigaMat[nV,1]) 		,;  //1
					AllTrim(T_F2->F2_FILIAL)		,;  //2
					aSigaMat[nV,3]					,;  //3
					T_F2->F2_CLIENTE				,;  //4
					AllTrim(T_F2->F2_DOC)			,;  //5
					AllTrim(T_F2->F2_SERIE)			,;  //6
					T_F2->F2_NFELETR				,;  //7
					DtoC(StoD(T_F2->F2_EMISSAO))	,;  //8
					DtoC(StoD(T_F2->E1_VENCREA))    })  //9

				EndIf

				T_F2->(dbSkip())
			EndDo

			T_F2->(dbCloseArea())

			cQuery := ""
		Next nV

		If Len(aDados) == 0
			aAdd(aDados,{"","","","","","","","",""})
		EndIf

		aSort(aDados,,,{|x,y| CtoD(X[9]) < CtoD(y[9]) })

		For nx := 1 To Len(aDados)
			//If Empty(cDatOld) .Or. aDados[nx,09] == cDatOld
			//	cDatOld := aDados[nx,09]
			oCabecalho:=wsclassnew("o_cabecalho")
			oCabecalho:co_empresa	    := aDados[nx,01]  //AllTrim(aSigaMat[nV,1]
			oCabecalho:co_filial	    := aDados[nx,02]  //AllTrim(T_F2->F2_FILIAL)
			oCabecalho:co_unidade	    := aDados[nx,03]  //aSigaMat[nV,3]
			oCabecalho:co_cliente	    := aDados[nx,04]  //T_F2->F2_CLIENTE
			oCabecalho:co_documento	    := aDados[nx,05]  //AllTrim(T_F2->F2_DOC)
			oCabecalho:co_serie		    := aDados[nx,06]  //AllTrim(T_F2->F2_SERIE)
			oCabecalho:co_nf_eletronica := aDados[nx,07]  //T_F2->F2_NFELETR
			oCabecalho:co_emissao	    := aDados[nx,08]  //DtoC(StoD(T_F2->F2_EMISSAO))
			oCabecalho:co_vencimento    := aDados[nx,09]  //DtoC(StoD(T_F2->E1_VENCREA))
			cSupplier				    := U_TFX005STA(oCabecalho:co_filial,oCabecalho:co_documento,oCabecalho:co_serie,oCabecalho:co_cliente,c_loja,'Z',.F.,.F.) //Indica se a NF é Supplier ou nao
			If cSupplier $ cStaSup
				cSupplier:="S"
			Else
				cSupplier:="N"
			EndIF
			oCabecalho:co_supplier     :=cSupplier
			aAdd(::o_cabec,oCabecalho)
			//EndIf
		Next nx
	EndIf
	U_xCONOUT("AUTOATENDIMENTO","PROX_VENCTO","RETORNO","")

Return .T.

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ CONS_PENDENCIAS  ³ Autor ³ Humberto Reis    ³ Data ³ 15/04/13 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/

WsMethod CONS_PENDENCIAS WsReceive c_cliente, c_loja WsSend ab_titulo2 WsService AUTOATENDIMENTO

	Local _WS_EMPRESA  	:= ""
	Local _WS_FILIAL   	:= ""
	Local _WS_UNIDADE  	:= ""
	Local _WS_NOME     	:= ""
	Local _WS_PREFIXO  	:= ""
	Local _WS_NUMERO   	:= ""
	Local _WS_PARCELA  	:= ""
	Local _WS_TIPO     	:= ""
	Local _WS_EMISSAO  	:= ""
	Local _WS_VENCTO   	:= ""
	Local _WS_PRORROG  	:= ""
	Local _WS_VALFAT   	:= 0
	Local _WS_VALBAIXA 	:= 0
	Local _WS_IR       	:= 0
	Local _WS_PIS      	:= 0
	Local _WS_COFINS   	:= 0
	Local _WS_CSLL     	:= 0
	Local _WS_INSS     	:= 0
	Local _WS_OBSERV   	:= ""
	Local _WS_NFE      	:= ""
	Local _WS_EMPTIT   	:= ""
	Local _WS_FILTIT   	:= ""
	Local _WS_CGC      	:= ""
	Local _WS_NOMECOM  	:= ""
	Local cGETEMPR		:=	""

	Local lVerFil      	:= .T.
	Local lCancelado   	:= .F.
	Local lOutraNf     	:= .F.

	Local nPos		   	:= 0
	Local nMoeda	   	:= 0
	Local ndecs 	   	:= 0
	Local nSaldo       	:= 0
	Local nTotAbat     	:= 0
	Local nVezes       	:= 0
	Local nX           	:= 0
	Local nV           	:= 0

	Local aDados	   	:= {}
	Local aValor       	:= {}
	Local aFiliais     	:= {}
	Local aEmpresas    	:= {}
	Local aSigaMat     	:= WS_Sw_SigaMat()
	Local cod_cliente := c_cliente
	Local cod_loja     :=c_loja

	Local oListTitulos	:= NIL
	Local lPSGETEMPR   	:= GetMV('PS_GETEMPR',,.t.)

	Local lAlter		:= GetMv("TI_S02USDT",,.T.)
	Local cDatas 		:= GetMv("TI_S01DATA",,"20000101|20491231")
	Local aDatas 		:= Separa(cDatas, "|", .T.)
	Local lAchou		:= .F.
	Local cParDime		:= SuperGetMv("TI_DIMEON",,"2")//Pega apenas da primeira empresa que Logou WS
	Local cEmpOld		:= ""
	Public oMsgItem3	:= NIL
		
	If cParDime =='1'
		
		U_xCONOUT("AUTOATENDIMENTO","CONS_PENDENCIAS","REQUISICAO","")

		For nV := 1 To Len(aSigaMat) 
			_WS_EMPRESA := aSigaMat[nV,1]
			_WS_FILIAL  := aSigaMat[nV,2]
			_WS_UNIDADE := aSigaMat[nV,3]
			
			If _WS_EMPRESA <> cEmpOld 
				RpcClearEnv()
				RpcSetEnv(_WS_EMPRESA,_WS_FILIAL)//WS traz cFilant em branco,extraimos de cNumEmp
				lPSGETEMPR  := RetMvParam('PS_GETEMPR')
				lAlter		:= RetMvParam("TI_S02USDT")
				cDatas 		:= RetMvParam("TI_S01DATA")
				aDatas 		:= Separa(cDatas, "|", .T.)
				cod_cliente := c_cliente
				cod_loja    := c_loja
				cEmpOld     := _WS_EMPRESA 						
				lAchou := U_AvalCli(_WS_EMPRESA, @cod_cliente, @cod_loja)
			Else 
				cFilAnt := _WS_FILIAL 
			EndIf 
								
			SA1->(dbSetOrder(1))
			SA1->(dbSeek(xFilial("SA1")+cod_cliente+cod_loja))
			If !lAchou     
				Loop 
			EndIf 

			cQuery := "SELECT "
			cQuery += "SE1.E1_FILIAL, SE1.E1_CLIENTE, SE1.E1_LOJA, SE1.E1_PREFIXO, SE1.E1_NUM, SE1.E1_PARCELA, SE1.E1_TIPO, SE1.E1_NOMCLI, "
			cQuery += "SE1.E1_NATUREZ, SE1.E1_SITUACA, SE1.E1_PORTADO, SE1.E1_SALDO, SE1.E1_MOEDA, SE1.E1_RECIBO, SE1.E1_ORIGEM, "
			cQuery += "SE1.E1_EMISSAO, SE1.E1_VENCREA, SE1.E1_BAIXA, SE1.E1_FATURA, SE1.E1_DTFATUR, SE1.E1_VALLIQ, "
			cQuery += "SE1.E1_VALOR,SE1.E1_SDACRES,SE1.E1_SDDECRE,SE1.E1_TXMOEDA,SE1.E1_ACRESC,SE1.E1_DECRESC,SE1.R_E_C_N_O_ RECNO, "
			cQuery += "SE1.E1_INSS, SE1.E1_CSLL, SE1.E1_COFINS, SE1.E1_PIS, SE1.E1_IRRF, E1_FILORIG, "
			cQuery += "SA1.A1_COD, SA1.A1_LOJA, SA1.A1_NOME, SA1.A1_NREDUZ, SA1.A1_CGC "

			cQuery += "FROM "
			cQuery += RetFullName("SE1",_WS_EMPRESA)+" SE1, "
			cQuery += RetFullName("SA1",_WS_EMPRESA)+" SA1  "
			
			cQuery += "WHERE "
			cQuery += "SA1.A1_COD		= SE1.E1_CLIENTE			AND "
			cQuery += "SA1.A1_LOJA		= SE1.E1_LOJA				AND "
			cQuery += "SE1.E1_FILIAL   	= '" +_WS_FILIAL+ "' 	AND "
			cQuery += "SE1.E1_CLIENTE 	= '" +cod_cliente	+ "' 		AND "
			cQuery += "SE1.E1_LOJA    	= '" +cod_loja	+ "' 		AND "
			cQuery += "SE1.E1_SALDO    	<> 0 		AND "

			If !lAlter
				cQuery += "SE1.E1_VENCREA  <= '" + DTOS(dDataBase)+ "'	AND "
			Else
				cQuery +=       "SE1.E1_EMISSAO>='"+aDatas[1]+"' AND "
				cQuery +=       "SE1.E1_EMISSAO<='"+aDatas[2]+"' AND "
				cQuery +=       "SE1.E1_VENCREA>='"+aDatas[1]+"' AND "
				cQuery +=       "SE1.E1_VENCREA<='"+aDatas[2]+"' AND "
			EndIf

			cQuery += "SE1.E1_TIPO <> '"+MVPROVIS+"' AND "
			cQuery += "SE1.E1_TIPO <> 'PR ' AND "
			cQuery += "SE1.E1_TIPO <> 'RA ' AND "

			cQuery += "SE1.E1_TIPO NOT LIKE '%-'	AND "
			cQuery += "SE1.D_E_L_E_T_ <> '*'		AND "
			cQuery += "SA1.D_E_L_E_T_ <> '*' 			"

			cQuery += " ORDER BY "

			cQuery += "SE1.E1_VENCREA,SE1.E1_CLIENTE, SE1.E1_LOJA ,"
			cQuery += "SE1.E1_PREFIXO, SE1.E1_NUM, SE1.E1_PARCELA, SE1.E1_TIPO "
			cGETEMPR := cQuery
			cQuery := ChangeQuery(cQuery)

			dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_E1",.T.,.T.)

			//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
			//³ Carrega array para trabalho ³
			//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
			dbSelectArea("T_E1")
			dbGoTop()

			If T_E1->(!Eof())
				// Tratamento para alias aberto antes do getEmpr
				// getEmpr eh uma função do aplib090, ela dah um dbcloseAll, fechando todos os alias
				if lPSGETEMPR
					if select('T_E1') <= 0
						cGETEMPR := ChangeQuery(cGETEMPR)
						dbUseArea(.T.,"TOPCONN",TcGenQry(,,cGETEMPR),"T_E1",.T.,.T.)
					endif
				endif

				nMoeda	  := 1
				ndecs 	  := Msdecimais(1)
				aEmpresas := VerEmpresas()
				aFiliais  := FinRetFil()
				aValor    := {}
			EndIf

			dbSelectArea("T_E1")
			dbGoTop()

			While T_E1->(!Eof())
				SE1->(dbgoto(T_E1->RECNO))
				
				nVezes := 0
				
				aValor := Baixas(T_E1->E1_NATUREZ,T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,nMoeda,"R",T_E1->E1_CLIENTE,dDataBase,T_E1->E1_LOJA,,,,.T., @lCancelado)
				
				If lCancelado
					T_E1->(dbSkip())
					Loop
				EndIf
				
				lVerFil := (!Empty(xFilial("SE1")) .And. !Empty(xFilial("SE5")) .And. Len(aFiliais) > 1)

				If lVerFil .And. !Empty(T_E1->E1_BAIXA) .And. aValor[11] == 0
					F340VerBxFil( @aValor, aFiliais, nMoeda )
				EndIf
				nSaldo := SaldoTit(T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,T_E1->E1_NATUREZ,"R",T_E1->E1_CLIENTE,nMoeda,dDataBase,,T_E1->E1_LOJA,,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0))
				
				If lVerFil .And. nSaldo > 0
					nSaldo -= Round(NoRound(xMoeda(FRVlCompFil("R",T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,T_E1->E1_CLIENTE,T_E1->E1_LOJA,,aFiliais),;
						T_E1->E1_MOEDA,nMoeda,dDataBase,ndecs+1,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0) ),nDecs+1),nDecs)
				EndIf

				If T_E1->E1_VALOR <> T_E1->E1_VALLIQ .Or. T_E1->(E1_CSLL+E1_COFINS+E1_PIS+E1_IRRF+E1_INSS) > 0
					nTotAbat := SomaAbat(T_E1->E1_PREFIXO, T_E1->E1_NUM, T_E1->E1_PARCELA, "R", nMoeda )
				Else
					nTotAbat := 0
				EndIf

				If !(T_E1->E1_TIPO $ MVRECANT+"/"+MV_CRNEG) .And. nSaldo<>0 // nao deve olhar abatimento pois e zerado o saldo na liquidacao final do titulo
					nSaldo -= nTotAbat
				EndIf

				If Str(T_E1->E1_VALOR,17,2) == Str(nSaldo,17,2) .And. T_E1->E1_DECRESC > 0 .And. T_E1->E1_SDDECRE == 0
					aValor[2] += T_E1->E1_DECRESC
					nSaldo -= T_E1->E1_DECRESC
				Endif

				If Str(T_E1->E1_VALOR,17,2) == Str(nSaldo,17,2) .And. T_E1->E1_ACRESC > 0 .And. T_E1->E1_SDACRES == 0
					aValor[3] += T_E1->E1_ACRESC
					nSaldo += T_E1->E1_ACRESC
				Endif

				If T_E1->E1_SALDO > 0 .And. Empty(T_E1->E1_BAIXA)
					aValor[3] += T_E1->E1_ACRESC
					aValor[2] += T_E1->E1_DECRESC
				Else
					aValor[3] += T_E1->E1_SDACRES
					aValor[2] += T_E1->E1_SDDECRE
				EndIf

				//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
				//³ Carrega avariaveis para facilitar manutencao no fonte ³
				//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
				_WS_FILIAL      := T_E1->E1_FILIAL
				_WS_NOME    	:= T_E1->A1_NOME
				_WS_PREFIXO 	:= Iif(!Empty(T_E1->E1_PREFIXO),"'"+T_E1->E1_PREFIXO+"'",CriaVar("E1_PREFIXO"))
				_WS_PREFIXO     := AllTrim(_WS_PREFIXO)
				_WS_NUMERO 		:= Iif(!Empty(T_E1->E1_NUM)    ,"'"+T_E1->E1_NUM    +"'",CriaVar("E1_NUMERO"))
				_WS_PARCELA 	:= Iif(!Empty(T_E1->E1_PARCELA),"'"+T_E1->E1_PARCELA+"'",CriaVar("E1_PARCELA"))
				_WS_TIPO 		:= Iif(!Empty(T_E1->E1_TIPO)   ,"'"+T_E1->E1_TIPO   +"'",CriaVar("E1_TIPO"))

				_WS_EMISSAO 	:= Iif(!Empty(T_E1->E1_EMISSAO),DTOC(STOD(T_E1->E1_EMISSAO)),"  /  /  ")
				_WS_VENCTO 		:= Iif(!Empty(T_E1->E1_VENCREA),DTOC(STOD(T_E1->E1_VENCREA)),"  /  /  ")
				_WS_PRORROG 	:= Iif(!Empty(T_E1->E1_VENCREA),DTOC(STOD(T_E1->E1_VENCREA)),"  /  /  ")

				_WS_VALFAT 		:= xMoeda(T_E1->E1_VALOR,T_E1->E1_MOEDA,nMoeda,T_E1->E1_EMISSAO,,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0)) //* Iif(Alltrim(T_E1->E1_TIPO)$(MVRECANT+","+MV_CRNEG),-1,1)
				_WS_VALBAIXA 	:= aValor[5]
				
				_WS_IR 			:= T_E1->E1_IRRF
				_WS_PIS 		:= T_E1->E1_PIS
				_WS_COFINS 		:= T_E1->E1_COFINS
				_WS_CSLL 		:= T_E1->E1_CSLL
				_WS_INSS 		:= T_E1->E1_INSS
				_WS_NFE 		:= AllTrim( Posicione("SF3",5,T_E1->E1_FILIAL+T_E1->(E1_PREFIXO+E1_NUM+E1_CLIENTE+E1_LOJA),"F3_NFELETR") )
				_WS_NFE         := Iif(!Empty(_WS_NFE),_WS_NFE,CriaVar("F3_NFELETR"))
				_WS_OBSERV 		:= " "

				_WS_EMPTIT 		:= _WS_EMPRESA
				_WS_FILTIT 		:= T_E1->E1_FILIAL
				nPosFil         := aScan(aEmpresas,{ |x| AllTrim(x[1])+AllTrim(x[2]) == AllTrim(_WS_EMPTIT)+AllTrim(_WS_FILTIT)})
				_WS_CGC         := aEmpresas[nPosFil,3]
				_WS_NOMECOM     := aEmpresas[nPosFil,4]
				_WS_RECNO       := T_E1->RECNO

				SFQ->(dbSetOrder(1))
				SFQ->(dbSeek(T_E1->E1_FILIAL+"SE1"+T_E1->(E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO+E1_CLIENTE+E1_LOJA)))
				While SFQ->(!Eof()).And. SFQ->(FQ_FILIAL+FQ_ENTORI+FQ_PREFORI+FQ_NUMORI+FQ_PARCORI+FQ_TIPOORI+FQ_CFORI+FQ_LOJAORI)==T_E1->E1_FILIAL+"SE1"+T_E1->(E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO+E1_CLIENTE+E1_LOJA)
					nVezes++
					_WS_OBSERV := _WS_OBSERV+Iif(nVezes>1,", ","")+AllTrim(SFQ->FQ_PREFDES)+"-"+AllTrim(SFQ->FQ_NUMDES)+"/"+AllTrim(SFQ->FQ_PARCDES)
					SFQ->(dbSkip())
				EndDo

				If !Empty(_WS_OBSERV)
					_WS_OBSERV := _WS_OBSERV+", "+AllTrim(T_E1->E1_PREFIXO)+"-"+AllTrim(T_E1->E1_NUM)+"/"+AllTrim(T_E1->E1_PARCELA)
				Else
					_WS_OBSERV := "-"
				EndIf

				lOutraNf := .F.

				cQuery := "SELECT FQ_NUMDES FROM "+RetSqlName("SFQ")+" WHERE "
				cQuery += "FQ_ENTDES  = 'SE1' AND "
				cQuery += "FQ_PREFDES = '"+T_E1->E1_PREFIXO	+"'	AND "
				cQuery += "FQ_NUMDES  = '"+T_E1->E1_NUM		+"'	AND "
				cQuery += "FQ_PARCDES = '"+T_E1->E1_PARCELA	+"'	AND "
				cQuery += "FQ_TIPODES = '"+T_E1->E1_TIPO	+"'	AND	"
				cQuery += "FQ_CFDES   = '"+T_E1->E1_CLIENTE	+"'	AND "
				cQuery += "FQ_LOJADES = '"+T_E1->E1_LOJA	+"'	AND "
				cQuery += "D_E_L_E_T_ <> '*'"

				cQuery := ChangeQuery(cQuery)

				dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_XX",.T.,.T.)

				T_XX->(dbGoTop())
				While T_XX->(!Eof())
					lOutraNf := .T.
					T_XX->(dbSkip())
				EndDo
				T_XX->(dbCloseArea())

				dbSelectArea("T_E1")

				nPos := aScan(aDados,{|x|	x[01] == _WS_VENCTO		.And.;
					X[02] == _WS_NOME 		.And.;
					x[03] == _WS_PREFIXO	.And.;
					x[04] == _WS_NUMERO		.And.;
					x[05] == _WS_PARCELA	.And.;
					x[06] == _WS_TIPO		.And.;
					x[23] == _WS_EMPRESA   	.And.;
					x[24] == _WS_FILIAL		.And.;
					x[25] == _WS_UNIDADE	})

				If nPos == 0
					aAdd(aDados,Array(26))

					aDados[Len(aDados),01]	:= _WS_VENCTO
					aDados[Len(aDados),02]	:= _WS_NOME
					aDados[Len(aDados),03]	:= _WS_PREFIXO
					aDados[Len(aDados),04]	:= _WS_NUMERO
					aDados[Len(aDados),05] 	:= _WS_PARCELA
					aDados[Len(aDados),06] 	:= _WS_TIPO

					aDados[Len(aDados),07] 	:= _WS_EMISSAO
					aDados[Len(aDados),08] 	:= _WS_PRORROG

					aDados[Len(aDados),09] 	:= _WS_VALFAT
					aDados[Len(aDados),10] 	:= _WS_VALBAIXA
					aDados[Len(aDados),11] 	:= _WS_IR
					aDados[Len(aDados),12] 	:= Iif(lOutraNf,0,_WS_PIS)
					aDados[Len(aDados),13] 	:= Iif(lOutraNf,0,_WS_COFINS)
					aDados[Len(aDados),14] 	:= Iif(lOutraNf,0,_WS_CSLL)
					aDados[Len(aDados),15] 	:= _WS_INSS

					aDados[Len(aDados),16] 	:= Iif(!Empty(_WS_NFE),"'"+_WS_NFE+"'","")
					aDados[Len(aDados),17] 	:= _WS_OBSERV
					aDados[Len(aDados),18] 	:= _WS_EMPTIT
					aDados[Len(aDados),19] 	:= _WS_FILTIT
					aDados[Len(aDados),20] 	:= _WS_CGC
					aDados[Len(aDados),21] 	:= _WS_NOMECOM
					aDados[Len(aDados),22] 	:= _WS_RECNO
					aDados[Len(aDados),23] 	:= _WS_EMPRESA
					aDados[Len(aDados),24] 	:= _WS_FILIAL
					aDados[Len(aDados),25] 	:= _WS_UNIDADE
					aDados[Len(aDados),26] 	:= 'N'
				Else
					aDados[nPos,09] += _WS_VALFAT
					aDados[nPos,10] += _WS_VALBAIXA
					aDados[nPos,11] += _WS_IR
					aDados[nPos,12] += Iif(lOutraNf,0,_WS_PIS)
					aDados[nPos,13] += Iif(lOutraNf,0,_WS_COFINS)
					aDados[nPos,14] += Iif(lOutraNf,0,_WS_CSLL)
					aDados[nPos,15] += _WS_INSS
				EndIf

				T_E1->(dbSkip())
			EndDo

			T_E1->(dbCloseArea())
		Next nV

		If Len(aDados) = 0
			aAdd(aDados,Array(26))
			aDados[Len(aDados),01]	:= "  /  /  "
			aDados[Len(aDados),02]	:= "-"
			aDados[Len(aDados),03]	:= ""
			aDados[Len(aDados),04]	:= ""
			aDados[Len(aDados),05] 	:= ""
			aDados[Len(aDados),06] 	:= ""
			aDados[Len(aDados),07] 	:= "  /  /  "
			aDados[Len(aDados),08] 	:= "  /  /  "

			aDados[Len(aDados),09] 	:= 0
			aDados[Len(aDados),10] 	:= 0
			aDados[Len(aDados),11] 	:= 0
			aDados[Len(aDados),12] 	:= 0
			aDados[Len(aDados),13] 	:= 0
			aDados[Len(aDados),14] 	:= 0
			aDados[Len(aDados),15] 	:= 0

			aDados[Len(aDados),16] 	:= ""
			aDados[Len(aDados),17] 	:= ""
			aDados[Len(aDados),18] 	:= ""
			aDados[Len(aDados),19] 	:= ""
			aDados[Len(aDados),20] 	:= ""
			aDados[Len(aDados),21] 	:= ""
			aDados[Len(aDados),22] 	:= 0
			aDados[Len(aDados),23] 	:= ""
			aDados[Len(aDados),24] 	:= ""
			aDados[Len(aDados),25] 	:= ""
			aDados[Len(aDados),26] 	:= ""
		EndIf

		//aSort(aDados,,,{|x,y| CtoD(X[1]) > CtoD(y[1]) .And. X[3]+X[4]+X[5]+X[6] < Y[3]+Y[4]+Y[5]+Y[6]})
		aSort(aDados,,,{|x,y| CtoD(X[1]) < CtoD(y[1])})

		For nX := 1 To Len(aDados)

			oListTitulos := wsclassnew("listtitulos")
			olisttitulos:cb_empresa	 	:= aDados[nX,23]
			olisttitulos:cb_filial		:= aDados[nX,24]
			olisttitulos:cb_unidade		:= aDados[nX,25]
			olisttitulos:cb_vencimento	:= aDados[nX,01]
			olisttitulos:cb_nome		:= AllTrim(aDados[nX,02])
			olisttitulos:cb_prefixo		:= StrTran(StrTran(StrTran(AllTrim(aDados[nX,03]), CHR(10), ""), CHR(32), ""), CHR(9), "")
			olisttitulos:cb_numero		:= aDados[nX,04]
			olisttitulos:cb_parcela		:= aDados[nX,05]
			olisttitulos:cb_tipo		:= aDados[nX,06]
			olisttitulos:cb_emissao		:= aDados[nX,07]
			olisttitulos:cb_prorrogacao	:= aDados[nX,08]
			olisttitulos:cb_original_valor	:= Transform(aDados[nX,09],"@E 999,999,999.99")
			olisttitulos:cb_baixa_valor	:= Transform(aDados[nX,10],"@E 999,999,999.99")
			olisttitulos:cb_ir			:= Transform(aDados[nX,11],"@E 999,999,999.99")
			olisttitulos:cb_pis			:= Transform(aDados[nX,12],"@E 999,999,999.99")
			olisttitulos:cb_cofins		:= Transform(aDados[nX,13],"@E 999,999,999.99")
			olisttitulos:cb_csll		:= Transform(aDados[nX,14],"@E 999,999,999.99")
			olisttitulos:cb_inss		:= Transform(aDados[nX,15],"@E 999,999,999.99")
			olisttitulos:cb_nfe			:= aDados[nX,16]
			olisttitulos:cb_observacao	:= AllTrim(aDados[nX,17])
			olisttitulos:cb_empori		:= aDados[nX,18]
			olisttitulos:cb_filori		:= aDados[nX,19]
			olisttitulos:cb_cnpj		:= aDados[nX,20]
			olisttitulos:cb_nomecom		:= AllTrim(aDados[nX,21])
			olisttitulos:cb_supplier	:= AllTrim(aDados[nX,26])
			olisttitulos:nb_recno  		:= aDados[nX,22]

			aAdd(::ab_titulo2,olisttitulos)
		Next
	else
			RpcClearEnv()
		RpcSetType(2)
		RpcSetEnv("00","00001000100")

		U_xCONOUT("AUTOATENDIMENTO","CONS_PENDENCIAS","REQUISICAO","")

		For nV := 1 To Len(aSigaMat)

			_WS_EMPRESA := aSigaMat[nV,1]
			_WS_FILIAL  := aSigaMat[nV,2]
			_WS_UNIDADE := aSigaMat[nV,3]

			cQuery := "SELECT "
			cQuery += "SE1.E1_FILIAL, SE1.E1_CLIENTE, SE1.E1_LOJA, SE1.E1_PREFIXO, SE1.E1_NUM, SE1.E1_PARCELA, SE1.E1_TIPO, SE1.E1_NOMCLI, "
			cQuery += "SE1.E1_NATUREZ, SE1.E1_SITUACA, SE1.E1_PORTADO, SE1.E1_SALDO, SE1.E1_MOEDA, SE1.E1_RECIBO, SE1.E1_ORIGEM, "
			cQuery += "SE1.E1_EMISSAO, SE1.E1_VENCREA, SE1.E1_BAIXA, SE1.E1_FATURA, SE1.E1_DTFATUR, SE1.E1_VALLIQ, "
			cQuery += "SE1.E1_VALOR,SE1.E1_SDACRES,SE1.E1_SDDECRE,SE1.E1_TXMOEDA,SE1.E1_ACRESC,SE1.E1_DECRESC,SE1.R_E_C_N_O_ RECNO, "
			cQuery += "SE1.E1_INSS, SE1.E1_CSLL, SE1.E1_COFINS, SE1.E1_PIS, SE1.E1_IRRF, E1_FILORIG, "
			cQuery += "SA1.A1_COD, SA1.A1_LOJA, SA1.A1_NOME, SA1.A1_NREDUZ, SA1.A1_CGC "

			cQuery += "FROM "
			cQuery += RetFullName("SE1",_WS_EMPRESA)+" SE1, "
			cQuery += RetFullName("SA1",_WS_EMPRESA)+" SA1  "

			cQuery += "WHERE "
			cQuery += "SA1.A1_COD		= SE1.E1_CLIENTE			AND "
			cQuery += "SA1.A1_LOJA		= SE1.E1_LOJA				AND "
			cQuery += "SE1.E1_FILIAL   	= '" +_WS_FILIAL+ "' 	AND "
			cQuery += "SE1.E1_CLIENTE 	= '" +c_cliente	+ "' 		AND "
			cQuery += "SE1.E1_LOJA    	= '" +c_loja	+ "' 		AND "
			cQuery += "SE1.E1_SALDO    	<> 0 		AND "

			If !lAlter
				cQuery += "SE1.E1_VENCREA  <= '" + DTOS(dDataBase)+ "'	AND "
			Else
				cQuery +=       "SE1.E1_EMISSAO>='"+aDatas[1]+"' AND "
				cQuery +=       "SE1.E1_EMISSAO<='"+aDatas[2]+"' AND "
				cQuery +=       "SE1.E1_VENCREA>='"+aDatas[1]+"' AND "
				cQuery +=       "SE1.E1_VENCREA<='"+aDatas[2]+"' AND "
			EndIf
		//	If cPaisLoc<>"BRA"
		//		cQuery += "SE1.E1_TIPO<>'CH' AND SE1.E1_TIPO<>'TF' AND "
		//	EndIf

			cQuery += "SE1.E1_TIPO <> '"+MVPROVIS+"' AND "
			cQuery += "SE1.E1_TIPO <> 'PR ' AND "
			cQuery += "SE1.E1_TIPO <> 'RA ' AND "

			cQuery += "SE1.E1_TIPO NOT LIKE '%-'	AND "
			cQuery += "SE1.D_E_L_E_T_ <> '*'		AND "
			cQuery += "SA1.D_E_L_E_T_ <> '*' 			"

			cQuery += " ORDER BY "

			cQuery += "SE1.E1_VENCREA,SE1.E1_CLIENTE, SE1.E1_LOJA ,"
			cQuery += "SE1.E1_PREFIXO, SE1.E1_NUM, SE1.E1_PARCELA, SE1.E1_TIPO "
			cGETEMPR := cQuery
			cQuery := ChangeQuery(cQuery)

			dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_E1",.T.,.T.)

			//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
			//³ Carrega array para trabalho ³
			//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
			dbSelectArea("T_E1")
			dbGoTop()

			If T_E1->(!Eof())

				GetEmpr(_WS_EMPRESA+_WS_FILIAL)

				// Tratamento para alias aberto antes do getEmpr
				// getEmpr eh uma função do aplib090, ela dah um dbcloseAll, fechando todos os alias
				if lPSGETEMPR
					if select('T_E1') <= 0
						cGETEMPR := ChangeQuery(cGETEMPR)
						dbUseArea(.T.,"TOPCONN",TcGenQry(,,cGETEMPR),"T_E1",.T.,.T.)
					endif
				endif

				nMoeda	  := 1
				ndecs 	  := Msdecimais(1)
				aEmpresas := VerEmpresas()
				aFiliais  := FinRetFil()
				aValor    := {}
			EndIf


			dbSelectArea("T_E1")
			dbGoTop()

			While T_E1->(!Eof())

				nVezes := 0
				aValor := Baixas(T_E1->E1_NATUREZ,T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,nMoeda,"R",T_E1->E1_CLIENTE,dDataBase,T_E1->E1_LOJA,,,,.T., @lCancelado)

				If lCancelado
					T_E1->(dbSkip())
					Loop
				EndIf

				lVerFil := (!Empty(xFilial("SE1")) .And. !Empty(xFilial("SE5")) .And. Len(aFiliais) > 1)

				If lVerFil .And. !Empty(T_E1->E1_BAIXA) .And. aValor[11] == 0
					F340VerBxFil( @aValor, aFiliais, nMoeda )
				EndIf

				nSaldo := SaldoTit(T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,T_E1->E1_NATUREZ,"R",T_E1->E1_CLIENTE,nMoeda,dDataBase,,T_E1->E1_LOJA,,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0))

				If lVerFil .And. nSaldo > 0
					nSaldo -= Round(NoRound(xMoeda(FRVlCompFil("R",T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,T_E1->E1_CLIENTE,T_E1->E1_LOJA,,aFiliais),;
						T_E1->E1_MOEDA,nMoeda,dDataBase,ndecs+1,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0) ),nDecs+1),nDecs)
				EndIf

				If T_E1->E1_VALOR <> T_E1->E1_VALLIQ .Or. T_E1->(E1_CSLL+E1_COFINS+E1_PIS+E1_IRRF+E1_INSS) > 0
					nTotAbat := SomaAbat(T_E1->E1_PREFIXO, T_E1->E1_NUM, T_E1->E1_PARCELA, "R", nMoeda )
				Else
					nTotAbat := 0
				EndIf

				If !(T_E1->E1_TIPO $ MVRECANT+"/"+MV_CRNEG) .And. nSaldo<>0 // nao deve olhar abatimento pois e zerado o saldo na liquidacao final do titulo
					nSaldo -= nTotAbat
				EndIf

				If Str(T_E1->E1_VALOR,17,2) == Str(nSaldo,17,2) .And. T_E1->E1_DECRESC > 0 .And. T_E1->E1_SDDECRE == 0
					aValor[2] += T_E1->E1_DECRESC
					nSaldo -= T_E1->E1_DECRESC
				Endif

				If Str(T_E1->E1_VALOR,17,2) == Str(nSaldo,17,2) .And. T_E1->E1_ACRESC > 0 .And. T_E1->E1_SDACRES == 0
					aValor[3] += T_E1->E1_ACRESC
					nSaldo += T_E1->E1_ACRESC
				Endif

				If T_E1->E1_SALDO > 0 .And. Empty(T_E1->E1_BAIXA)
					aValor[3] += T_E1->E1_ACRESC
					aValor[2] += T_E1->E1_DECRESC
				Else
					aValor[3] += T_E1->E1_SDACRES
					aValor[2] += T_E1->E1_SDDECRE
				EndIf


				//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
				//³ Carrega avariaveis para facilitar manutencao no fonte ³
				//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
				_WS_FILIAL      := T_E1->E1_FILIAL
				_WS_NOME    	:= T_E1->A1_NOME
				_WS_PREFIXO 	:= Iif(!Empty(T_E1->E1_PREFIXO),"'"+T_E1->E1_PREFIXO+"'",CriaVar("E1_PREFIXO"))
				_WS_PREFIXO     := AllTrim(_WS_PREFIXO)
				_WS_NUMERO 		:= Iif(!Empty(T_E1->E1_NUM)    ,"'"+T_E1->E1_NUM    +"'",CriaVar("E1_NUMERO"))
				_WS_PARCELA 	:= Iif(!Empty(T_E1->E1_PARCELA),"'"+T_E1->E1_PARCELA+"'",CriaVar("E1_PARCELA"))
				_WS_TIPO 		:= Iif(!Empty(T_E1->E1_TIPO)   ,"'"+T_E1->E1_TIPO   +"'",CriaVar("E1_TIPO"))

				_WS_EMISSAO 	:= Iif(!Empty(T_E1->E1_EMISSAO),DTOC(STOD(T_E1->E1_EMISSAO)),"  /  /  ")
				_WS_VENCTO 		:= Iif(!Empty(T_E1->E1_VENCREA),DTOC(STOD(T_E1->E1_VENCREA)),"  /  /  ")
				_WS_PRORROG 	:= Iif(!Empty(T_E1->E1_VENCREA),DTOC(STOD(T_E1->E1_VENCREA)),"  /  /  ")

				_WS_VALFAT 		:= xMoeda(T_E1->E1_VALOR,T_E1->E1_MOEDA,nMoeda,T_E1->E1_EMISSAO,,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0)) //* Iif(Alltrim(T_E1->E1_TIPO)$(MVRECANT+","+MV_CRNEG),-1,1)
				_WS_VALBAIXA 	:= aValor[5]
				_WS_IR 			:= T_E1->E1_IRRF
				_WS_PIS 		:= T_E1->E1_PIS
				_WS_COFINS 		:= T_E1->E1_COFINS
				_WS_CSLL 		:= T_E1->E1_CSLL
				_WS_INSS 		:= T_E1->E1_INSS
				_WS_NFE 		:= AllTrim( Posicione("SF3",5,T_E1->E1_FILIAL+T_E1->(E1_PREFIXO+E1_NUM+E1_CLIENTE+E1_LOJA),"F3_NFELETR") )
				_WS_NFE         := Iif(!Empty(_WS_NFE),_WS_NFE,CriaVar("F3_NFELETR"))
				_WS_OBSERV 		:= " "

				_WS_EMPTIT 		:= SubStr(RetSqlName("SE1"),4,2)
				_WS_FILTIT 		:= T_E1->E1_FILIAL
				nPosFil         := aScan(aEmpresas,{ |x| AllTrim(x[1])+AllTrim(x[2]) == AllTrim(_WS_EMPTIT)+AllTrim(_WS_FILTIT)})
				_WS_CGC         := aEmpresas[nPosFil,3]
				_WS_NOMECOM     := aEmpresas[nPosFil,4]
				_WS_RECNO       := T_E1->RECNO

				SFQ->(dbSetOrder(1))
				SFQ->(dbSeek(T_E1->E1_FILIAL+"SE1"+T_E1->(E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO+E1_CLIENTE+E1_LOJA)))
				While SFQ->(!Eof()).And. SFQ->(FQ_FILIAL+FQ_ENTORI+FQ_PREFORI+FQ_NUMORI+FQ_PARCORI+FQ_TIPOORI+FQ_CFORI+FQ_LOJAORI)==T_E1->E1_FILIAL+"SE1"+T_E1->(E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO+E1_CLIENTE+E1_LOJA)
					nVezes++
					_WS_OBSERV := _WS_OBSERV+Iif(nVezes>1,", ","")+AllTrim(SFQ->FQ_PREFDES)+"-"+AllTrim(SFQ->FQ_NUMDES)+"/"+AllTrim(SFQ->FQ_PARCDES)
					SFQ->(dbSkip())
				EndDo

				If !Empty(_WS_OBSERV)
					_WS_OBSERV := _WS_OBSERV+", "+AllTrim(T_E1->E1_PREFIXO)+"-"+AllTrim(T_E1->E1_NUM)+"/"+AllTrim(T_E1->E1_PARCELA)
				Else
					_WS_OBSERV := "-"
				EndIf

				lOutraNf := .F.

				cQuery := "SELECT FQ_NUMDES FROM "+RetSqlName("SFQ")+" WHERE "
				cQuery += "FQ_ENTDES  = 'SE1' AND "
				cQuery += "FQ_PREFDES = '"+T_E1->E1_PREFIXO	+"'	AND "
				cQuery += "FQ_NUMDES  = '"+T_E1->E1_NUM		+"'	AND "
				cQuery += "FQ_PARCDES = '"+T_E1->E1_PARCELA	+"'	AND "
				cQuery += "FQ_TIPODES = '"+T_E1->E1_TIPO	+"'	AND	"
				cQuery += "FQ_CFDES   = '"+T_E1->E1_CLIENTE	+"'	AND "
				cQuery += "FQ_LOJADES = '"+T_E1->E1_LOJA	+"'	AND "
				cQuery += "D_E_L_E_T_ <> '*'"

				cQuery := ChangeQuery(cQuery)

				dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_XX",.T.,.T.)


				T_XX->(dbGoTop())
				While T_XX->(!Eof())
					lOutraNf := .T.
					T_XX->(dbSkip())
				EndDo
				T_XX->(dbCloseArea())

				dbSelectArea("T_E1")

				nPos := aScan(aDados,{|x|	x[01] == _WS_VENCTO		.And.;
					X[02] == _WS_NOME 		.And.;
					x[03] == _WS_PREFIXO	.And.;
					x[04] == _WS_NUMERO		.And.;
					x[05] == _WS_PARCELA	.And.;
					x[06] == _WS_TIPO		.And.;
					x[23] == _WS_EMPRESA   	.And.;
					x[24] == _WS_FILIAL		.And.;
					x[25] == _WS_UNIDADE	})
				If nPos == 0

					aAdd(aDados,Array(25))

					aDados[Len(aDados),01]	:= _WS_VENCTO
					aDados[Len(aDados),02]	:= _WS_NOME
					aDados[Len(aDados),03]	:= _WS_PREFIXO
					aDados[Len(aDados),04]	:= _WS_NUMERO
					aDados[Len(aDados),05] 	:= _WS_PARCELA
					aDados[Len(aDados),06] 	:= _WS_TIPO

					aDados[Len(aDados),07] 	:= _WS_EMISSAO
					aDados[Len(aDados),08] 	:= _WS_PRORROG

					aDados[Len(aDados),09] 	:= _WS_VALFAT
					aDados[Len(aDados),10] 	:= _WS_VALBAIXA
					aDados[Len(aDados),11] 	:= _WS_IR
					aDados[Len(aDados),12] 	:= Iif(lOutraNf,0,_WS_PIS)
					aDados[Len(aDados),13] 	:= Iif(lOutraNf,0,_WS_COFINS)
					aDados[Len(aDados),14] 	:= Iif(lOutraNf,0,_WS_CSLL)
					aDados[Len(aDados),15] 	:= _WS_INSS

					aDados[Len(aDados),16] 	:= Iif(!Empty(_WS_NFE),"'"+_WS_NFE+"'","")
					aDados[Len(aDados),17] 	:= _WS_OBSERV
					aDados[Len(aDados),18] 	:= _WS_EMPTIT
					aDados[Len(aDados),19] 	:= _WS_FILTIT
					aDados[Len(aDados),20] 	:= _WS_CGC
					aDados[Len(aDados),21] 	:= _WS_NOMECOM
					aDados[Len(aDados),22] 	:= _WS_RECNO
					aDados[Len(aDados),23] 	:= _WS_EMPRESA
					aDados[Len(aDados),24] 	:= _WS_FILIAL
					aDados[Len(aDados),25] 	:= _WS_UNIDADE

				Else

					aDados[nPos,09] += _WS_VALFAT
					aDados[nPos,10] += _WS_VALBAIXA
					aDados[nPos,11] += _WS_IR
					aDados[nPos,12] += Iif(lOutraNf,0,_WS_PIS)
					aDados[nPos,13] += Iif(lOutraNf,0,_WS_COFINS)
					aDados[nPos,14] += Iif(lOutraNf,0,_WS_CSLL)
					aDados[nPos,15] += _WS_INSS

				EndIf

				T_E1->(dbSkip())
			EndDo

			T_E1->(dbCloseArea())
			dbSelectArea("SE1")
		Next

		//RpcClearEnv()
		//RpcSetType(2)

		If Len(aDados) = 0

			aAdd(aDados,Array(25))
			aDados[Len(aDados),01]	:= "  /  /  "
			aDados[Len(aDados),02]	:= "-"
			aDados[Len(aDados),03]	:= ""
			aDados[Len(aDados),04]	:= ""
			aDados[Len(aDados),05] 	:= ""
			aDados[Len(aDados),06] 	:= ""
			aDados[Len(aDados),07] 	:= "  /  /  "
			aDados[Len(aDados),08] 	:= "  /  /  "

			aDados[Len(aDados),09] 	:= 0
			aDados[Len(aDados),10] 	:= 0
			aDados[Len(aDados),11] 	:= 0
			aDados[Len(aDados),12] 	:= 0
			aDados[Len(aDados),13] 	:= 0
			aDados[Len(aDados),14] 	:= 0
			aDados[Len(aDados),15] 	:= 0

			aDados[Len(aDados),16] 	:= ""
			aDados[Len(aDados),17] 	:= ""
			aDados[Len(aDados),18] 	:= ""
			aDados[Len(aDados),19] 	:= ""
			aDados[Len(aDados),20] 	:= ""
			aDados[Len(aDados),21] 	:= ""
			aDados[Len(aDados),22] 	:= 0
			aDados[Len(aDados),23] 	:= ""
			aDados[Len(aDados),24] 	:= ""
			aDados[Len(aDados),25] 	:= ""
		EndIf

		//aSort(aDados,,,{|x,y| CtoD(X[1]) > CtoD(y[1]) .And. X[3]+X[4]+X[5]+X[6] < Y[3]+Y[4]+Y[5]+Y[6]})
		aSort(aDados,,,{|x,y| CtoD(X[1]) < CtoD(y[1])})

		For nX := 1 To Len(aDados)

			oListTitulos := wsclassnew("listtitulos")
			olisttitulos:cb_empresa	 	:= aDados[nX,23]
			olisttitulos:cb_filial		:= aDados[nX,24]
			olisttitulos:cb_unidade		:= aDados[nX,25]
			olisttitulos:cb_vencimento	:= aDados[nX,01]
			olisttitulos:cb_nome		:= AllTrim(aDados[nX,02])
			olisttitulos:cb_prefixo		:= StrTran(StrTran(StrTran(AllTrim(aDados[nX,03]), CHR(10), ""), CHR(32), ""), CHR(9), "")
			olisttitulos:cb_numero		:= aDados[nX,04]
			olisttitulos:cb_parcela		:= aDados[nX,05]
			olisttitulos:cb_tipo		:= aDados[nX,06]
			olisttitulos:cb_emissao		:= aDados[nX,07]
			olisttitulos:cb_prorrogacao	:= aDados[nX,08]
			olisttitulos:cb_original_valor	:= Transform(aDados[nX,09],"@E 999,999,999.99")
			olisttitulos:cb_baixa_valor	:= Transform(aDados[nX,10],"@E 999,999,999.99")
			olisttitulos:cb_ir			:= Transform(aDados[nX,11],"@E 999,999,999.99")
			olisttitulos:cb_pis			:= Transform(aDados[nX,12],"@E 999,999,999.99")
			olisttitulos:cb_cofins		:= Transform(aDados[nX,13],"@E 999,999,999.99")
			olisttitulos:cb_csll		:= Transform(aDados[nX,14],"@E 999,999,999.99")
			olisttitulos:cb_inss		:= Transform(aDados[nX,15],"@E 999,999,999.99")
			olisttitulos:cb_nfe			:= aDados[nX,16]
			olisttitulos:cb_observacao	:= AllTrim(aDados[nX,17])
			olisttitulos:cb_empori		:= aDados[nX,18]
			olisttitulos:cb_filori		:= aDados[nX,19]
			olisttitulos:cb_cnpj		:= aDados[nX,20]
			olisttitulos:cb_nomecom		:= AllTrim(aDados[nX,21])
			olisttitulos:nb_recno  		:= aDados[nX,22]

			aAdd(::ab_titulo2,olisttitulos)


		Next
	Endif
	U_xCONOUT("AUTOATENDIMENTO","CONS_PENDENCIAS","RETORNO","")

Return .T. 

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ FLAG_LOGIN       ³ Autor ³ Cleyton Ferreira ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod FLAG_LOGIN WsReceive c_cliente, c_loja WsSend ca_situacao WsService AUTOATENDIMENTO

//Public __CINTERNET := ""
	RpcClearEnv()
	RpcSetType(2)
	Prepare Environment Empresa "00" Filial "00001000100"
	U_xCONOUT("AUTOATENDIMENTO","FLAG_LOGIN","REQUISICAO","")
//ALTERADO POR R.R.MEZZALIRA 19/06/15 CHAMADO TRXQ68
	IF LEN(c_loja) < TamSx3("A1_LOJA")[1]
		c_loja := c_loja+SPACE(LEN(SPACE(TamSx3("A1_LOJA")[1]-LEN(c_loja))))
	Endif
	IF LEN(c_cliente) < TamSx3("A1_COD")[1]

		c_cliente:= c_cliente+SPACE(LEN(SPACE(TamSx3("A1_COD")[1]-LEN(c_cliente))))
	Endif



	SA1->(dbSetOrder(1))
//ALTERADO POR R.R.MEZZALIRA 19/06/15 CHAMADO TRXQ68
//If SA1->(dbSeek(xFilial("SA1")+AllTrim(c_cliente)+AllTrim(c_loja)))
	If SA1->(dbSeek(xFilial("SA1")+c_cliente+c_loja))
		If AllTrim(SA1->A1_XSITCLI) == "2"
			::ca_situacao := "INATIVO"
		Else
			If !Empty(SA1->A1_PAIS) .And. AllTrim(SA1->A1_PAIS) <> "105"
				::ca_situacao := "INTERNACIONAL"
			Else
				::ca_situacao := "ATIVO"
			EndIf
		EndIf
	Else
		::ca_situacao := "NAO ENCONTRADO"
	EndIf

	U_xCONOUT("AUTOATENDIMENTO","FLAG_LOGIN","RETORNO","")

Return .T.
/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ CONSULTA_TITULOS ³ Autor ³ Cleyton Ferreira ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONSULTA_TITULOS WsReceive c_cliente, c_loja, cb_de_vencimento, cb_ate_vencimento, cb_de_emissao, cb_ate_emissao, cb_de_titulo, cb_ate_titulo, cb_posicao WsSend ab_titulos WsService AUTOATENDIMENTO

	Local _WS_EMPRESA  := ""
	Local _WS_FILIAL   := ""
	Local _WS_UNIDADE  := ""
	Local _WS_NOME     := ""
	Local _WS_PREFIXO  := ""
	Local _WS_NUMERO   := ""
	Local _WS_PARCELA  := ""
	Local _WS_TIPO     := ""
	Local _WS_EMISSAO  := ""
	Local _WS_VENCTO   := ""
	Local _WS_PRORROG  := ""
	Local _WS_VALFAT   := 0
	Local _WS_VALBAIXA := 0
	Local _WS_IR       := 0
	Local _WS_PIS      := 0
	Local _WS_COFINS   := 0
	Local _WS_CSLL     := 0
	Local _WS_INSS     := 0
	Local _WS_OBSERV   := ""
	Local _WS_NFE      := ""
	Local _WS_EMPTIT   := ""
	Local _WS_FILTIT   := ""
	Local _WS_CGC      := ""
	Local _WS_NOMECOM  := ""

	Local _WS_DESCSERV := ""
	Local _WS_OUTINFO  := ""
	Local _WS_TOTDED   := ""

	Local _WS_RPSEMP	:= ""
	Local _WS_RPSEND	:= ""
	Local _WS_RPSBAI	:= ""
	Local _WS_RPSCID	:= ""
	Local _WS_RPSUF 	:= ""
	Local _WS_RPSCEP	:= ""
	Local _WS_RPSTEL	:= ""
	Local _WS_RPSCGC	:= ""
	Local _WS_RPSINC	:= ""

	Local lVerFil      := .T.
	Local lCancelado   := .F.
	Local lOutraNf     := .F.

	Local nPos		   := 0
	Local nMoeda	   := 0
	Local ndecs 	   := 0
	Local nSaldo       := 0
	Local nTotAbat     := 0
	Local nVezes       := 0
	Local nX           := 0
	Local nV           := 0
	Local cPosSF3	   := ""

	Local aDados	   := {}
	Local aValor       := {}
	Local aFiliais     := {}
	Local aEmpresas    := {}
	Local aSigaMat     := WS_Sw_SigaMat()
	Local cQueryx		:=""

	Local oListaTitulos

	Public oMsgItem3

//Prepare Environment Empresa "00" Filial "01"
//RpcSetEnv("00","01","PROTHEUS-AUTO","AP710",,{"SA1","SE1","SE5","SFQ","SE2","SE4","SF4"})

	RpcClearEnv()
	RpcSetType(2)
	RpcSetEnv("00","00001000100")
	U_xCONOUT("AUTOATENDIMENTO","CONSULTA_TITULOS","REQUISICAO","")

	For nV := 1 To Len(aSigaMat)

		_WS_EMPRESA := aSigaMat[nV,1]
		_WS_FILIAL  := aSigaMat[nV,2]
		_WS_UNIDADE := aSigaMat[nV,3]

		cQuery := "SELECT "
		cQuery += "SE1.E1_FILIAL, SE1.E1_CLIENTE, SE1.E1_LOJA, SE1.E1_PREFIXO, SE1.E1_NUM, SE1.E1_PARCELA, SE1.E1_TIPO, SE1.E1_NOMCLI, "
		cQuery += "SE1.E1_NATUREZ, SE1.E1_SITUACA, SE1.E1_PORTADO, SE1.E1_SALDO, SE1.E1_MOEDA, SE1.E1_RECIBO, SE1.E1_ORIGEM, "
		cQuery += "SE1.E1_EMISSAO, SE1.E1_VENCTO, SE1.E1_BAIXA, SE1.E1_FATURA, SE1.E1_DTFATUR, SE1.E1_VALLIQ, "
		cQuery += "SE1.E1_VALOR,SE1.E1_SDACRES,SE1.E1_SDDECRE,SE1.E1_TXMOEDA,SE1.E1_ACRESC,SE1.E1_DECRESC,SE1.R_E_C_N_O_ RECNO, "
		cQuery += "SE1.E1_INSS, SE1.E1_CSLL, SE1.E1_COFINS, SE1.E1_PIS, SE1.E1_IRRF, E1_VENCREA, E1_FILORIG, "
		cQuery += "SA1.A1_COD, SA1.A1_LOJA, SA1.A1_NOME, SA1.A1_NREDUZ, SA1.A1_CGC "

		cQuery += "FROM "
		cQuery += RetFullName("SE1",_WS_EMPRESA)+" SE1, "
		cQuery += RetFullName("SA1",_WS_EMPRESA)+" SA1  "

		cQuery += "WHERE "
		cQuery += "SA1.A1_COD		= SE1.E1_CLIENTE			AND "
		cQuery += "SA1.A1_LOJA		= SE1.E1_LOJA				AND "
		cQuery += "SE1.E1_FILIAL   	= '" +_WS_FILIAL+ "' 	AND "
		cQuery += "SE1.E1_CLIENTE 	= '" +c_cliente	+ "' 		AND "
		cQuery += "SE1.E1_LOJA    	= '" +c_loja	+ "' 		AND "

		cQuery += "SE1.E1_EMISSAO  <= '" + DTOS(dDataBase)+ "'	AND "
		cQuery += "SE1.E1_EMISSAO 	BETWEEN '" + cb_de_emissao    + "' AND '" + cb_ate_emissao    + "' AND "
		cQuery += "SE1.E1_VENCTO 	BETWEEN '" + cb_de_vencimento + "' AND '" + cb_ate_vencimento + "' AND "
		cQuery += "SE1.E1_NUM     	BETWEEN '" + cb_de_titulo     + "' AND '" + cb_ate_titulo     + "' AND "

//	If cPaisLoc<>"BRA"
//		cQuery += "SE1.E1_TIPO<>'CH' AND SE1.E1_TIPO<>'TF' AND "
//	EndIf

		cQuery += "SE1.E1_TIPO <> '"+MVPROVIS+"' AND "
		cQuery += "SE1.E1_TIPO <> 'PR ' AND "
		cQuery += "SE1.E1_TIPO <> 'RA ' AND "

		cQuery += "SE1.E1_TIPO NOT LIKE '%-'	AND "
		cQuery += "SE1.D_E_L_E_T_ <> '*'		AND "
		cQuery += "SA1.D_E_L_E_T_ <> '*' 			"

		cQuery += " ORDER BY "

		cQuery += "SE1.E1_VENCTO,SE1.E1_CLIENTE, SE1.E1_LOJA ,"
		cQuery += "SE1.E1_PREFIXO, SE1.E1_NUM, SE1.E1_PARCELA, SE1.E1_TIPO "

		cQuery := ChangeQuery(cQuery)

		cQueryx := cQuery

		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_E1",.T.,.T.)

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ Carrega array para trabalho ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		dbSelectArea("T_E1")
		dbGoTop()

		If T_E1->(!Eof())
			GetEmpr(_WS_EMPRESA+_WS_FILIAL)
			nMoeda	  := 1
			ndecs 	  := Msdecimais(1)
			aEmpresas := VerEmpresas()
			aFiliais  := FinRetFil()
			aValor    := {}

			If Select( "T_E1" ) == 0
				dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQueryx),"T_E1",.T.,.T.)
			Endif                                `
		EndIf

		dbSelectArea("T_E1")
		dbGoTop()

		While T_E1->(!Eof())
			
			nVezes := 0
			aValor := Baixas(T_E1->E1_NATUREZ,T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,nMoeda,"R",T_E1->E1_CLIENTE,dDataBase,T_E1->E1_LOJA,,,,.T., @lCancelado)

			If lCancelado
				T_E1->(dbSkip())
				Loop
			EndIf

			lVerFil := (!Empty(xFilial("SE1")) .And. !Empty(xFilial("SE5")) .And. Len(aFiliais) > 1)

			If lVerFil .And. !Empty(T_E1->E1_BAIXA) .And. aValor[11] == 0
				F340VerBxFil( @aValor, aFiliais, nMoeda )
			EndIf

			nSaldo := SaldoTit(T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,T_E1->E1_NATUREZ,"R",T_E1->E1_CLIENTE,nMoeda,dDataBase,,T_E1->E1_LOJA,,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0))

			If lVerFil .And. nSaldo > 0
				nSaldo -= Round(NoRound(xMoeda(FRVlCompFil("R",T_E1->E1_PREFIXO,T_E1->E1_NUM,T_E1->E1_PARCELA,T_E1->E1_TIPO,T_E1->E1_CLIENTE,T_E1->E1_LOJA,,aFiliais),;
					T_E1->E1_MOEDA,nMoeda,dDataBase,ndecs+1,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0) ),nDecs+1),nDecs)
			EndIf

			If T_E1->E1_VALOR <> T_E1->E1_VALLIQ .Or. T_E1->(E1_CSLL+E1_COFINS+E1_PIS+E1_IRRF+E1_INSS) > 0
				nTotAbat := SomaAbat(T_E1->E1_PREFIXO, T_E1->E1_NUM, T_E1->E1_PARCELA, "R", nMoeda )
			Else
				nTotAbat := 0
			EndIf

			If !(T_E1->E1_TIPO $ MVRECANT+"/"+MV_CRNEG) .And. nSaldo<>0 // nao deve olhar abatimento pois e zerado o saldo na liquidacao final do titulo
				nSaldo -= nTotAbat
			EndIf

			If Str(T_E1->E1_VALOR,17,2) == Str(nSaldo,17,2) .And. T_E1->E1_DECRESC > 0 .And. T_E1->E1_SDDECRE == 0
				aValor[2] += T_E1->E1_DECRESC
				nSaldo -= T_E1->E1_DECRESC
			Endif

			If Str(T_E1->E1_VALOR,17,2) == Str(nSaldo,17,2) .And. T_E1->E1_ACRESC > 0 .And. T_E1->E1_SDACRES == 0
				aValor[3] += T_E1->E1_ACRESC
				nSaldo += T_E1->E1_ACRESC
			Endif

			If T_E1->E1_SALDO > 0 .And. Empty(T_E1->E1_BAIXA)
				aValor[3] += T_E1->E1_ACRESC
				aValor[2] += T_E1->E1_DECRESC
			Else
				aValor[3] += T_E1->E1_SDACRES
				aValor[2] += T_E1->E1_SDDECRE
			EndIf

			If nSaldo <= 0 .And. Upper(cb_posicao) == "EM ABERTO"
				T_E1->(dbSkip())
				Loop
			EndIf

			If nSaldo > 0 .And. Upper(cb_posicao) == "BAIXADO"
				T_E1->(dbSkip())
				Loop
			EndIf

			//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
			//³ Carrega avariaveis para facilitar manutencao no fonte ³
			//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
			_WS_FILIAL      := T_E1->E1_FILIAL
			_WS_NOME    	:= T_E1->A1_NOME
			_WS_PREFIXO 	:= Iif(!Empty(T_E1->E1_PREFIXO),"'"+T_E1->E1_PREFIXO+"'",CriaVar("E1_PREFIXO"))
			_WS_NUMERO 		:= Iif(!Empty(T_E1->E1_NUM)    ,"'"+T_E1->E1_NUM    +"'",CriaVar("E1_NUMERO"))
			_WS_PARCELA 	:= Iif(!Empty(T_E1->E1_PARCELA),"'"+T_E1->E1_PARCELA+"'",CriaVar("E1_PARCELA"))
			_WS_TIPO 		:= Iif(!Empty(T_E1->E1_TIPO)   ,"'"+T_E1->E1_TIPO   +"'",CriaVar("E1_TIPO"))

			_WS_EMISSAO 	:= Iif(!Empty(T_E1->E1_EMISSAO),DTOC(STOD(T_E1->E1_EMISSAO)),"  /  /  ")
			_WS_VENCTO 		:= Iif(!Empty(T_E1->E1_VENCTO),DTOC(STOD(T_E1->E1_VENCTO)),"  /  /  ")
			_WS_PRORROG 	:= Iif(!Empty(T_E1->E1_VENCREA),DTOC(STOD(T_E1->E1_VENCREA)),"  /  /  ")

			_WS_VALFAT 		:= xMoeda(T_E1->E1_VALOR,T_E1->E1_MOEDA,nMoeda,T_E1->E1_EMISSAO,,If(cPaisLoc=="BRA",T_E1->E1_TXMOEDA,0)) //* Iif(Alltrim(T_E1->E1_TIPO)$(MVRECANT+","+MV_CRNEG),-1,1)
			_WS_VALBAIXA 	:= aValor[5]
			_WS_IR 			:= T_E1->E1_IRRF
			_WS_PIS 		:= T_E1->E1_PIS
			_WS_COFINS 		:= T_E1->E1_COFINS
			_WS_CSLL 		:= T_E1->E1_CSLL
			_WS_INSS 		:= T_E1->E1_INSS
			_WS_NFE 		:= Posicione("SF3",5,T_E1->E1_FILIAL+T_E1->(E1_PREFIXO+E1_NUM+E1_CLIENTE+E1_LOJA),"F3_NFELETR")
			_WS_NFE         := Iif(!Empty(_WS_NFE),_WS_NFE,CriaVar("F3_NFELETR"))
			_WS_OBSERV 		:= " "

			_WS_EMPTIT 		:= SubStr(RetSqlName("SE1"),4,2)
			_WS_FILTIT 		:= T_E1->E1_FILIAL
			nPosFil         := aScan(aEmpresas,{ |x| AllTrim(x[1])+AllTrim(x[2]) == AllTrim(_WS_EMPTIT)+AllTrim(_WS_FILTIT)})
			_WS_CGC         := aEmpresas[nPosFil,3]
			_WS_NOMECOM     := aEmpresas[nPosFil,4]
			_WS_RECNO       := T_E1->RECNO


			//========================================================= DADOS EXTRAS - GUIA RPS
			DbSelectArea("SF3")
			dbSetOrder(5)

			dbSeek(T_E1->E1_FILIAL + T_E1->(E1_PREFIXO+E1_NUM+E1_CLIENTE+E1_LOJA))

			cPosSF3	:= Posicione("SF3",5,T_E1->E1_FILIAL+T_E1->(E1_PREFIXO+E1_NUM+E1_CLIENTE+E1_LOJA),"F3_CODISS")
			SX5->(dbSetOrder(1))
			If SX5->(dbSeek(xFilial("SX5")+"60"+cPosSF3))
				_WS_DESCSERV := cPosSF3 +" - "+ SX5->(FieldGet(FieldPos("X5_DESCRI")))
			Endif

			_WS_OUTINFO := Execblock("MTOBSNFE",.F.,.F.,{SF3->F3_NFISCAL,SF3->F3_SERIE,SF3->F3_CLIEFOR,SF3->F3_LOJA})

			_WS_TOTDED := (Iif(SF3->(FieldPos("F3_ISSSUB")) > 0,SF3->F3_ISSSUB,0))+(Iif(SF3->(FieldPos("F3_ISSMAT")) > 0,SF3->F3_ISSMAT,0))

			DbSelectArea("SM0")
			dbSeek(_WS_EMPRESA+_WS_FILIAL)

			_WS_RPSEMP:= SM0->M0_NOMECOM
			_WS_RPSEND:= SM0->M0_ENDENT
			_WS_RPSBAI:= SM0->M0_BAIRENT
			_WS_RPSCID:= SM0->M0_CIDENT
			_WS_RPSUF := SM0->M0_ESTENT
			_WS_RPSCEP:= SM0->M0_CEPENT
			_WS_RPSTEL:= SM0->M0_TEL
			_WS_RPSCGC:= Transform(SM0->M0_CGC,"@R 99.999.999/9999-99")
			_WS_RPSINC:= SM0->M0_INSCM

			//========================================================= FINAL DADOS RPS


			SFQ->(dbSetOrder(1))
			SFQ->(dbSeek(T_E1->E1_FILIAL+"SE1"+T_E1->(E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO+E1_CLIENTE+E1_LOJA)))
			While SFQ->(!Eof()).And. SFQ->(FQ_FILIAL+FQ_ENTORI+FQ_PREFORI+FQ_NUMORI+FQ_PARCORI+FQ_TIPOORI+FQ_CFORI+FQ_LOJAORI)==T_E1->E1_FILIAL+"SE1"+T_E1->(E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO+E1_CLIENTE+E1_LOJA)
				nVezes++
				_WS_OBSERV := _WS_OBSERV+Iif(nVezes>1,", ","")+AllTrim(SFQ->FQ_PREFDES)+"-"+AllTrim(SFQ->FQ_NUMDES)+"/"+AllTrim(SFQ->FQ_PARCDES)
				SFQ->(dbSkip())
			EndDo

			If !Empty(_WS_OBSERV)
				_WS_OBSERV := _WS_OBSERV+", "+AllTrim(T_E1->E1_PREFIXO)+"-"+AllTrim(T_E1->E1_NUM)+"/"+AllTrim(T_E1->E1_PARCELA)
			Else
				_WS_OBSERV := "-"
			EndIf

			lOutraNf := .F.

			cQuery := "SELECT FQ_NUMDES FROM "+RetSqlName("SFQ")+" WHERE "
			cQuery += "FQ_ENTDES  = 'SE1' AND "
			cQuery += "FQ_PREFDES = '"+T_E1->E1_PREFIXO	+"'	AND "
			cQuery += "FQ_NUMDES  = '"+T_E1->E1_NUM		+"'	AND "
			cQuery += "FQ_PARCDES = '"+T_E1->E1_PARCELA	+"'	AND "
			cQuery += "FQ_TIPODES = '"+T_E1->E1_TIPO	+"'	AND	"
			cQuery += "FQ_CFDES   = '"+T_E1->E1_CLIENTE	+"'	AND "
			cQuery += "FQ_LOJADES = '"+T_E1->E1_LOJA	+"'	AND "
			cQuery += "D_E_L_E_T_ <> '*'"

			cQuery := ChangeQuery(cQuery)

			dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_XX",.T.,.T.)

			T_XX->(dbGoTop())
			While T_XX->(!Eof())
				lOutraNf := .T.
				T_XX->(dbSkip())
			EndDo
			T_XX->(dbCloseArea())

			dbSelectArea("T_E1")

			nPos := aScan(aDados,{|x|	x[01] == _WS_VENCTO		.And.;
				X[02] == _WS_NOME 		.And.;
				x[03] == _WS_PREFIXO	.And.;
				x[04] == _WS_NUMERO		.And.;
				x[05] == _WS_PARCELA	.And.;
				x[06] == _WS_TIPO		.And.;
				x[23] == _WS_EMPRESA   	.And.;
				x[24] == _WS_FILIAL		.And.;
				x[25] == _WS_UNIDADE	})
			If nPos == 0

				aAdd(aDados,Array(37))

				aDados[Len(aDados),01]	:= _WS_VENCTO
				aDados[Len(aDados),02]	:= _WS_NOME
				aDados[Len(aDados),03]	:= _WS_PREFIXO
				aDados[Len(aDados),04]	:= _WS_NUMERO
				aDados[Len(aDados),05] 	:= _WS_PARCELA
				aDados[Len(aDados),06] 	:= _WS_TIPO

				aDados[Len(aDados),07] 	:= _WS_EMISSAO
				aDados[Len(aDados),08] 	:= _WS_PRORROG

				aDados[Len(aDados),09] 	:= _WS_VALFAT
				aDados[Len(aDados),10] 	:= _WS_VALBAIXA
				aDados[Len(aDados),11] 	:= _WS_IR
				aDados[Len(aDados),12] 	:= Iif(lOutraNf,0,_WS_PIS)
				aDados[Len(aDados),13] 	:= Iif(lOutraNf,0,_WS_COFINS)
				aDados[Len(aDados),14] 	:= Iif(lOutraNf,0,_WS_CSLL)
				aDados[Len(aDados),15] 	:= _WS_INSS

				aDados[Len(aDados),16] 	:= Iif(!Empty(_WS_NFE),"'"+_WS_NFE+"'","")
				aDados[Len(aDados),17] 	:= _WS_OBSERV
				aDados[Len(aDados),18] 	:= _WS_EMPTIT
				aDados[Len(aDados),19] 	:= _WS_FILTIT
				aDados[Len(aDados),20] 	:= _WS_CGC
				aDados[Len(aDados),21] 	:= _WS_NOMECOM
				aDados[Len(aDados),22] 	:= _WS_RECNO
				aDados[Len(aDados),23] 	:= _WS_EMPRESA
				aDados[Len(aDados),24] 	:= _WS_FILIAL
				aDados[Len(aDados),25] 	:= _WS_UNIDADE
				aDados[Len(aDados),26] 	:= _WS_DESCSERV
				aDados[Len(aDados),27] 	:= _WS_OUTINFO
				aDados[Len(aDados),28] 	:= Transform(_WS_TOTDED,"@E 999,999,999.99")

				aDados[Len(aDados),29] 	:= alltrim(_WS_RPSEMP)
				aDados[Len(aDados),30] 	:= alltrim(_WS_RPSEND)
				aDados[Len(aDados),31] 	:= alltrim(_WS_RPSBAI)
				aDados[Len(aDados),32] 	:= alltrim(_WS_RPSCID)
				aDados[Len(aDados),33] 	:= alltrim(_WS_RPSUF)
				aDados[Len(aDados),34] 	:= alltrim(_WS_RPSCEP)
				aDados[Len(aDados),35] 	:= alltrim(_WS_RPSTEL)
				aDados[Len(aDados),36] 	:= alltrim(_WS_RPSCGC)
				aDados[Len(aDados),37] 	:= alltrim(_WS_RPSINC)


			Else

				aDados[nPos,09] += _WS_VALFAT
				aDados[nPos,10] += _WS_VALBAIXA
				aDados[nPos,11] += _WS_IR
				aDados[nPos,12] += Iif(lOutraNf,0,_WS_PIS)
				aDados[nPos,13] += Iif(lOutraNf,0,_WS_COFINS)
				aDados[nPos,14] += Iif(lOutraNf,0,_WS_CSLL)
				aDados[nPos,15] += _WS_INSS

			EndIf

			T_E1->(dbSkip())
		EndDo

		T_E1->(dbCloseArea())
		dbSelectArea("SE1")
	Next

//RpcClearEnv()
//RpcSetType(2)

	If Len(aDados) = 0

		aAdd(aDados,Array(37))
		aDados[Len(aDados),01]	:= "  /  /  "
		aDados[Len(aDados),02]	:= "-"
		aDados[Len(aDados),03]	:= ""
		aDados[Len(aDados),04]	:= ""
		aDados[Len(aDados),05] 	:= ""
		aDados[Len(aDados),06] 	:= ""
		aDados[Len(aDados),07] 	:= "  /  /  "
		aDados[Len(aDados),08] 	:= "  /  /  "

		aDados[Len(aDados),09] 	:= 0
		aDados[Len(aDados),10] 	:= 0
		aDados[Len(aDados),11] 	:= 0
		aDados[Len(aDados),12] 	:= 0
		aDados[Len(aDados),13] 	:= 0
		aDados[Len(aDados),14] 	:= 0
		aDados[Len(aDados),15] 	:= 0

		aDados[Len(aDados),16] 	:= ""
		aDados[Len(aDados),17] 	:= ""
		aDados[Len(aDados),18] 	:= ""
		aDados[Len(aDados),19] 	:= ""
		aDados[Len(aDados),20] 	:= ""
		aDados[Len(aDados),21] 	:= ""
		aDados[Len(aDados),22] 	:= 0
		aDados[Len(aDados),23] 	:= ""
		aDados[Len(aDados),24] 	:= ""
		aDados[Len(aDados),25] 	:= ""
		aDados[Len(aDados),26] 	:= ""
		aDados[Len(aDados),27] 	:= ""
		aDados[Len(aDados),28] 	:= ""

		aDados[Len(aDados),29] 	:= ""
		aDados[Len(aDados),30] 	:= ""
		aDados[Len(aDados),31] 	:= ""
		aDados[Len(aDados),32] 	:= ""
		aDados[Len(aDados),33] 	:= ""
		aDados[Len(aDados),34] 	:= ""
		aDados[Len(aDados),35] 	:= ""
		aDados[Len(aDados),36] 	:= ""
		aDados[Len(aDados),37] 	:= ""

	EndIf

//aSort(aDados,,,{|x,y| CtoD(X[1]) > CtoD(y[1]) .And. X[3]+X[4]+X[5]+X[6] < Y[3]+Y[4]+Y[5]+Y[6]})
	aSort(aDados,,,{|x,y| CtoD(X[1]) > CtoD(y[1])})

	For nX := 1 To Len(aDados)

		oListaTitulos := wsclassnew("listatitulos")

		oListaTitulos:cb_empresa	 	:= aDados[nX,23]
		oListaTitulos:cb_filial			:= aDados[nX,24]
		oListaTitulos:cb_unidade		:= aDados[nX,25]

		oListaTitulos:cb_vencimento	 	:= aDados[nX,01]
		oListaTitulos:cb_nome			:= AllTrim(aDados[nX,02])
		oListaTitulos:cb_prefixo		:= aDados[nX,03]
		oListaTitulos:cb_numero			:= aDados[nX,04]
		oListaTitulos:cb_parcela		:= aDados[nX,05]
		oListaTitulos:cb_tipo			:= aDados[nX,06]
		oListaTitulos:cb_emissao		:= aDados[nX,07]
		oListaTitulos:cb_prorrogacao	:= aDados[nX,08]
		oListaTitulos:cb_original_valor	:= Transform(aDados[nX,09],"@E 999,999,999.99")
		oListaTitulos:cb_baixa_valor	:= Transform(aDados[nX,10],"@E 999,999,999.99")
		oListaTitulos:cb_ir				:= Transform(aDados[nX,11],"@E 999,999,999.99")
		oListaTitulos:cb_pis			:= Transform(aDados[nX,12],"@E 999,999,999.99")
		oListaTitulos:cb_cofins			:= Transform(aDados[nX,13],"@E 999,999,999.99")
		oListaTitulos:cb_csll			:= Transform(aDados[nX,14],"@E 999,999,999.99")
		oListaTitulos:cb_inss			:= Transform(aDados[nX,15],"@E 999,999,999.99")
		oListaTitulos:cb_nfe			:= aDados[nX,16]
		oListaTitulos:cb_observacao		:= AllTrim(aDados[nX,17])
		oListaTitulos:cb_empori			:= aDados[nX,18]
		oListaTitulos:cb_filori			:= aDados[nX,19]
		oListaTitulos:cb_cnpj			:= aDados[nX,20]
		oListaTitulos:cb_nomecom		:= AllTrim(aDados[nX,21])

		oListaTitulos:cb_descServ		:= aDados[nX,26]
		oListaTitulos:cb_outInfo		:= aDados[nX,27]
		oListaTitulos:cb_totDedu		:= aDados[nX,28]

		oListaTitulos:nb_recno  		:= aDados[nX,22]

		oListaTitulos:cb_rpsEmp			:= aDados[nX,29]
		oListaTitulos:cb_rpsEnd			:= aDados[nX,30]
		oListaTitulos:cb_rpsBai			:= aDados[nX,31]
		oListaTitulos:cb_rpsCid			:= aDados[nX,32]
		oListaTitulos:cb_rpsUf			:= aDados[nX,33]
		oListaTitulos:cb_rpsCep			:= aDados[nX,34]
		oListaTitulos:cb_rpsTel			:= aDados[nX,35]
		oListaTitulos:cb_rpsCgc			:= aDados[nX,36]
		oListaTitulos:cb_rpsInc			:= aDados[nX,37]

		aAdd(::ab_titulos,oListaTitulos)

	Next

	U_xCONOUT("AUTOATENDIMENTO","CONSULTA_TITULOS","RETORNO","")

Return .T.

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ CONSULTA_NFISCAL ³ Autor ³ Cleyton Ferreira ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONSULTA_NFISCAL WsReceive c_cliente, c_loja, cc_de_emissao, cc_ate_emissao, cc_de_nfiscal, cc_ate_nfiscal,cc_de_nfEletr,cc_ate_nfEletr WsSend ac_nfiscal WsService AUTOATENDIMENTO

	Local cChave     := ""
	Local cQuery     := ""
	Local cQryComp   := ""
	Local cEmpTemp   := ""
	Local cFilTemp   := ""
	Local cDescAglu  := "-"
	Local lMudou     := .T.
	Local nPos	     := 0
	Local nV         := 0
	Local nX         := 0
	Local nY         := 0
	Local aSigaMat   := WS_Sw_SigaMat()
	Local aDados     := {}
	Local oCabecalho
	Local oItens
	Local oNfsImpAc
	Local cQueryx := ""
	Local lMetodoAntigo
	Public oMsgItem3
//Public __CINTERNET := ""

//Prepare Environment Empresa "00" Filial "01"
	RpcClearEnv()
	RpcSetType(2)
	RpcSetEnv("00","00001000100")
	lMetodoAntigo	:= GetMv("TI_MTSXGET",,.F.)

	U_xCONOUT("AUTOATENDIMENTO","CONSULTA_NFISCAL","REQUISICAO","")

	For nV := 1 To Len(aSigaMat)


		cEmpTemp := aSigaMat[nV,1]
		cFilTemp := aSigaMat[nV,2]

		cQuery += "SELECT "
		cQuery += "SF2.F2_CLIENTE, SF2.F2_LOJA   , SF2.F2_XCLIEN2, SF2.F2_COND   , SF2.F2_DOC   , "
		cQuery += "SF2.F2_SERIE  , SF2.F2_NFELETR, SF2.F2_CODNFE , SF2.F2_EMISSAO, SF2.F2_FILIAL, "
		cQuery += "SF2.F2_DUPL   , SF2.F2_PREFIXO, SF2.F2_EMINFE ,"
		cQuery += "SD2.D2_DOC    , SD2.D2_SERIE  , SD2.D2_COD    , SD2.D2_PRCVEN , SD2.D2_QUANT , "
		cQuery += "SD2.D2_TOTAL  , SD2.D2_UM     , SD2.D2_ITEM   , "
		cQuery += "SB1.B1_ALIQISS, SB1.B1_DESC   , SB1.B1_GRTRIB , SB1.B1_GRUPO, SB1.B1_PRV1, SB1.B1_TS, SB1.B1_UM "
		cQuery += "FROM  "
		cQuery += RetFullName("SF2",cEmpTemp)+" SF2, "
		cQuery += RetFullName("SD2",cEmpTemp)+" SD2, "
		cQuery += RetFullName("SB1",cEmpTemp)+" SB1  "
		cQuery += "WHERE "
		cQuery += "SF2.F2_FILIAL  = '"+cFilTemp+"'	AND "
		cQuery += "SD2.D2_FILIAL  = '"+cFilTemp+"'	AND "
		cQuery += "SB1.B1_FILIAL  = '"+xFilial("SB1")+"'	AND "
		cQuery += "SD2.D2_DOC     = SF2.F2_DOC 		AND "
		cQuery += "SD2.D2_SERIE   = SF2.F2_SERIE 	AND "
		cQuery += "SD2.D2_CLIENTE = SF2.F2_CLIENTE	AND "
		cQuery += "SD2.D2_LOJA    = SF2.F2_LOJA 	AND "
		cQuery += "SB1.B1_COD     = SD2.D2_COD 		AND "
		cQuery += "SF2.F2_CLIENTE = '"+c_cliente+"'	AND "
		cQuery += "SF2.F2_LOJA    = '"+c_loja+"' 	AND "
		cQuery += "SF2.F2_EMISSAO BETWEEN '"+cc_de_emissao+"' AND '"+cc_ate_emissao+"' AND "
		cQuery += "SF2.F2_DOC     BETWEEN '"+cc_de_nfiscal+"' AND '"+cc_ate_nfiscal+"' AND "
		cQuery += "SF2.F2_NFELETR BETWEEN '"+cc_de_nfEletr+"' AND '"+cc_ate_nfEletr+"' AND "
		cQuery += "SF2.D_E_L_E_T_ = ' ' AND "
		cQuery += "SD2.D_E_L_E_T_ = ' ' AND "
		cQuery += "SB1.D_E_L_E_T_ = ' ' "

		cQuery += "ORDER BY F2_EMISSAO, F2_DOC, F2_SERIE, D2_COD, D2_ITEM DESC "

		cQuery := ChangeQuery(cQuery)

		cQueryx:= cQuery

		dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQryComp+cQuery), "T_F2" , .F., .T.)

		dbSelectArea("T_F2")
		dbGoTop()

		If T_F2->(!Eof())

			If lMetodoAntigo
				GetEmpr(cEmpTemp+cFilTemp)
			Else
				RpcClearEnv()
				RpcSetEnv(cEmpTemp, cFilTemp)
			EndIf

			If Select( "T_F2" ) == 0
				dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQueryx),"T_F2",.T.,.T.)
			EndIf
		EndIf

		While T_F2->(!Eof())

			nPos := aScan(aDados,{|x| 	AllTrim(x[1])			+AllTrim(x[2])				+AllTrim(x[6])			+AllTrim(x[7])	== ;
				AllTrim(aSigaMat[nV,1])	+AllTrim(T_F2->F2_FILIAL)	+AllTrim(T_F2->F2_DOC)	+AllTrim(T_F2->F2_SERIE)})

			If nPos == 0

				cDescAglu := Execblock("MTDESCRNFE",.F.,.F.,{T_F2->F2_DOC,T_F2->F2_SERIE,c_cliente,c_loja})
				cDescAglu := Iif(Empty(cDescAglu),"-",cDescAglu)

				aAdd(aDados,{	AllTrim(aSigaMat[nV,1])			,;
					AllTrim(T_F2->F2_FILIAL)		,;
					aSigaMat[nV,3]					,;
					T_F2->F2_XCLIEN2				,;
					T_F2->F2_COND					,;
					AllTrim(T_F2->F2_DOC)			,;
					AllTrim(T_F2->F2_SERIE)			,;
					T_F2->F2_NFELETR				,;
					T_F2->F2_CODNFE					,;
					DtoC(StoD(T_F2->F2_EMISSAO))	,;
					cDescAglu						,;
					DtoC(StoD(T_F2->F2_EMINFE))		,;
					{}								,;
					CalcRetencao("T_F2",cEmpTemp)})
				nPos := Len(aDados)
			EndIf

			aAdd(aDados[nPos,13],{	T_F2->D2_COD									,;
				T_F2->B1_DESC									,;
				Transform(T_F2->D2_PRCVEN,"@E 999,999,999.99")	,;
				Transform(T_F2->D2_QUANT ,"@E 9,999,999.9999")	,;
				Transform(T_F2->D2_TOTAL ,"@E 999,999,999.99")	,;
				T_F2->D2_UM										,;
				Transform(T_F2->B1_ALIQISS,"@E 999,999,999.99"),;
				T_F2->B1_GRTRIB              					})



			T_F2->(dbSkip())

		EndDo

		T_F2->(dbCloseArea())

		cQuery := ""
	Next nV

	//RpcClearEnv()
	//RpcSetType(2)

	If Len(aDados) == 0
		aAdd(aDados,{"","","","","","","","","","  /  /  ","", "  /  /  ",{{"","","0","0","0","","0",""}},{{"","","","","",""}}})
	EndIf

	//aSort(aDados,,,{|x,y| DtoS(CtoD(X[10])) > DtoS(CtoD(y[10])) .And. X[7]+X[6] < Y[7]+Y[6]})
	aSort(aDados,,,{|x,y| CtoD(X[10]) > CtoD(y[10]) })

	For nx := 1 To Len(aDados)

		oCabecalho:=wsclassnew("listanotasfiscais")
		oCabecalho:cc_empresa	   	:= aDados[nx,01]  //T_F2->F2_XCLIEN2
		oCabecalho:cc_filial   		:= aDados[nx,02]  //T_F2->F2_COND
		oCabecalho:cc_unidade	   	:= aDados[nx,03]  //T_F2->F2_DOC
		oCabecalho:cc_cliente	   	:= aDados[nx,04]  //T_F2->F2_XCLIEN2
		oCabecalho:cc_cond_pag	   	:= aDados[nx,05]  //T_F2->F2_COND
		oCabecalho:cc_documento	   	:= aDados[nx,06]  //T_F2->F2_DOC
		oCabecalho:cc_serie		   	:= aDados[nx,07]  //T_F2->F2_SERIE
		oCabecalho:cc_nf_eletronica	:= aDados[nx,08]  //T_F2->F2_NFELETR
		oCabecalho:cc_codigo_nfe	:= aDados[nx,09]  //T_F2->F2_CODNFE
		oCabecalho:cc_emissao	   	:= aDados[nx,10]  //DtoC(StoD(T_F2->F2_EMISSAO))
		oCabecalho:cc_descricaonf   := aDados[nx,11]  //cDescAglu
		oCabecalho:cc_emisnfe	    := aDados[nx,12]  //DtoC(StoD(T_F2->F2_EMINFE)
		oCabecalho:ac_itens_nfiscal := {}
		oCabecalho:ac_nf_imp_acum	:= {}

		For nv := 1 To Len(aDados[nx,13])
			oItens:=wsclassnew("itens_nfiscal")
			oItens:cc_produto	 		:= aDados[nx,13,nv,1] //T_F2->D2_COD
			oItens:cc_descricao	   		:= aDados[nx,13,nv,2]  //T_F2->B1_DESC
			oItens:cc_preco_unitario	:= aDados[nx,13,nv,3]  //Transform(T_F2->D2_PRCVEN,"@E 999,999,999.99")
			oItens:cc_quantidade   		:= aDados[nx,13,nv,4]  //Transform(T_F2->D2_QUANT,"@E 9,999,999.9999")
			oItens:cc_total	 			:= aDados[nx,13,nv,5]  //Transform(T_F2->D2_TOTAL,"@E 999,999,999.99")
			oItens:cc_unidademedida		:= aDados[nx,13,nv,6]  //T_F2->D2_UM
			oItens:cc_aliquota_iss	 	:= aDados[nx,13,nv,7]  //Transform(T_F2->B1_ALIQISS,"@E 999,999,999.99")
			oItens:cc_grupo_tributario	:= aDados[nx,13,nv,8]  //T_F2->B1_GRTRIB
			aAdd(oCabecalho:ac_itens_nFiscal,oItens)
		Next nv

		//Dados do imposto acumulado buscados no Financeiro
		For nY := 1 To Len(aDados[nx,14])
			oNfsImpAc:=wsclassnew("nf_imp_acum")
			oNfsImpAc:cc_prefixo		:= aDados[nx,14,nY,1]
			oNfsImpAc:cc_numero			:= aDados[nx,14,nY,2]
			oNfsImpAc:cc_emissao		:= aDados[nx,14,nY,3]
			oNfsImpAc:cc_vencimento		:= aDados[nx,14,nY,4]
			oNfsImpAc:cc_valor			:= aDados[nx,14,nY,5]
			oNfsImpAc:cc_empresatitulo	:= aDados[nx,14,nY,6]
			aAdd(oCabecalho:ac_nf_imp_acum,oNfsImpAc)
		Next nY

		If Len(aDados[nx,14]) == 0
			oNfsImpAc:=wsclassnew("nf_imp_acum")
			oNfsImpAc:cc_prefixo		:= ""
			oNfsImpAc:cc_numero			:= ""
			oNfsImpAc:cc_emissao		:= ""
			oNfsImpAc:cc_vencimento		:= ""
			oNfsImpAc:cc_valor			:= ""
			oNfsImpAc:cc_empresatitulo	:= ""
			aAdd(oCabecalho:ac_nf_imp_acum,oNfsImpAc)
		EndIf

		aAdd(::ac_nfiscal,oCabecalho)

	Next nx

	If Select ("T_F2") >0
		T_F2->(dbCloseArea())
	EndIf

	U_xCONOUT("AUTOATENDIMENTO","CONSULTA_NFISCAL","RETORNO","")

Return .T.


/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ RE_IMPRESSAO     ³ Autor ³ Cleyton Ferreira ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod RE_IMPRESSAO WsReceive c_empresa, c_filial, c_cliente, c_loja, cd_documento, cd_serie WsSend cd_impnfiscal WsService AUTOATENDIMENTO

	Local cArqNF        := ""
	Local cMaskPfAl     := ""
	Local cMaskPfJun
	Local cQuery        := ""
	Local cIM           := ""
	Local _cPrefBEJun
	Local aCGCFilial    := {}
	Local lPrtConv      := .F.
	Local cod_cliente   := c_cliente
	Local cod_loja	    := c_loja
	Local cParDime	    := SuperGetMv("TI_DIMEON",,"2")
	Local lAchou        := .F.  
	
	If cParDime =='1'
			
		If Empty(c_Filial) //Validação de Filial vazia para não cair no endereço de Curitiba
			c_Filial:='00001001000'
		EndIf	

		U_xCONOUT("AUTOATENDIMENTO","RE_IMPRESSAO","REQUISICAO","")

		If AllTrim(::c_empresa) != cEmpAnt 
			RpcClearEnv()
			RPCSetEnv(AllTrim(::c_empresa), AllTrim(::c_Filial))
		Else 
			cFilAnt := AllTrim(::c_Filial)
		EndIf 
		
		cFilAnt := c_filial
		cEmpant := c_empresa
			
		lAchou := U_AvalCli(cEmpant, @cod_cliente, @cod_loja)

		SetFGene () 
		_cPrefBEJun := GetMv("TI_PREFJUN",,"0002501002000|0000202000800")
		cMaskPfJun  := GetMv("TI_FAT22",,"https://jundiai.giss.com.br/consultar-autenticidade-documento-fiscal") //Jundiai
		lPrtConv	:= GetMv("TI_PRTCONV",,.T.)

		cQuery += "SELECT F2_EMISSAO,F2_NFELETR, F2_CODNFE, X5_DESCRI, F2_CHVNFE, F2_SERIE, F2_XLINKNF, UTL_RAW.CAST_TO_VARCHAR2(SF2.F2_XLNKNF2) F2_XLNKNF2 " 
		cQuery += "FROM  " + RetFullName("SF2",::c_empresa)+" SF2 "

		If lPrtConv
			cQuery += "INNER JOIN " + RetFullName("SX5",::c_empresa)+" SX5 ON X5_FILIAL = '"+::c_Filial+"' AND X5_CHAVE = '"+::c_empresa+ "' AND X5_TABELA = 'XN' "
		Else
			cQuery += "INNER JOIN " + RetFullName("SX5",::c_empresa)+" SX5 ON X5_FILIAL = '"+::c_Filial+"' AND X5_CHAVE = "+::c_empresa+ " AND X5_TABELA = 'XN' "
		EndIf
		cQuery += "WHERE "
		cQuery += "SF2.F2_FILIAL  = '"+ ::c_filial +"'	AND "
		cQuery += "SF2.F2_CLIENTE = '"+ cod_cliente +"'	AND "
		cQuery += "SF2.F2_LOJA    = '"+ cod_loja +"' 	AND "
		cQuery += "SF2.F2_DOC     = '"+ ::cd_documento +"' AND "
		cQuery += "SF2.D_E_L_E_T_ = ' ' "

		cQuery := ChangeQuery(cQuery)

		If lAchou
			dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQuery), "T_F2" , .F., .T.)

			cIM := allTrim(T_F2->(FieldGet(FieldPos("X5_DESCRI"))))
			// Se achar a IM na tabela do De/Para, faz a troca
			ZX5->(DbSetOrder(1))
			If ZX5->(DbSeek(::c_Filial+Pad("XN",Len(ZX5->ZX5_TABELA))+T_F2->F2_SERIE))
				cIM := Alltrim(ZX5->ZX5_DESCRI)
			Endif

			cMaskPfAl := "https://www.nf-eletronica.com.br/NFE.asp?a="+AllTrim(T_F2->F2_CHVNFE)+"&b=T"
			If AllTrim(::c_empresa+::c_filial)$ cPrefDimSP // DIMENSA SAO PAULO
				cArqNF := StrTran(StrTran(StrTran(cMaskPfsp, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(::c_empresa+::c_filial)$ cPrefRecif // TOTVS NORDESTE
				cArqNF := StrTran(StrTran(StrTran(cMaskPfre, '__NF__', Alltrim(Right(Alltrim(T_F2->F2_NFELETR),8))), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			Elseif AllTrim(::c_empresa+::c_filial)$ cPrefSC // TOTVS SANTA CATARINA / JOINVILLE
				cArqNF := StrTran(StrTran(StrTran(cMaskPfjv  , '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			Elseif AllTrim(::c_empresa+::c_filial)$ cPrefFL // FLORIANOPOLIS - FEEDZ
				cArqNF := StrTran(StrTran(cMaskPfFL, '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			Elseif ( AllTrim(::c_empresa+::c_filial)$cPrefBH .Or. AllTrim(::c_empresa+::c_filial)$_cPrefPCBh ) //TOTVS BH OU PC SISTEMAS BH
				cArqNF := cMaskPfbh
			Elseif AllTrim(::c_empresa+::c_filial)$ cPrefRJ //TOTVS RIO DE JANEIRO
				cArqNF := StrTran(StrTran(StrTran(cMaskPfrj, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', StrTran(AllTrim(T_F2->F2_CODNFE),'-',''))
			ElseIf AllTrim(::c_empresa+::c_filial)$cPrefCam //TOTVS CAMPINAS
				cArqNF := cMaskPfcp
			ElseIf AllTrim(::c_empresa+::c_filial)$cPrefWealth //Wealth - System
				cArqNF := StrTran(cMaskPfWS, '__NF__', Alltrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(::c_empresa+::c_filial) $ cPrefMac // Totvs Macae
				cArqNF := StrTran(StrTran(StrTran(_cMaskPfMa,'__IM__',cIm),'__NF__', Alltrim(T_F2->F2_NFELETR)),'__CV__',StrTran(AllTrim(T_F2->F2_CODNFE),'-',''))
			ElseIf AllTrim(::c_empresa+::c_filial)$ cPrefDF // Totvs Brasilia
				cArqNF := _cMaskPfDf
			ElseIf AllTrim(::c_empresa+::c_filial)$cPrefPOA // Totvs Porto Alegre
				cArqNF := cMaskPfPa
			ElseIf AllTrim(::c_empresa+::c_filial)$cPrefAlp // RMS /ALPHAVILLE
				cArqNF := cMaskPfAl
			ElseIf AllTrim(::c_empresa+::c_filial)$ cPrefASS //TOTVS ASSIS
				cArqNF := StrTran(StrTran(StrTran(cMPfAssis, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(::c_empresa+::c_filial) $ _cPrefPCGoi //PC Sistemas Goias
				cArqNF := StrTran(StrTran(StrTran(_cMaskGoias, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(::c_empresa+::c_filial) $ _cPrefCian //agora é MARINGA apenas
				cArqNF :=  StrTran ( StrTran(  StrTran(_cMaringMsk, '__NF__', Alltrim(T_F2->F2_NFELETR)) , '__CV__', AllTrim(T_F2->F2_CODNFE)), '__MARINGA__', AllTrim(cMaringa) )
			ElseIf AllTrim(::c_empresa+::c_filial) $ cPrefCAX   
				cArqNF := cMPfCaxias //a prefeitura não permite a abertura da nota automaticamente. Sempre força editar a chave
				//Foi solicitado direcionar apenas para o link base e nao montar : + "?Chave=" + fMontaChav("Caxias",T_F2->F2_SERIE,T_F2->F2_DOC,Alltrim(T_F2->F2_CODNFE),T_F2->F2_EMISSAO) 
			ElseIf AllTrim(::c_empresa+::c_filial) $ cPrefSSABA
				cArqNF := StrTran(StrTran(StrTran(cMaskPfba, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(cEmpAnt+cFilAnt) $ cPrefCurit  //CIASHOP CURITIBA
				cArqNF := StrTran(StrTran(_cMaskCuri, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__CV__', AllTrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(::c_empresa+::c_filial) $ _cPrefBEJun .Or. (T_F2->F2_SERIE $ GetMv("TI_PREFJND",,"JND") .And. ::c_empresa+::c_filial == "0002501000200")
				cArqNF := cMaskPfJun 
			ElseIf AllTrim(::c_empresa+::c_filial) $ Alltrim(cPrefRibPre)
				If !empty(Alltrim( T_F2->F2_XLNKNF2 ))
					cArqNF := Alltrim( T_F2->F2_XLNKNF2 )
				Else
					aCGCFilial := FWSM0Util():GetSM0Data( ::c_empresa , ::c_filial , { "M0_CGC" } )
					cArqNF := StrTran(StrTran(StrTran(_cMaskRPrt, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__CGC__', aCGCFilial[1][2]), '__CV__', UPPER(AllTrim(T_F2->F2_CODNFE)) )
				Endif
			ElseIf AllTrim(::c_empresa+::c_filial) $ _cPrefLime
				If !Empty( T_F2->F2_XLINKNF ) // Campo alimentado pelo PE F022ATUNF - no final do monitoramento da nota pela rotina FISA022 - Nfs-e
					cArqNF := Alltrim( T_F2->F2_XLINKNF )
				Else
					cArqNF := _cUrlLimei
				EndIf
			Else
				cArqNF := StrTran(StrTran(StrTran(cMaskPfsp, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			Endif

			//Se usa mudança de prefeitura de Alphaville para Saõ Paulo a partir de 01/12/20
			If lUsaAlpSP .and. AllTrim(::c_empresa+::c_filial)$cPrefAlp1 .and. Stod(T_F2->F2_EMISSAO) >= CtoD(GetMv("TI_DTNFALP",,"01/12/20"))
				cArqNF := StrTran(StrTran(StrTran(cMaskPfsp, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			EndIf

			T_F2->(dbCloseArea())
		EndIf 

		::cd_impnfiscal := cArqNF
		
	else
		U_xCONOUT("AUTOATENDIMENTO","RE_IMPRESSAO","REQUISICAO","")

		If RPCSetEnv(::c_empresa,::c_Filial)
			SetFGene ()
			_cPrefBEJun := GetMv("TI_PREFJUN",,"0002501002000|0000202000800")
			cMaskPfJun  := GetMv("TI_FAT22",,"https://jundiai.giss.com.br/consultar-autenticidade-documento-fiscal") //Jundiai
			lPrtConv	:= GetMv("TI_PRTCONV",,.T.)

			cQuery += "SELECT F2_EMISSAO,F2_NFELETR, F2_CODNFE, X5_DESCRI, F2_CHVNFE, F2_SERIE,F2_XLINKNF, UTL_RAW.CAST_TO_VARCHAR2(SF2.F2_XLNKNF2) F2_XLNKNF2 "
			cQuery += "FROM  " + RetFullName("SF2",::c_empresa)+" SF2 "

			If lPrtConv
				cQuery += "INNER JOIN " + RetFullName("SX5",::c_empresa)+" SX5 ON X5_FILIAL = '"+::c_Filial+"' AND X5_CHAVE = '"+::c_empresa+ "' AND X5_TABELA = 'XN' "
			Else
				cQuery += "INNER JOIN " + RetFullName("SX5",::c_empresa)+" SX5 ON X5_FILIAL = '"+::c_Filial+"' AND X5_CHAVE = "+::c_empresa+ " AND X5_TABELA = 'XN' "
			EndIf
			cQuery += "WHERE "
			cQuery += "SF2.F2_FILIAL  = '"+ ::c_filial +"'	AND "
			cQuery += "SF2.F2_CLIENTE = '"+ ::c_cliente +"'	AND "
			cQuery += "SF2.F2_LOJA    = '"+ ::c_loja +"' 	AND "
			cQuery += "SF2.F2_DOC     = '"+ ::cd_documento +"' AND "
			cQuery += "SF2.D_E_L_E_T_ = ' ' "

			cQuery := ChangeQuery(cQuery)

			dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQuery), "T_F2" , .F., .T.)

			cIM := allTrim(T_F2->(FieldGet(FieldPos("X5_DESCRI"))))
			// Se achar a IM na tabela do De/Para, faz a troca
			ZX5->(DbSetOrder(1))
			If ZX5->(DbSeek(::c_Filial+Pad("XN",Len(ZX5->ZX5_TABELA))+T_F2->F2_SERIE))
				cIM := Alltrim(ZX5->ZX5_DESCRI)
			Endif

			cMaskPfAl := "https://www.nf-eletronica.com.br/NFE.asp?a="+AllTrim(T_F2->F2_CHVNFE)+"&b=T"

			If AllTrim(::c_empresa+::c_filial)$ cPrefRecif // TOTVS NORDESTE
				cArqNF := StrTran(StrTran(StrTran(cMaskPfre, '__NF__', Alltrim(Right(Alltrim(T_F2->F2_NFELETR),8))), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			Elseif AllTrim(::c_empresa+::c_filial)$ cPrefSC // TOTVS SANTA CATARINA / JOINVILLE
				cArqNF := StrTran(StrTran(StrTran(cMaskPfjv, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			Elseif AllTrim(::c_empresa+::c_filial)$ cPrefFL // FLORIANOPOLIS - FEEDZ
				cArqNF := StrTran(StrTran(cMaskPfFL, '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			Elseif ( AllTrim(::c_empresa+::c_filial)$cPrefBH .Or. AllTrim(::c_empresa+::c_filial)$_cPrefPCBh ) //TOTVS BH OU PC SISTEMAS BH
				cArqNF := cMaskPfbh
			Elseif AllTrim(::c_empresa+::c_filial)$ cPrefRJ //TOTVS RIO DE JANEIRO
				cArqNF := StrTran(StrTran(StrTran(cMaskPfrj, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', StrTran(AllTrim(T_F2->F2_CODNFE),'-',''))
			ElseIf AllTrim(::c_empresa+::c_filial)$cPrefCam //TOTVS CAMPINAS
				cArqNF := cMaskPfcp
			ElseIf AllTrim(::c_empresa+::c_filial)$cPrefWealth //Wealth - System
				cArqNF := StrTran(cMaskPfWS, '__NF__', Alltrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(::c_empresa+::c_filial) $ cPrefMac // Totvs Macae
				cArqNF := StrTran(StrTran(StrTran(_cMaskPfMa,'__IM__',cIm),'__NF__', Alltrim(T_F2->F2_NFELETR)),'__CV__',StrTran(AllTrim(T_F2->F2_CODNFE),'-',''))
			ElseIf AllTrim(::c_empresa+::c_filial)$ cPrefDF // Totvs Brasilia
				cArqNF := _cMaskPfDf
			ElseIf AllTrim(::c_empresa+::c_filial)$cPrefPOA // Totvs Porto Alegre
				cArqNF := cMaskPfPa
			ElseIf AllTrim(::c_empresa+::c_filial)$cPrefAlp // RMS /ALPHAVILLE
				cArqNF := cMaskPfAl
			ElseIf AllTrim(::c_empresa+::c_filial)$ cPrefASS //TOTVS ASSIS
				cArqNF := StrTran(StrTran(StrTran(cMPfAssis, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(::c_empresa+::c_filial) $ _cPrefPCGoi //PC Sistemas Goias
				cArqNF := StrTran(StrTran(StrTran(_cMaskGoias, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(::c_empresa+::c_filial) $ _cPrefCian //agora é MARINGA apenas
				cArqNF :=  StrTran ( StrTran(  StrTran(_cMaringMsk, '__NF__', Alltrim(T_F2->F2_NFELETR)) , '__CV__', AllTrim(T_F2->F2_CODNFE)), '__MARINGA__', AllTrim(cMaringa) )
			ElseIf AllTrim(::c_empresa+::c_filial) $ cPrefSSABA
				cArqNF := StrTran(StrTran(StrTran(cMaskPfba, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(cEmpAnt+cFilAnt) $ cPrefCurit //CIASHOP CURITIBA
				cArqNF := StrTran(StrTran(_cMaskCuri, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__CV__', AllTrim(T_F2->F2_CODNFE))
			ElseIf AllTrim(::c_empresa+::c_filial) $ _cPrefBEJun .Or. (T_F2->F2_SERIE $ GetMv("TI_PREFJND",,"JND") .And. ::c_empresa+::c_filial == "0002501000200")
				cArqNF := cMaskPfJun
			ElseIf AllTrim(::c_empresa+::c_filial) $ Alltrim(cPrefRibPre)
				aCGCFilial := FWSM0Util():GetSM0Data( ::c_empresa , ::c_filial , { "M0_CGC" } )
				cArqNF := StrTran(StrTran(StrTran(_cMaskRPrt, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__CGC__', aCGCFilial[1][2]), '__CV__', UPPER(AllTrim(T_F2->F2_CODNFE)) )
			ElseIf AllTrim(::c_empresa+::c_filial) $ _cPrefLime
				If !Empty( T_F2->F2_XLINKNF ) // Campo alimentado pelo PE F022ATUNF - no final do monitoramento da nota pela rotina FISA022 - Nfs-e
					cArqNF := Alltrim( T_F2->F2_XLINKNF )
				Else
					cArqNF := _cUrlLimei
				EndIf
			Else
				cArqNF := StrTran(StrTran(StrTran(cMaskPfsp, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			Endif

			//Se usa mudança de prefeitura de Alphaville para Saõ Paulo a partir de 01/12/20
			If lUsaAlpSP .and. AllTrim(::c_empresa+::c_filial)$cPrefAlp1 .and. Stod(T_F2->F2_EMISSAO) >= CtoD(GetMv("TI_DTNFALP",,"01/12/20"))
				cArqNF := StrTran(StrTran(StrTran(cMaskPfsp, '__NF__', Alltrim(T_F2->F2_NFELETR)), '__IM__', cIM), '__CV__', AllTrim(T_F2->F2_CODNFE))
			EndIf

			T_F2->(dbCloseArea())

			::cd_impnfiscal := cArqNF
		EndIf

	ENDIF
	U_xCONOUT("AUTOATENDIMENTO","RE_IMPRESSAO","RETORNO","")

Return .T.



/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ DADOS_EXECUTIVO  ³ Autor ³ Cleyton Ferreira ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod DADOS_EXECUTIVO WsReceive c_empresa, c_filial, c_cliente, c_loja WsSend res_executivo WsService AUTOATENDIMENTO

//Public __CINTERNET := ""
	Local cod_cliente  :=c_cliente
	Local cod_loja	   :=c_loja
	Local cParDime	   := SuperGetMv("TI_DIMEON",,"2")
	Local lAchou       := .F. 
	Local cod_vend     := "" 
	
	If cParDime =='1'

		If Len(c_empresa) != 2 .or. Len(c_filial) != TamSx3("A1_FILIAL")[1] 
			Prepare Environment Empresa AllTrim(c_empresa) Filial AllTrim(c_filial)
		Else 
			If c_empresa != cEmpAnt 
				RpcClearEnv()
				RpcSetEnv( AllTrim(c_empresa), AllTrim(c_filial))//WS traz cFilant em branco,extraimos de cNumEmp
			Else 
				cFilAnt := c_filial
			EndIf 
		EndIf 
		U_xCONOUT("AUTOATENDIMENTO","DADOS_EXECUTIVO","REQUISICAO","")

		cEmpTemp := c_empresa
		cFilTemp := c_filial
		cod_vend := ""
		lAchou := U_AvalCli(cEmpTemp, @cod_cliente, @cod_loja, @cod_vend)

		SA1->(dbSetOrder(1))
		If  lAchou .And. SA1->(dbSeek(xFilial("SA1")+cod_cliente+cod_loja))
			If SA3->(dbSeek(xFilial("SA3")+cod_vend))

				::res_executivo:ce_codigo    := AllTrim(SA3->A3_COD)
				::res_executivo:ce_nome      := AllTrim(SA3->A3_NOME)
				::res_executivo:ce_endereco  := AllTrim(SA3->A3_END)
				::res_executivo:ce_bairro    := AllTrim(SA3->A3_BAIRRO)
				::res_executivo:ce_municipio := AllTrim(SA3->A3_MUN)
				::res_executivo:ce_cep       := AllTrim(SA3->A3_CEP)
				::res_executivo:ce_cgc       := AllTrim(SA3->A3_CGC)
				::res_executivo:ce_telefone  := AllTrim(SA3->A3_TEL)
				::res_executivo:ce_email     := AllTrim(SA3->A3_EMAIL)
				::res_executivo:ce_ddd       := AllTrim(SA3->A3_DDDTEL)
				::res_executivo:ce_celular   := AllTrim(SA3->A3_CEL)
			Else
				::res_executivo:ce_codigo    := AllTrim(SA1->A1_VEND)
				::res_executivo:ce_nome      := "VENDEDOR NAO ENCONTRADO"
				::res_executivo:ce_endereco  := "-"
				::res_executivo:ce_bairro    := "-"
				::res_executivo:ce_municipio := "-"
				::res_executivo:ce_cep       := "-"
				::res_executivo:ce_cgc       := "-"
				::res_executivo:ce_telefone  := "-"
				::res_executivo:ce_email     := "-"
				::res_executivo:ce_ddd	     := "-"
				::res_executivo:ce_celular   := "-"
			EndIf
		Else
			::res_executivo:ce_codigo    := AllTrim(c_cliente)
			::res_executivo:ce_nome      := "CLIENTE NAO ENCONTRADO"
			::res_executivo:ce_endereco  := "-"
			::res_executivo:ce_bairro    := "-"
			::res_executivo:ce_municipio := "-"
			::res_executivo:ce_cep       := "-"
			::res_executivo:ce_cgc       := "-"
			::res_executivo:ce_telefone  := "-"
			::res_executivo:ce_email     := "-"
			::res_executivo:ce_ddd	     := "-"
			::res_executivo:ce_celular   := "-"
		EndIf
		
	Else
		Prepare Environment Empresa AllTrim(c_empresa) Filial AllTrim(c_filial)
		U_xCONOUT("AUTOATENDIMENTO","DADOS_EXECUTIVO","REQUISICAO","")

		SA1->(dbSetOrder(1))
		If SA1->(dbSeek(xFilial("SA1")+AllTrim(c_cliente)+AllTrim(c_loja)))
			SA3->(dbSetOrder(1))
			If SA3->(dbSeek(xFilial("SA3")+SA1->A1_VEND))

				::res_executivo:ce_codigo    := AllTrim(SA3->A3_COD)
				::res_executivo:ce_nome      := AllTrim(SA3->A3_NOME)
				::res_executivo:ce_endereco  := AllTrim(SA3->A3_END)
				::res_executivo:ce_bairro    := AllTrim(SA3->A3_BAIRRO)
				::res_executivo:ce_municipio := AllTrim(SA3->A3_MUN)
				::res_executivo:ce_cep       := AllTrim(SA3->A3_CEP)
				::res_executivo:ce_cgc       := AllTrim(SA3->A3_CGC)
				::res_executivo:ce_telefone  := AllTrim(SA3->A3_TEL)
				::res_executivo:ce_email     := AllTrim(SA3->A3_EMAIL)
				::res_executivo:ce_ddd       := AllTrim(SA3->A3_DDDTEL)
				::res_executivo:ce_celular   := AllTrim(SA3->A3_CEL)
			Else
				::res_executivo:ce_codigo    := AllTrim(SA1->A1_VEND)
				::res_executivo:ce_nome      := "VENDEDOR NAO ENCONTRADO"
				::res_executivo:ce_endereco  := "-"
				::res_executivo:ce_bairro    := "-"
				::res_executivo:ce_municipio := "-"
				::res_executivo:ce_cep       := "-"
				::res_executivo:ce_cgc       := "-"
				::res_executivo:ce_telefone  := "-"
				::res_executivo:ce_email     := "-"
				::res_executivo:ce_ddd	     := "-"
				::res_executivo:ce_celular   := "-"
			EndIf
		Else
			::res_executivo:ce_codigo    := AllTrim(c_cliente)
			::res_executivo:ce_nome      := "CLIENTE NAO ENCONTRADO"
			::res_executivo:ce_endereco  := "-"
			::res_executivo:ce_bairro    := "-"
			::res_executivo:ce_municipio := "-"
			::res_executivo:ce_cep       := "-"
			::res_executivo:ce_cgc       := "-"
			::res_executivo:ce_telefone  := "-"
			::res_executivo:ce_email     := "-"
			::res_executivo:ce_ddd	     := "-"
			::res_executivo:ce_celular   := "-"
		EndIf
	ENDIF
	U_xCONOUT("AUTOATENDIMENTO","DADOS_EXECUTIVO","RETORNO","")

Return .T. 

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ RE_IMPRESSAO     ³ Autor ³ Cleyton Ferreira ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod RE_IMP_BOLETO WsReceive c_empresa, c_filial, c_cliente, c_loja, nf_recno WsSend cf_link WsService AUTOATENDIMENTO

	Local cQuery       := ""
	Local cRootPath    := ""
	Local cDirHist     := ""
	Local cDirHist2    := ""
	Local cDirHist3    := ""
	Local cDiretorOrig := ""
	Local cServer      := ""
	Local cMaskLink    := ""
	Local cMaskBoleto  := ""
	Local cMaskNF      := ""
	Local cMaskGUIA    := ""
	Local cSerie       := ""
	Local cNota        := ""
	Local cNFe         := ""
	Local cVerific     := ""
	Local cEmpAntiga   := ""
	Local cArqSemBoleto:= ""
	Local cCodCliAnt   := ""
	Local cClienteNew  := ""
	Local cCondPag     := ""
	Local aParcelas    := {}
	Local aBoletos     := {}
	Local nA           := 0
//Local nF1          := 0
//Local nF2          := 0
	Local nF3          := 0
	Local cParcela     := ""
	Local cArqBoleto   := ""
	Local cTI_FAT17    := GetMV('TI_FAT17',,'024')

	Private lParcAnt   := .F.

	Prepare Environment Empresa c_empresa Filial c_filial
	U_xCONOUT("AUTOATENDIMENTO","RE_IMP_BOLETO","REQUISICAO","")

	SE1->(dbGoTo(nf_recno))

	If SE1->(Eof())
		::cf_link := ""
		U_xCONOUT("AUTOATENDIMENTO","RE_IMP_BOLETO","(SE1 NF_RECNO NAO ENCONTRADO)- RETORNO","")
		Return .T.
	EndIf

	cQuery := "SELECT SF2.F2_DOC, SF2.F2_SERIE, "
	cQuery += "SF2.F2_NFELETR, SF2.F2_CODNFE, SF2.F2_COND "
	cQuery += "FROM "
	cQuery += RetSqlName("SF2")+" SF2 "
	cQuery += "WHERE "
	cQuery += "SF2.F2_FILIAL  = '"+xFilial("SF2")	+"' AND "
	cQuery += "SF2.F2_DOC     = '"+SE1->E1_NUM		+"' AND "
	cQuery += "SF2.F2_SERIE   = '"+SE1->E1_PREFIXO	+"' AND "
	cQuery += "SF2.F2_CLIENTE = '"+c_cliente		+"' AND "
	cQuery += "SF2.F2_LOJA    = '"+c_loja			+"' AND "
	cQuery += "SF2.D_E_L_E_T_ <> '*'"

	cQuery := ChangeQuery(cQuery)

	dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQuery), "B_F2" , .F., .T.)

	dbSelectArea("B_F2")
	dbGoTop()
	If Eof()
		::cf_Link := ""
		U_xCONOUT("AUTOATENDIMENTO","RE_IMP_BOLETO","(QUERY B_F2 NAO ENCONTRADO)- RETORNO","")
		Return .T.
	EndIf

	cSerie   := AllTrim(B_F2->F2_SERIE)
	cNota    := AllTrim(B_F2->F2_DOC)
	cNFe     := Alltrim(Str(Val(B_F2->F2_NFELETR)))
	cVerific := Alltrim(B_F2->F2_CODNFE)
	cCondPag := AllTrim(B_F2->F2_COND)

	B_F2->(dbCloseArea())

	FATM01VPAR(cSerie,cNota,@aParcelas)
	cEmpAntiga   := FcEmpant(cEmpAnt+cFilAnt)

	cRootPath	 := Alltrim(GetSrvProfString("ROOTPATH", ""))
	cDirHist	 := StrTran(StrTran(GetMV('TI_FAT06',,'\NF\EMP??'), '??', cEmpAntiga)+'\', '\\', '\')
	cDiretorOrig := StrTran(StrTran(GetMV('TI_FAT06',,'\NF\EMP??'), '??', cEmpAnt + cFilAnt)+'\', '\\', '\')

	cServer      := AllTrim(GetMV("TI_NFSRV",,"nfe.totvs.com.br"))
	cMaskLink    := 'http://'+cServer+'/w_rfatw001.apw?cTpEmail=X_A&cTipoLink=X_B&cEmpresa=X_C&cRPS=X_D&cSerie=X_F&cCliente=X_G&cCripto=X_H&cNFe=X_I&cVerific=X_J'

	cMaskNF      := GetMV('TI_FAT07',,'NFXXXXXXXXXZZZ.DOC')
	cMaskBoleto  := GetMV('TI_FAT08',,'BLXXXXXXXXXZZZY.HTM')
	cMaskGUIA    := GetMV('TI_FAT09',,'GUXXXXXXXXXZZZ.HTM')

	dbSelectArea("SA1")
	dbSetOrder(1)
	dbSeek(xFilial("SA1")+c_cliente+c_loja)

	If cEmpAnt == "00"
		If !Empty(SA1->A1_BCO5) .AND. !Empty(SA1->A1_AGECLI) .AND. !Empty(SA1->A1_CTACLI)
			cArqSemBoleto:= "\NF\IMAGENS-POR\Boleto\SEMBoletoDB.HTM"
		Else
			cArqSemBoleto:= "\NF\IMAGENS-POR\Boleto\SEMBoleto"+cEmpAnt+cFilAnt+".HTM"
		Endif
	Else
		cArqSemBoleto:= "\NF\IMAGENS-POR\Boleto\SEMBoleto.HTM"
	Endif

	cCodCliAnt := ""
	cClienteNew:= ""

	If cEmpAntiga == "00"
		cCodCliAnt := SA1->A1_CODMS
	ElseIf cEmpAntiga == "20"
		cCodCliAnt := SA1->A1_CODLG
	ElseIf cEmpAntiga == "30"
		cCodCliAnt := SA1->A1_CODRM
	Endif

	If !Empty(cCodCliAnt)
		cClienteNew := c_cliente
		cDirHist3   := cDirHist+U_DirNFeCli(cCodCliAnt, cDirHist,,cClienteNew)
	Else
		cDirHist3   := cDirHist+U_DirNFeCli(c_cliente, cDirHist)
	Endif

	cDirHist2  := cDirHist     + U_DirNFeCli(c_cliente, cDirHist)
	cDiretorNF := cDiretorOrig + U_DirNFeCli(c_cliente, cDiretorOrig)

	cNota:= IIF( Len(AllTrim(cNota)) == 6, '000'+AllTrim(cNota),AllTrim(cNota))

	If cPaisLoc <> "PTG" //Portugal

		If Len(aParcelas) > 0

			For nA := 1 to Len(aParcelas)

				cParcela := AllTrim(If(Empty(aParcelas[nA,1]),'001',aParcelas[nA,1]))

				If AllTrim(cParcela) <> Iif(Empty(SE1->E1_PARCELA),"001",AllTrim(SE1->E1_PARCELA))
					Loop
				EndIf

				NF3:= 0
				cDiretorio3 := ""
				cArqBoleto := StrTran(StrTran(StrTran(cMaskBoleto,'Y',cParcela),'XXXXXXXXX',AllTrim(cNota)),'ZZZ',Alltrim(cSerie))

				If File(cDiretorNF+cArqBoleto)
					cDiretorio3 := cDiretorNF
					NF3:=0
				ElseIf File(cDirHist2+cArqBoleto)
					cDiretorio3 := cDirHist2
					NF3:= 1
				ElseIf File(cDirHist3+cArqBoleto)
					cDiretorio3 := cDirHist3
					NF3:= 1
				Endif

				If Empty(cDiretorio3) .And. lParcAnt
					cArqBoleto := StrTran(StrTran(StrTran(cMaskBoleto,'Y','A'),'XXXXXXXXX',AllTrim(cNota)),'ZZZ',Alltrim(cSerie))
					If File(cDiretorNF+cArqBoleto)
						cDiretorio3 := cDiretorNF
						NF3:=0
					ElseIf File(cDirHist2+cArqBoleto)
						cDiretorio3 := cDirHist2
						NF3:= 1
					ElseIf File(cDirHist3+cArqBoleto)
						cDiretorio3 := cDirHist3
						NF3:= 1
					Endif
				EndIf

				lEnviaSBol := .F.

				If  SA1->A1_BCO1 == 'CX1' .Or. lEnviaSBol .Or. AllTrim(cCondPag) $ cTI_FAT17;
						.Or. (!Empty(SA1->A1_BCO5) .AND. !Empty(SA1->A1_AGECLI) .AND. !Empty(SA1->A1_CTACLI))
					Exit
				EndIf

				aAdd(aBoletos,{cParcela, cDiretorio3})

				exit

			Next nA
		EndIf
	Endif

	cCripto := U_CNPJCPF(Alltrim(IIF(NF3 = 0, cEmpAnt+cFilAnt,FcEmpant(cEmpAnt+cFilAnt)))+Alltrim(c_cliente)+Alltrim(cNota),.T.)

	cArqBoleto := StrTran(StrTran(StrTran(StrTran(StrTran(StrTran(StrTran(StrTran(StrTran(cMaskLink,'X_A','99'),'X_B','BL'),'X_C',Iif(NF3==0,cEmpAnt+cFilAnt,FcEmpant(cEmpAnt+cFilAnt))),'X_D',cNota),'X_F',cSerie),'X_G',c_cliente),'X_H',cCripto),'X_I',cNFe),'X_J',cVerific)

	If Len(aBoletos) > 0
		::cf_link := cArqBoleto+'&cParcela='+aBoletos[1,1]
	Else
		::cf_link := ""
	EndIf

	U_xCONOUT("AUTOATENDIMENTO","RE_IMP_BOLETO","RETORNO","")

Return .T.

//-----------------------------------------------------------------------------
/*/ {Protheus.doc} Function
	Descricao Método DADOS_CADASTRAIS
	Uso      TDI - Totvs
	<AUTHOR> Ferreira Alves
	@obs     Carvalho-Mobile - 27/10/2015
	@version P12
	@since   07/10/2015
/*/
//-----------------------------------------------------------------------------
WsMethod DADOS_CADASTRAIS WsReceive c_empresa, c_filial, c_cliente, c_loja WsSend ag_cadastrais WsService AUTOATENDIMENTO

	Local aCampos     := {}
	Local aAreaSX3    := {}
	Local lAchou      := .F.
	Local lInativo    := .F.
	Local lTemZWW     := .F.
	Local nc          := 0
	Local cConteudo   := ""
	Local cQuery      := ""
	Local cField	  	:= ""
	Local oListaDados
	Local cCliente 	:= cLoja := ""
	Local cod_cliente  :=c_cliente
	Local cod_loja	   :=c_loja
	Local cParDime		:= SuperGetMv("TI_DIMEON",,"2")
	Local lAchou2      := .F. 

	If cParDime =='1'

		If Len(c_empresa) != 2 .or. Len(c_filial) != TamSx3("A1_FILIAL")[1] 
			Prepare Environment Empresa AllTrim(c_empresa) Filial AllTrim(c_filial)
		Else 
			If AllTrim(c_empresa) != cEmpAnt 
				RpcClearEnv()
				RpcSetEnv( AllTrim(c_empresa), AllTrim(c_filial))//WS traz cFilant em branco,extraimos de cNumEmp
			Else 
				cFilAnt := AllTrim(c_filial)
			EndIf 
		EndIf 
		U_xCONOUT("AUTOATENDIMENTO","DADOS_CADASTRAIS","REQUISICAO","")

		cEmpTemp := c_empresa
		cFilTemp := c_filial
		lAchou2 := U_AvalCli(cEmpTemp, @cod_cliente, @cod_loja)//DADOS_CADASTRAIS

			//Fim Alteração João Gabriel
		aAreaSX3 	:= SX3->(GetArea())
		//cod_cliente	:= Left(cod_cliente + Space(Len(SA1->A1_COD)), Len(SA1->A1_COD))
		//cod_loja		:= Left(cod_loja + Space(Len(SA1->A1_LOJA)), Len(SA1->A1_LOJA))

		SA1->(dbSetOrder(1))
		If lAchou2 .And. SA1->(dbSeek(xFilial("SA1")+cod_cliente+cod_loja))
			lInativo := SA1->A1_XSITCLI == "2"
			AI0->(dbSetOrder(1))
			If AI0->(dbSeek(xFilial("AI0")+cod_cliente+cod_loja))
				lAchou := .T.
			Endif
		EndIf

		If lAchou 
			aAdd(aCampos,{"A1_PESSOA"	,"Pessoa"					,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_NOME"		,"Nome"					,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_NREDUZ"	,"Nome Reduzido"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_END"		,"Endereço"		   		,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_BAIRRO"	,"Bairro"					,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CEP"		,"CEP"		   	   			,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_EST"		,"Estado"					,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_MUN"		,"Municipio"				,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_ENDCOB"	,"Endereço Cobrança"		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_BAIRROC"	,"Bairro Cobrança"		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CEPC"		,"CEP Cobrança"			,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_ESTC"		,"Estado Cobrança"		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_MUNC"		,"Municipio Cobrança"	,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_DDI"		,"DDI"						,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_DDD"		,"DDD"						,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_TEL"		,"Telefone"				,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_FAX"		,"FAX"						,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CONTATO"	,"Contato"					,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CARGO1"	,"Cargo"					,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_EMAIL"		,"e-Mail"					,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_HPAGE"		,"Home Page"				,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CGC"		,"CNPJ"		   			,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_SIMPNAC"	,"Simples Nacional"		,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_INSCR"		,"Inscrição Estadual"	,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CNAE"		,"Codigo CNAE"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_SUFRAMA"	,"Codigo Suframa"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_INCISS"	,"ISS Incluso ?"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_RECISS"	,"Recolhe ISS ?"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_RECCOFI"	,"Recolhe Cofins ?"		,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_RECPIS"	,"Recolhe PIS ?"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_RECCSLL"	,"Recolhe CSLL ?"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_RECIRRF"	,"Recolhe IRRF ?"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTVER"	,"Versao do Sistema"		,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTBSI"	,"Build do Sistema"		,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTSOT"	,"Build do TopConnect"	,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTBAS"	,"Data Base"  			,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTSOS"	,"S.O. Servidor"			,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTSOT"	,"S.O. TopConnect"		,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTSOB"	,"S.O. Data Base"			,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTSOR"	,"S.O. Remote"			,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_XEMAILF"	,"E-Mail Financeiro"		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XENTRP"	,"Entrega NF para  "		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_TEL"		,"Telefone"				,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_XSITCLI"  ,"Situacao Cliente"		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPRSMS"	,"Primeiro SMS"			,"",0,0,"","CADASTRAIS"	,"D","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XDTURE"	,"Data Ultimo Recorr"	,"",0,0,"","CADASTRAIS"	,"D","","VISUALIZAR"})

			ZL1->(dbSetOrder(2))
			SX3->(dbSetOrder(2))
			
			For nc := 1 TO Len(aCampos)
				SX3->(dbSeek(aCampos[nc,01]))

				aCampos[nc,03] 	:= SX3->(FieldGet(FieldPos("X3_TIPO")))
				aCampos[nc,04] 	:= SX3->(FieldGet(FieldPos("X3_TAMANHO")))
				aCampos[nc,05] 	:= SX3->(FieldGet(FieldPos("X3_DECIMAL")))
				aCampos[nc,09] 	:= SX3->(FieldGet(FieldPos("X3_CBOX")))

				cQuery := "SELECT ZWW_CONTEU FROM " + REtSqlName("ZWW")+" ZWW "
				cQuery +=  "WHERE D_E_L_E_T_ = ' ' AND ZWW_FILIAL = '"+xFilial("ZWW")+"' "
				cQuery +=    "AND ZWW_CODCLI = '"+cod_cliente+"' "
				cQuery +=    "AND ZWW_LOJCLI = '"+cod_loja   +"' "
				cQuery +=    "AND ZWW_CAMPO  = '"+AllTrim(aCampos[nc,01])+"' "
				cQuery +=    "AND ZWW_STATUS = 'A' "
				cQuery +=  "ORDER BY ZWW_DATINC, ZWW_HRINCL"

				cQuery := ChangeQuery(cQuery)

				dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_ZWW",.T.,.T.)

				lTemZWW := .F.
				While T_ZWW->(!Eof())
					lTemZWW := .T.
					cConteudo := T_ZWW->ZWW_CONTEU
					T_ZWW->(dbSkip())
				EndDo
				T_ZWW->(dbCloseArea())

				If !lTemZWW
					cField := "S" + Left(aCampos[nc,01], 2)
					If Left(aCampos[nc,01], 3) = "AI0"
						cField := Left(aCampos[nc,01], 3)
					EndIf

					If AllTrim(aCampos[nc,03]) == "N"
						cConteudo := StrZero(&(cField + "->"+AllTrim(aCampos[nc,01])),aCampos[nc,05])
					ElseIf AllTrim(aCampos[nc,03]) == "D"
						cConteudo := DtoC(&(cField + "->"+AllTrim(aCampos[nc,01])))
					ElseIf AllTrim(aCampos[nc,03]) == "L"
						cConteudo := Iif(&(cField + "->"+AllTrim(aCampos[nc,01])),"T","F")
					ElseIf AllTrim(aCampos[nc,03]) == "M"
						cConteudo := MSMM(&(cField + "->"+AllTrim(aCampos[nc,01])))
					Else
						cConteudo := &(cField + "->"+AllTrim(aCampos[nc,01]))
					EndIf
				Else
					aCampos[nc,08] := "S"
				EndIf
				aCampos[nc,06] := cConteudo

				If ZL1->(dbSeek(xFilial("ZL1")+AllTrim(aCampos[nc,01])))
					aCampos[nc,10] := "ALTERAR"
				EndIf

				oListaDados:= wsclassnew("listacadastrais")
				oListaDados:cg_campo	:= aCampos[nc,01]
				oListaDados:cg_titulo	:= aCampos[nc,02]
				oListaDados:cg_tipo		:= aCampos[nc,03]
				oListaDados:ng_tamanho	:= aCampos[nc,04]
				oListaDados:ng_decimais	:= aCampos[nc,05]
				oListaDados:cg_conteudo	:= aCampos[nc,06]
				oListaDados:cg_folder	:= aCampos[nc,07]
				oListaDados:cg_flag		:= aCampos[nc,08]
				oListaDados:cg_combobox := aCampos[nc,09]
				oListaDados:cg_permissao:= aCampos[nc,10]
				aAdd(::ag_cadastrais,oListaDados)
			Next
		Else
				oListaDados := wsclassnew("listacadastrais")
				oListaDados:cg_campo	:= Iif(lInativo,"Cliente Inativo","")
				oListaDados:cg_titulo	:= ""
				oListaDados:cg_tipo	 	:= ""
				oListaDados:ng_tamanho	:= 0
				oListaDados:ng_decimais	:= 0
				oListaDados:cg_conteudo  := ""
				oListaDados:cg_folder 	:= ""
				oListaDados:cg_flag 		:= "N"
				oListaDados:cg_combobox  := ""
				oListaDados:cg_permissao := "VISUALIZAR"
				aAdd(::ag_cadastrais,oListaDados)
		Endif
		
		RestArea(aAreaSX3)
	else

		Prepare Environment Empresa AllTrim(c_empresa) Filial AllTrim(c_filial)

		U_xCONOUT("AUTOATENDIMENTO","DADOS_CADASTRAIS","REQUISICAO","")

		aAreaSX3 	:= SX3->(GetArea())
		cCliente	:= Left(c_cliente + Space(Len(SA1->A1_COD)), Len(SA1->A1_COD))
		cLoja		:= Left(c_loja + Space(Len(SA1->A1_LOJA)), Len(SA1->A1_LOJA))

		SA1->(dbSetOrder(1))
		If SA1->(dbSeek(xFilial("SA1")+cCliente+cLoja))
			lInativo := SA1->A1_XSITCLI == "2"
			AI0->(dbSetOrder(1))
			If AI0->(dbSeek(xFilial("AI0")+cCliente+cLoja))
				lAchou := .T.
			Endif
		EndIf

		If lAchou
			aAdd(aCampos,{"A1_PESSOA"	,"Pessoa"					,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_NOME"		,"Nome"					,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_NREDUZ"	,"Nome Reduzido"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_END"		,"Endereço"		   		,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_BAIRRO"	,"Bairro"					,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CEP"		,"CEP"		   	   			,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_EST"		,"Estado"					,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_MUN"		,"Municipio"				,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_ENDCOB"	,"Endereço Cobrança"		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_BAIRROC"	,"Bairro Cobrança"		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CEPC"		,"CEP Cobrança"			,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_ESTC"		,"Estado Cobrança"		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_MUNC"		,"Municipio Cobrança"	,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_DDI"		,"DDI"						,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_DDD"		,"DDD"						,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_TEL"		,"Telefone"				,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_FAX"		,"FAX"						,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CONTATO"	,"Contato"					,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CARGO1"	,"Cargo"					,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_EMAIL"		,"e-Mail"					,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_HPAGE"		,"Home Page"				,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CGC"		,"CNPJ"		   			,"",0,0,"","FISCAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_SIMPNAC"	,"Simples Nacional"		,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_INSCR"		,"Inscrição Estadual"	,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_CNAE"		,"Codigo CNAE"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_SUFRAMA"	,"Codigo Suframa"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_INCISS"	,"ISS Incluso ?"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_RECISS"	,"Recolhe ISS ?"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_RECCOFI"	,"Recolhe Cofins ?"		,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_RECPIS"	,"Recolhe PIS ?"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_RECCSLL"	,"Recolhe CSLL ?"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_RECIRRF"	,"Recolhe IRRF ?"			,"",0,0,"","GERAIS"		,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTVER"	,"Versao do Sistema"		,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTBSI"	,"Build do Sistema"		,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTSOT"	,"Build do TopConnect"	,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTBAS"	,"Data Base"  			,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTSOS"	,"S.O. Servidor"			,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTSOT"	,"S.O. TopConnect"		,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTSOB"	,"S.O. Data Base"			,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPTSOR"	,"S.O. Remote"			,"",0,0,"","PROTHEUS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_XEMAILF"	,"E-Mail Financeiro"		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XENTRP"	,"Entrega NF para  "		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_TEL"		,"Telefone"				,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"A1_XSITCLI"  ,"Situacao Cliente"		,"",0,0,"","CADASTRAIS"	,"N","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XPRSMS"	,"Primeiro SMS"			,"",0,0,"","CADASTRAIS"	,"D","","VISUALIZAR"})
			aAdd(aCampos,{"AI0_XDTURE"	,"Data Ultimo Recorr"	,"",0,0,"","CADASTRAIS"	,"D","","VISUALIZAR"})

			ZL1->(dbSetOrder(2))
			SX3->(dbSetOrder(2))
			For nc := 1 TO Len(aCampos)
				SX3->(dbSeek(aCampos[nc,01]))

				aCampos[nc,03] 	:= SX3->(FieldGet(FieldPos("X3_TIPO")))
				aCampos[nc,04] 	:= SX3->(FieldGet(FieldPos("X3_TAMANHO")))
				aCampos[nc,05] 	:= SX3->(FieldGet(FieldPos("X3_DECIMAL")))
				aCampos[nc,09] 	:= SX3->(FieldGet(FieldPos("X3_CBOX")))

				cQuery := "SELECT ZWW_CONTEU FROM " + REtSqlName("ZWW")+" ZWW "
				cQuery +=  "WHERE D_E_L_E_T_ = ' ' AND ZWW_FILIAL = '"+xFilial("ZWW")+"' "
				cQuery +=    "AND ZWW_CODCLI = '"+cCliente+"' "
				cQuery +=    "AND ZWW_LOJCLI = '"+cLoja   +"' "
				cQuery +=    "AND ZWW_CAMPO  = '"+AllTrim(aCampos[nc,01])+"' "
				cQuery +=    "AND ZWW_STATUS = 'A' "
				cQuery +=  "ORDER BY ZWW_DATINC, ZWW_HRINCL"

				cQuery := ChangeQuery(cQuery)

				dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_ZWW",.T.,.T.)

				lTemZWW := .F.
				While T_ZWW->(!Eof())
					lTemZWW := .T.
					cConteudo := T_ZWW->ZWW_CONTEU
					T_ZWW->(dbSkip())
				EndDo
				T_ZWW->(dbCloseArea())

				If !lTemZWW
					cField := "S" + Left(aCampos[nc,01], 2)
					If Left(aCampos[nc,01], 3) = "AI0"
						cField := Left(aCampos[nc,01], 3)
					EndIf

					If AllTrim(aCampos[nc,03]) == "N"
						cConteudo := StrZero(&(cField + "->"+AllTrim(aCampos[nc,01])),aCampos[nc,05])
					ElseIf AllTrim(aCampos[nc,03]) == "D"
						cConteudo := DtoC(&(cField + "->"+AllTrim(aCampos[nc,01])))
					ElseIf AllTrim(aCampos[nc,03]) == "L"
						cConteudo := Iif(&(cField + "->"+AllTrim(aCampos[nc,01])),"T","F")
					ElseIf AllTrim(aCampos[nc,03]) == "M"
						cConteudo := MSMM(&(cField + "->"+AllTrim(aCampos[nc,01])))
					Else
						cConteudo := &(cField + "->"+AllTrim(aCampos[nc,01]))
					EndIf
				Else
					aCampos[nc,08] := "S"
				EndIf
				aCampos[nc,06] := cConteudo

				If ZL1->(dbSeek(xFilial("ZL1")+AllTrim(aCampos[nc,01])))
					aCampos[nc,10] := "ALTERAR"
				EndIf

				oListaDados:= wsclassnew("listacadastrais")
				oListaDados:cg_campo	:= aCampos[nc,01]
				oListaDados:cg_titulo	:= aCampos[nc,02]
				oListaDados:cg_tipo		:= aCampos[nc,03]
				oListaDados:ng_tamanho	:= aCampos[nc,04]
				oListaDados:ng_decimais	:= aCampos[nc,05]
				oListaDados:cg_conteudo	:= aCampos[nc,06]
				oListaDados:cg_folder	:= aCampos[nc,07]
				oListaDados:cg_flag		:= aCampos[nc,08]
				oListaDados:cg_combobox := aCampos[nc,09]
				oListaDados:cg_permissao:= aCampos[nc,10]
				aAdd(::ag_cadastrais,oListaDados)
			Next
		Else
			oListaDados := wsclassnew("listacadastrais")
			oListaDados:cg_campo	:= Iif(lInativo,"Cliente Inativo","")
			oListaDados:cg_titulo	:= ""
			oListaDados:cg_tipo	 	:= ""
			oListaDados:ng_tamanho	:= 0
			oListaDados:ng_decimais	:= 0
			oListaDados:cg_conteudo  := ""
			oListaDados:cg_folder 	:= ""
			oListaDados:cg_flag 		:= "N"
			oListaDados:cg_combobox  := ""
			oListaDados:cg_permissao := "VISUALIZAR"
			aAdd(::ag_cadastrais,oListaDados)
		Endif

		RestArea(aAreaSX3)


	EndIF

	U_xCONOUT("AUTOATENDIMENTO","DADOS_CADASTRAIS","RETORNO","")

Return .T. 


//-----------------------------------------------------------------------------
/*/ {Protheus.doc} Function
	Descricao Método GRAVA_DADOS
	Uso      TDI - Totvs
	<AUTHOR> Ferreira Alves
	@obs     Carvalho-Mobile - 27/10/2015
	@version P12
	@since   08/10/2015
/*/
//-----------------------------------------------------------------------------
WsMethod GRAVA_DADOS WsReceive c_empresa, c_filial, c_cliente, c_loja, res_cadastrais WsSend ch_resultado WsService AUTOATENDIMENTO

	Local aAreaSX3    := {}
	Local nc          := 0
	Local cConteudo   := ""
	Local cContAnt    := ""
	Local cQuery      := ""
	Local nV         	:= 0
	Local nX         	:= 0
	Local cCampo      := ""
	Local aSigaMat   	:= WS_Sw_SigaMat()
	Local aFilInc       := {} 
	Local cCliente 	:= cLoja := ""
	Local cod_cliente  :=c_cliente
	Local cod_loja	   :=c_loja
	Local cEmpTemp   	:= ""
	Local cSit:=""
	Local cParDime		:= SuperGetMv("TI_DIMEON",,"2")
	Local lAchou       := .F. 
	Local cod_vend     := ""
	Local cEmpOld      := "" 
	
	If cParDime =='1'

		U_xCONOUT("AUTOATENDIMENTO","GRAVA_DADOS","REQUISICAO","")

		For nV := 1 To Len(aSigaMat)
			cEmpTemp := aSigaMat[nV,1]
			cFilTemp := aSigaMat[nV,2]

			If AllTrim(aSigaMat[nV,1]) <> cEmpOld 
				RpcClearEnv()
				RpcSetEnv(cEmpTemp,cFilTemp)//WS traz cFilant em branco,extraimos de cNumEmp
				
				cEmpOld     := AllTrim(aSigaMat[nV,1]) 						
				cCliente	:= Left(c_cliente + Space(Len(SA1->A1_COD)), Len(SA1->A1_COD))
				cLoja		:= Left(c_loja + Space(Len(SA1->A1_LOJA)), Len(SA1->A1_LOJA))
				cod_vend    := ""
				cSit        := ""
				lAchou      := U_AvalCli(cEmpTemp, @cCliente, @cLoja, @cod_vend, @cSit)
				
				If lAchou 
					
					aAreaSX3 := SX3->(GetArea())

					ZL1->(dbSetOrder(2))
					SA1->(dbSetOrder(1))

					If cSit == "2"
						::ch_resultado := "CLIENTE INATIVO"
					Else
						SX3->(dbSetOrder(2))
						For nc := 1 To Len(res_cadastrais:ah_dadosalterados)
							cCampo    := AllTrim(res_cadastrais:ah_dadosalterados[nc]:ch_campo)
							cConteudo := AllTrim(res_cadastrais:ah_dadosalterados[nc]:ch_conteudo)
							If AllTrim(res_cadastrais:ah_dadosalterados[nc]:ch_flag) == "S" .And. ZL1->(dbSeek(xFilial("ZL1")+cCampo))
								dbSelectArea("ZWW")
								cQuery := "SELECT R_E_C_N_O_ AS RECNO FROM "
								cQuery += REtSqlName("ZWW")+" ZWW "
								cQuery += "WHERE "
								cQuery += "ZWW_FILIAL = '"+xFilial("ZWW")+"' AND "
								cQuery += "ZWW_CODCLI = '"+cCliente+"' AND "
								cQuery += "ZWW_LOJCLI = '"+cLoja   +"' AND "
								cQuery += "ZWW_CAMPO  = '"+cCampo+"' AND "
								cQuery += "ZWW_STATUS = 'A' AND "
								cQuery += "D_E_L_E_T_ <> '*'"

								cQuery := ChangeQuery(cQuery)

								dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_ZWW",.T.,.T.)

								//===============================
								//  Carrega array para trabalho
								//===============================
								dbSelectArea("T_ZWW")
								dbGoTop()
								While T_ZWW->(!Eof())
									ZWW->(dbGoTo(T_ZWW->RECNO))
									RecLock("ZWW",.F.)
									dbDelete()
									ZWW->(MsUnLock())
									T_ZWW->(dbSkip())
								EndDo
								T_ZWW->(dbCloseArea())

								SX3->(dbSeek(cCampo))

								cConAnt := "SA1->"+cCampo
								If Left(cCampo, 3) = "AI0"	//-- Complemento de Cliente
									cConAnt := "AI0->"+cCampo
								EndIf
										
								DbUseArea(.T.,"TOPCONN",RetFullName("ZWW",cEmpTemp),,.T.,.T.)
										
								dbGoTop()

								RecLock("ZWW", .T.)
								ZWW->ZWW_FILIAL := xFilial("ZWW")
								ZWW->ZWW_CODIGO := GetSxEnum("ZWW","ZWW_CODIGO")
								ZWW->ZWW_DATINC := dDataBase
								ZWW->ZWW_HRINCL := Time()
								ZWW->ZWW_CODCLI := cCliente
								ZWW->ZWW_LOJCLI := cLoja
								ZWW->ZWW_CAMPO  := cCampo
								ZWW->ZWW_CONTEU := cConteudo
								ZWW->ZWW_CONTAN := &cContAnt
								ZWW->ZWW_STATUS := "A" // Em Aberto
								ZWW->(MsUnLock())
							EndIf
						Next
					EndIf
				EndIf 

			Else 
				cFilAnt := cFilTemp
			EndIf 

			
		Next nV
		RestArea(aAreaSX3)

	else
		Prepare Environment Empresa AllTrim(c_empresa) Filial AllTrim(c_filial)

		U_xCONOUT("AUTOATENDIMENTO","GRAVA_DADOS","REQUISICAO","")

		cCliente	:= Left(c_cliente + Space(Len(SA1->A1_COD)), Len(SA1->A1_COD))
		cLoja		:= Left(c_loja + Space(Len(SA1->A1_LOJA)), Len(SA1->A1_LOJA))

		aAreaSX3 := SX3->(GetArea())

		ZL1->(dbSetOrder(2))
		SA1->(dbSetOrder(1))

		If SA1->(dbSeek(xFilial("SA1")+cCliente+cLoja))
			AI0->(dbSetOrder(1))
			If AI0->(dbSeek(xFilial("AI0")+cCliente+cLoja))
				If SA1->A1_XSITCLI == "2"
					::ch_resultado := "CLIENTE INATIVO"
				Else
					SX3->(dbSetOrder(2))
					For nc := 1 To Len(res_cadastrais:ah_dadosalterados)
						cCampo    := AllTrim(res_cadastrais:ah_dadosalterados[nc]:ch_campo)
						cConteudo := AllTrim(res_cadastrais:ah_dadosalterados[nc]:ch_conteudo)
						If AllTrim(res_cadastrais:ah_dadosalterados[nc]:ch_flag) == "S" .And. ZL1->(dbSeek(xFilial("ZL1")+cCampo))
							dbSelectArea("ZWW")
							cQuery := "SELECT R_E_C_N_O_ AS RECNO FROM "
							cQuery += REtSqlName("ZWW")+" ZWW "
							cQuery += "WHERE "
							cQuery += "ZWW_FILIAL = '"+xFilial("ZWW")+"' AND "
							cQuery += "ZWW_CODCLI = '"+cCliente+"' AND "
							cQuery += "ZWW_LOJCLI = '"+cLoja   +"' AND "
							cQuery += "ZWW_CAMPO  = '"+cCampo+"' AND "
							cQuery += "ZWW_STATUS = 'A' AND "
							cQuery += "D_E_L_E_T_ <> '*'"

							cQuery := ChangeQuery(cQuery)

							dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"T_ZWW",.T.,.T.)

							//===============================
							//  Carrega array para trabalho
							//===============================
							dbSelectArea("T_ZWW")
							dbGoTop()
							While T_ZWW->(!Eof())
								ZWW->(dbGoTo(T_ZWW->RECNO))
								RecLock("ZWW",.F.)
								dbDelete()
								ZWW->(MsUnLock())
								T_ZWW->(dbSkip())
							EndDo
							T_ZWW->(dbCloseArea())

							SX3->(dbSeek(cCampo))

							cConAnt := "SA1->"+cCampo
							If Left(cCampo, 3) = "AI0"	//-- Complemento de Cliente
								cConAnt := "AI0->"+cCampo
							EndIf

							RecLock("ZWW", .T.)
							ZWW->ZWW_FILIAL := xFilial("ZWW")
							ZWW->ZWW_CODIGO := GetSxEnum("ZWW","ZWW_CODIGO")
							ZWW->ZWW_DATINC := dDataBase
							ZWW->ZWW_HRINCL := Time()
							ZWW->ZWW_CODCLI := cCliente
							ZWW->ZWW_LOJCLI := cLoja
							ZWW->ZWW_CAMPO  := cCampo
							ZWW->ZWW_CONTEU := cConteudo
							ZWW->ZWW_CONTAN := &cContAnt
							ZWW->ZWW_STATUS := "A" // Em Aberto
							ZWW->(MsUnLock())
						EndIf
					Next
				EndIf
			Else
				::ch_resultado := "FALHA CADASTRAL - CLIENTE NAO ENCONTRADO EM AI0"
			Endif
		Else
			::ch_resultado := "CLIENTE NAO ENCONTRADO EM SA1"
		EndIf

		RestArea(aAreaSX3)

	EndIF
	
	U_xCONOUT("AUTOATENDIMENTO","GRAVA_DADOS","RETORNO","")

Return .T.

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³CONSULTA_CONTRATOS³ Autor ³ Cleyton Ferreira ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONSULTA_CONTRATOS WsReceive n_portal, c_cliente, c_loja WsSend ai_contratos WsService AUTOATENDIMENTO

	Local aSigaMat   	:= WS_Sw_SigaMat()
	Local aDados     	:= {}
	Local cQuery     	:= ""
	Local cQryComp   	:= ""
	Local cEmpTemp   	:= ""
	Local cFilTemp  	:= ""
	Local nPos	     	:= 0
	Local nV         	:= 1
	Local nX			:= 0
	Local nL			:= 0
	Local oCabecalho
	Local oItens
	Local aTipGrp		:= STRTOKARR(GetMV("TI_WSGRP",,"9999"),"/")
	Local cTipGrp		:= ""
	Local cDesativa     := GetMV("TI_FATS01A",,"N")  //Desativa a consulta de contratos: S=Sim,N=Nao
	Local cod_cliente  :=c_cliente
	Local cod_loja	   :=c_loja
	Local lAchou       := .F. 
	Local cParDime		:= SuperGetMv("TI_DIMEON",,"2")
	Default n_portal	:= 1

	If cParDime =='1'
		U_xCONOUT("AUTOATENDIMENTO","CONSULTA_CONTRATOS","REQUISICAO","")

			For nX := 1 To Len(aTipGrp)
				cTipGrp += "'"+aTipGrp[nx]+Iif(Len(aTipGrp)==nX, "'","',")
			Next
			//cTipGrp := SubStr(cTipGrp,2,Len(cTipGrp)-3)

			If cDesativa == "S"  //Desativa a consulta de contratos: S=Sim,N=Nao
				nPos := 0
			ElseIf n_portal == 1

			For nV := 1 To Len(aSigaMat)
				IF cEmpTemp <> aSigaMat[nV,1]
				cEmpTemp   := aSigaMat[nV,1]
				cFilTemp   := aSigaMat[nV,2]
				cod_cliente:= c_cliente 
				cod_loja   := c_loja 

				RpcClearEnv()
				RpcSetEnv(cEmpTemp,cFilTemp)
				
				aTipGrp		:= RetMvParam("TI_WSGRP") 
				cTipGrp		:= ""
				For nX := 1 To Len(aTipGrp)
					cTipGrp += "'"+aTipGrp[nx]+Iif(Len(aTipGrp)==nX, "'","',")
				Next

				lAchou := U_AvalCli(cEmpTemp, @cod_cliente, @cod_loja)

				cQuery := "	SELECT	 SB1.B1_DESC "
				cQuery += ",CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA "
				cQuery += ",CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_ORIGEM	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VENCTO	,CNB.CNB_DATASS "
				cQuery += ",CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC 	,CNB.CNB_STATRM 	,CNB.CNB_UM     	,CNB.CNB_NOTASE 	,CNB.CNB_DIAVEN "
				cQuery += ",CNB.CNB_IMPOST 	,CNB.CNB_INDCTR 	,CNB.CNB_TS     	,CNB.CNB_PROPOS 	,CNB.CNB_VERPRO 	,CNB.R_E_C_N_O_	,CNB_ULTRJT "
				cQuery += ",SBM.BM_GRUPO   	,SBM.BM_PICPAD "
				cQuery += ",CNC.CNC_CLIENT 	,CNC.CNC_LOJACL "
				cQuery += ",SE4.E4_FORMA "
				cQuery += "FROM "+ RetFullName("CNA",cEmpTemp)+" CNA  "
				cQuery += "INNER JOIN " + RetFullName("CNB",cEmpTemp)+ " CNB  "
				cQuery += "ON	CNB.CNB_FILIAL		= CNA.CNA_FILIAL "
				cQuery += "AND CNB.CNB_CONTRA	= CNA.CNA_CONTRA "
				cQuery += "AND CNB.CNB_REVISA	= CNA.CNA_REVISA "
				cQuery += "AND CNB.D_E_L_E_T_	<> '*' "
				cQuery += "INNER JOIN " + RetFullName("CNC",cEmpTemp) + " CNC   "
				cQuery += "ON	CNC.CNC_FILIAL		= CNA.CNA_FILIAL "
				cQuery += "AND CNC.CNC_NUMERO	= CNA.CNA_CONTRA "
				cQuery += "AND CNC.D_E_L_E_T_	<> '*' "
				cQuery += "INNER JOIN " + RetFullName("SB1",cEmpTemp) + " SB1   "
				cQuery += "ON SB1.B1_FILIAL			= CNB.CNB_FILIAL "
				cQuery += "AND SB1.B1_COD		= CNB.CNB_PRODUT "
				cQuery += "AND SB1.D_E_L_E_T_	<> '*' "
				cQuery += "INNER JOIN " + RetFullName("SBM",cEmpTemp) + " SBM  "
				cQuery += "ON SBM.BM_FILIAL			= SB1.B1_FILIAL "
				cQuery += "AND SBM.BM_GRUPO		= SB1.B1_GRUPO "
				cQuery += "AND SBM.D_E_L_E_T_	<> '*' "
				cQuery += "INNER JOIN " + RetFullName("SE4",cEmpTemp) + " SE4   "
				cQuery += "ON SE4.E4_FILIAL			= CNB.CNB_FILIAL  "
				cQuery += "AND SE4.E4_CODIGO		= CNB.CNB_CONDPG "
				cQuery += "AND SE4.D_E_L_E_T_	<> '*' "
				cQuery += "	WHERE "
				cQuery += "		CNB.CNB_SITUAC IN ('A','G','P') 		AND " 
				cQuery += "		CNB.D_E_L_E_T_	<> '*'					AND "
				cQuery += "		SB1.D_E_L_E_T_  	<> '*'					AND "
				cQuery += "		SBM.D_E_L_E_T_  	<> '*'					AND "
				cQuery += "		CNC.CNC_CLIENT	=  '"+cod_cliente+"'	AND "
				cQuery += "		CNC.CNC_LOJACL  	= '"+cod_loja+"'		AND "
				cQuery += "		CNB.CNB_REVISA	= ( SELECT MAX( CN9.CN9_REVISA ) VERSAO "
				cQuery += "		                  	FROM " + RetFullName("CN9",cEmpTemp) + " CN9 "
				cQuery += "		                  	WHERE 	CN9.CN9_FILIAL  	= '  '	AND "
				cQuery += "		                      	  	CNB.CNB_FILIAL  	= '  '	AND "
				cQuery += "		                     	  	CNA.CNA_CONTRA  	= CN9.CN9_NUMERO 			AND "
				cQuery += "		                     	   	CN9.D_E_L_E_T_ 	<> '*' ) "
				cQuery += "	GROUP BY  SB1.B1_DESC "
				cQuery += "			,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA "
				cQuery += "			,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_ORIGEM	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VENCTO	,CNB.CNB_DATASS "
				cQuery += "			,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC 	,CNB.CNB_STATRM 	,CNB.CNB_UM     	,CNB.CNB_NOTASE 	,CNB.CNB_DIAVEN "
				cQuery += "			,CNB.CNB_IMPOST 	,CNB.CNB_INDCTR 	,CNB.CNB_TS     	,CNB.CNB_PROPOS 	,CNB.CNB_VERPRO 	,CNB.R_E_C_N_O_	,CNB_ULTRJT "
				cQuery += "			,SBM.BM_GRUPO   	,SBM.BM_PICPAD "
				cQuery += "			,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL "
				cQuery += "			,SE4.E4_FORMA "
				cQuery += "	HAVING ROUND(CNB.CNB_QUANT * CNB.CNB_VLUNIT, 2) > 0 "
				cQuery += "	ORDER BY SBM.BM_PICPAD, CNA.CNA_FILIAL, CNA.CNA_CONTRA , CNA.CNA_REVISA , CNB.CNB_ITEM ,CNB.CNB_PRODUT "

				cQuery := ChangeQuery(cQuery)

				If(Select("T_CO") <> 0)
					dbSelectArea("T_CO")
					dbCloseArea()
				EndIf

				If lAchou 
					dbUseArea( .T., 'TOPCONN',TcGenQry(,,cQuery), "T_CO" , .T., .T.)
					
					T_CO->(DbGoTop())


					While T_CO->(!Eof())

						nPos := aScan(aDados,{|x| x[1]+x[2]+x[4]+x[5] == aSigaMat[nV,1]+T_CO->CNA_FILIAL+T_CO->CNA_CONTRA +T_CO->CNA_REVISA})

						If nPos == 0
							aAdd(aDados,{aSigaMat[nV,1],T_CO->CNA_FILIAL,aSigaMat[nV,3],T_CO->CNA_CONTRA,T_CO->CNA_REVISA,T_CO->CNC_CLIENT,T_CO->CNC_LOJACL,{}})
							nPos := Len(aDados)
						EndIf

						aAdd(aDados[nPos,8],{T_CO->CNB_ITEM											,;
							AllTrim(T_CO->CNB_PRODUT)+"-"+AllTrim(T_CO->B1_DESC)	,;
							Transform(T_CO->CNB_QUANT,"@E 999,999,999.99")  		,;
							Transform(T_CO->CNB_VLUNIT,"@E 999,999,999.99")		,;
							DTOC(StoD(T_CO->CNB_VENCTO))							,;
							DTOC(StoD(T_CO->CNB_DTURJT))							,;
							T_CO->CNB_CONDPG											,;
							T_CO->CNB_SITUAC											,;
							DTOC(StoD(T_CO->CNB_DATASS))							,;
							T_CO->CNB_IMPOST											,;
							T_CO->CNB_INDCTR											,;
							T_CO->CNB_TS												,;
							T_CO->CNB_STATRM											,;
							T_CO->CNB_UM												,;
							T_CO->CNB_NOTASE											,;
							T_CO->CNB_PROPOS											,;
							T_CO->CNB_VERPRO											,;
							T_CO->CNB_ORIGEM											,;
							T_CO->BM_GRUPO											,;
							T_CO->BM_PICPAD											,;
							T_CO->R_E_C_N_O_											,;
							T_CO->CNB_ULTRJT											,;
							substr(T_CO->CNB_DTURJT,5,2)							,;
							alltrim(str(T_CO->CNB_DIAVEN))							,;
							(Transform((T_CO->CNB_QUANT * T_CO->CNB_VLUNIT),"@E 999,999,999.99")),;
							IIf(Empty(T_CO->E4_FORMA),"BOL",T_CO->E4_FORMA)		,;
							""})

						T_CO->(dbSkip())
					EndDo
					T_CO->(dbCloseArea())
				EndIf 

				EndIF
			Next nV 
			Else
				cEmpTemp   := aSigaMat[nV,1]
				cFilTemp   := aSigaMat[nV,2]

			
				cQuery := "	SELECT	 SB1.B1_DESC "
				cQuery += "			,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA" 
				cQuery += "			,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_ORIGEM	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VENCTO	,CNB.CNB_DATASS "
				cQuery += "			,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC 	,CNB.CNB_STATRM 	,CNB.CNB_UM     	,CNB.CNB_NOTASE 	,CNB.CNB_DIAVEN "
				cQuery += "			,CNB.CNB_IMPOST 	,CNB.CNB_INDCTR 	,CNB.CNB_TS     	,CNB.CNB_PROPOS 	,CNB.CNB_VERPRO 	,CNB.R_E_C_N_O_	,CNB_ULTRJT "
				cQuery += "			,SBM.BM_GRUPO   	,SBM.BM_PICPAD "
				cQuery += "			,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL "
				cQuery += "			,SE4.E4_FORMA "
				cQuery += "	FROM "+ RetFullName("CNA",cEmpTemp)+" CNA "
				cQuery += "		INNER JOIN " + RetFullName("CNB",cEmpTemp)+ " CNB "
				cQuery += "		ON	CNB.CNB_FILIAL		= CNA.CNA_FILIAL "
				cQuery += "			AND CNB.CNB_CONTRA	= CNA.CNA_CONTRA "
				cQuery += "			AND CNB.CNB_REVISA	= CNA.CNA_REVISA "
				cQuery += "			AND CNB.D_E_L_E_T_ = ' ' "
				cQuery += "		INNER JOIN " + RetFullName("CNC",cEmpTemp) + " CNC "
				cQuery += "		ON	CNC.CNC_FILIAL		= CNA.CNA_FILIAL "
				cQuery += "			AND CNC.CNC_NUMERO	= CNA.CNA_CONTRA "
				cQuery += "			AND CNC.D_E_L_E_T_ = ' ' "
				cQuery += "		INNER JOIN " + RetFullName("SB1",cEmpTemp) + " SB1 "
				cQuery += "		ON SB1.B1_FILIAL			= CNB.CNB_FILIAL "
				cQuery += "			AND SB1.B1_COD		= CNB.CNB_PRODUT "
				cQuery += "			AND SB1.D_E_L_E_T_ = ' ' "
				cQuery += "		INNER JOIN " + RetFullName("SBM",cEmpTemp) + " SBM "
				cQuery += "			ON SBM.BM_FILIAL		= SB1.B1_FILIAL "
				cQuery += "			AND SBM.BM_GRUPO		= SB1.B1_GRUPO "
				cQuery += "			AND SBM.D_E_L_E_T_	= ' ' "
				cQuery += "		JOIN " + RetFullName("SE4",cEmpTemp) + " SE4 "
				cQuery += "		ON SE4.E4_FILIAL			= CNB.CNB_FILIAL "
				cQuery += "			AND SE4.E4_CODIGO		= CNB.CNB_CONDPG "
				cQuery += "			AND SE4.D_E_L_E_T_	= ' ' "
				cQuery += "	WHERE "
				cQuery += "		CNB.CNB_SITUAC IN ('A','P') 		AND "
				cQuery += "		CNB.D_E_L_E_T_	   = ' '					AND "
				cQuery += "		SB1.D_E_L_E_T_  	= ' '					AND "
				cQuery += "		SBM.BM_GRUPO	IN (" +cTipGrp+ ")   AND "
				cQuery += "		SBM.D_E_L_E_T_  	= ' '					AND "
				cQuery += "		CNC.CNC_CLIENT	=  '"+cod_cliente+"'	AND "
				cQuery += "		CNC.CNC_LOJACL =  '"+cod_loja+"'		AND "
				cQuery += "		CNB.CNB_REVISA	= ( SELECT MAX( CN9.CN9_REVISA ) VERSAO "
				cQuery += "		                  	FROM " + RetFullName("CN9",cEmpTemp) + " CN9 "
				cQuery += "		                    WHERE 	CN9.CN9_FILIAL  	= '  '	AND "
				cQuery += "		                      	  	CNB.CNB_FILIAL  	= '  '	AND "
				cQuery += "		                     	  	CNA.CNA_CONTRA  	= CN9.CN9_NUMERO 			AND "
				cQuery += "		                     	  	CN9.D_E_L_E_T_ 	= ' ' ) "
				cQuery += "	GROUP BY  SB1.B1_DESC "
				cQuery += "			,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA "
				cQuery += "			,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_ORIGEM	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VENCTO	,CNB.CNB_DATASS "
				cQuery += "			,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC 	,CNB.CNB_STATRM 	,CNB.CNB_UM     	,CNB.CNB_NOTASE 	,CNB.CNB_DIAVEN "
				cQuery += "			,CNB.CNB_IMPOST 	,CNB.CNB_INDCTR 	,CNB.CNB_TS     	,CNB.CNB_PROPOS 	,CNB.CNB_VERPRO 	,CNB.R_E_C_N_O_	,CNB_ULTRJT "
				cQuery += "			,SBM.BM_GRUPO   	,SBM.BM_PICPAD "
				cQuery += "			,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL "
				cQuery += "			,SE4.E4_FORMA "
				cQuery += "	HAVING ROUND(CNB.CNB_QUANT * CNB.CNB_VLUNIT, 2) > 0 "
				cQuery += "	ORDER BY SBM.BM_PICPAD, CNA.CNA_FILIAL, CNA.CNA_CONTRA , CNA.CNA_REVISA , CNB.CNB_ITEM ,CNB.CNB_PRODUT "
				
				cQuery := ChangeQuery(cQuery)

				If(Select("T_CO") <> 0)
					dbSelectArea("T_CO")
					dbCloseArea()
				EndIf

				
				dbUseArea( .T., 'TOPCONN',TcGenQry(,,cQuery), "T_CO" , .T., .T.)
				
				T_CO->(DbGoTop())


				While T_CO->(!Eof())

					nPos := aScan(aDados,{|x| x[1]+x[2]+x[4]+x[5] == aSigaMat[nV,1]+T_CO->CNA_FILIAL+T_CO->CNA_CONTRA +T_CO->CNA_REVISA})

					If nPos == 0
						aAdd(aDados,{aSigaMat[nV,1],T_CO->CNA_FILIAL,aSigaMat[nV,3],T_CO->CNA_CONTRA,T_CO->CNA_REVISA,T_CO->CNC_CLIENT,T_CO->CNC_LOJACL,{}})
						nPos := Len(aDados)
					EndIf

					aAdd(aDados[nPos,8],{T_CO->CNB_ITEM											,;
						AllTrim(T_CO->CNB_PRODUT)+"-"+AllTrim(T_CO->B1_DESC)	,;
						Transform(T_CO->CNB_QUANT,"@E 999,999,999.99")  		,;
						Transform(T_CO->CNB_VLUNIT,"@E 999,999,999.99")		,;
						DTOC(StoD(T_CO->CNB_VENCTO))							,;
						DTOC(StoD(T_CO->CNB_DTURJT))							,;
						T_CO->CNB_CONDPG											,;
						T_CO->CNB_SITUAC											,;
						DTOC(StoD(T_CO->CNB_DATASS))							,;
						T_CO->CNB_IMPOST											,;
						T_CO->CNB_INDCTR											,;
						T_CO->CNB_TS												,;
						T_CO->CNB_STATRM											,;
						T_CO->CNB_UM												,;
						T_CO->CNB_NOTASE											,;
						T_CO->CNB_PROPOS											,;
						T_CO->CNB_VERPRO											,;
						T_CO->CNB_ORIGEM											,;
						T_CO->BM_GRUPO											,;
						T_CO->BM_PICPAD											,;
						T_CO->R_E_C_N_O_											,;
						T_CO->CNB_ULTRJT											,;
						substr(T_CO->CNB_DTURJT,5,2)							,;
						alltrim(str(T_CO->CNB_DIAVEN))							,;
						(Transform((T_CO->CNB_QUANT * T_CO->CNB_VLUNIT),"@E 999,999,999.99")),;
						IIf(Empty(T_CO->E4_FORMA),"BOL",T_CO->E4_FORMA)		,;
						""})

					T_CO->(dbSkip())

				EndDo

				T_CO->(dbCloseArea())

			EndIf

			If nPos == 0
				aAdd(aDados,{"","","","","","","",{}})
				aAdd(aDados[1,8],{"",""	,"0","0","  /  /  ","  /  /  ","","","  /  /  ","","","","","","",	"","","","","",	0,"","","","","",""})
			EndIf

			For nx := 1 To Len(aDados)

				oCabecalho:=wsclassnew("listacontratos")

				oCabecalho:ci_empresa	   		:= aDados[nx,1]
				oCabecalho:ci_filial	   			:= aDados[nx,2]
				oCabecalho:ci_unidade 	   		:= aDados[nx,3]
				oCabecalho:ci_numero	   			:= aDados[nx,4]
				oCabecalho:ci_versao	   			:= aDados[nx,5]
				oCabecalho:ci_cliente 	   		:= aDados[nx,6]
				oCabecalho:ci_loja		   		:= aDados[nx,7]
				oCabecalho:ai_itens_contrato	:= {}

				For nv := 1 To Len(aDados[nx,8])
					oItens:=wsclassnew("itens_contrato")
					oItens:ci_item	 	   		:= aDados[nx,8,nv,01]  //T_CO->CNB_ITEM
					oItens:ci_produto	   			:= aDados[nx,8,nv,02]  //AllTrim(T_CO->CNB_PRODUT)+"-"+AllTrim(T_CO->B1_DESC)
					oItens:ci_quantidade   		:= aDados[nx,8,nv,03]  //Transform(T_CO->CNB_QUANT,"@E 999,999,999.99")
					oItens:ci_valorunitario   	:= aDados[nx,8,nv,04]  //Transform(T_CO->CNB_VLUNIT,"@E 999,999,999.99")
					oItens:ci_vencimento	 		:= aDados[nx,8,nv,05]  //DTOC(StoD(T_CO->CNB_VENCTO))
					oItens:ci_dtareajuste	   	:= aDados[nx,8,nv,06]  //DTOC(StoD(T_CO->CNB_DTURJT))
					oItens:ci_condicaopagamento	:= aDados[nx,8,nv,07]  //T_CO->CNB_CONDPG
					oItens:ci_sitauacao			:= aDados[nx,8,nv,08]  //T_CO->CNB_SITUAC
					oItens:ci_dataassinatura		:= aDados[nx,8,nv,09]  //DTOC(StoD(T_CO->CNB_DATASS))
					oItens:ci_imposto	   			:= aDados[nx,8,nv,10]  //T_CO->CNB_IMPOST
					oItens:ci_indice	 			:= aDados[nx,8,nv,11]  //T_CO->CNB_INDCTR
					oItens:ci_tes	   				:= aDados[nx,8,nv,12]  //T_CO->CNB_TS
					oItens:ci_startrm	 			:= aDados[nx,8,nv,13]  //T_CO->CNB_STATRM
					oItens:ci_unidademedida	   	:= aDados[nx,8,nv,14]  //T_CO->CNB_UM
					oItens:ci_notasep	 			:= aDados[nx,8,nv,15]  //T_CO->CNB_NOTASE
					oItens:ci_proposta	   		:= aDados[nx,8,nv,16]  //T_CO->CNB_PROPOS
					oItens:ci_versaoproposta		:= aDados[nx,8,nv,17]  //T_CO->CNB_VERPRO
					oItens:ci_origem	   			:= aDados[nx,8,nv,18]  //T_CO->CNB_ORIGEM
					oItens:ci_grupo	 			:= aDados[nx,8,nv,19]  //T_CO->BM_GRUPO
					oItens:ci_linha	   			:= aDados[nx,8,nv,20]  //T_CO->BM_PICPAD
					oItens:ni_recno	 			:= aDados[nx,8,nv,21]  //T_CO->R_E_C_N_O_
					oItens:ci_reajuste	   		:= aDados[nx,8,nv,22]  //T_CO->CNB_ULTRJT
					oItens:ci_mesreaj	 			:= aDados[nx,8,nv,23]  //substr(T_CO->CNB_DTURJT,5,2)
					oItens:ci_diavencto	   		:= aDados[nx,8,nv,24]  //T_CO->CNB_DIAVEN
					oItens:ci_valtotal	 		:= aDados[nx,8,nv,25]  //T_CO->CNB_QUANT * T_CO->CNB_VLUNIT
					oItens:ci_formaPgto	 		:= aDados[nx,8,nv,26]  //T_CO->E4_FORMA
					oItens:ci_numCartao	 		:= aDados[nx,8,nv,27]  //Campo relacionado ao numero do cartão

					aAdd(oCabecalho:ai_itens_contrato,oItens)
				Next nv

				aAdd(::ai_contratos,oCabecalho)

			Next nx
	else	
			
			U_xCONOUT("AUTOATENDIMENTO","CONSULTA_CONTRATOS","REQUISICAO","")

		For nX := 1 To Len(aTipGrp)
			cTipGrp += "'"+aTipGrp[nx]+"',"
		Next
		cTipGrp := SubStr(cTipGrp,2,Len(cTipGrp)-3)

		If cDesativa == "S"  //Desativa a consulta de contratos: S=Sim,N=Nao
			nPos := 0
		ElseIf n_portal == 1

		//For nV := 1 To Len(aSigaMat)
			cEmpTemp   := aSigaMat[nV,1]
			cFilTemp   := aSigaMat[nV,2]

			BeginSQL Alias "T_CO"
		SELECT	 SB1.B1_DESC
				,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA
				,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_ORIGEM	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VENCTO	,CNB.CNB_DATASS
				,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC 	,CNB.CNB_STATRM 	,CNB.CNB_UM     	,CNB.CNB_NOTASE 	,CNB.CNB_DIAVEN
				,CNB.CNB_IMPOST 	,CNB.CNB_INDCTR 	,CNB.CNB_TS     	,CNB.CNB_PROPOS 	,CNB.CNB_VERPRO 	,CNB.R_E_C_N_O_	,CNB_ULTRJT
				,SBM.BM_GRUPO   	,SBM.BM_PICPAD
				,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL
				,SE4.E4_FORMA
		FROM %table:CNA% CNA
			INNER JOIN %table:CNB% CNB
			ON	CNB.CNB_FILIAL		= %exp:xFilial('CNB')%
				AND CNB.CNB_CONTRA	= CNA.CNA_CONTRA
				AND CNB.CNB_REVISA	= CNA.CNA_REVISA
				AND CNB.D_E_L_E_T_	<> '*'
			INNER JOIN %table:CNC% CNC
			ON	CNC.CNC_FILIAL		= %exp:xFilial('CNC')%
				AND CNC.CNC_NUMERO	= CNA.CNA_CONTRA
				AND CNC.D_E_L_E_T_	<> '*'
			INNER JOIN %table:SB1% SB1
			ON SB1.B1_FILIAL			= %exp:xFilial('SB1')%
				AND SB1.B1_COD		= CNB.CNB_PRODUT
				AND SB1.D_E_L_E_T_	<> '*'
			INNER JOIN %table:SBM% SBM
			ON SB1.B1_FILIAL			= %exp:xFilial('SBM')%
				AND SBM.BM_FILIAL		= ''
				AND SBM.BM_GRUPO		= SB1.B1_GRUPO
				AND SBM.D_E_L_E_T_	<> '*'
			INNER JOIN %table:SE4% SE4
			ON SE4.E4_FILIAL			= %exp:xFilial('SE4')%
				AND SE4.E4_CODIGO		= CNB.CNB_CONDPG
				AND SE4.D_E_L_E_T_	<> '*'
		WHERE
			CNB.CNB_SITUAC IN ('A','G','P') 		AND
			CNB.D_E_L_E_T_	<> '*'					AND
			SB1.D_E_L_E_T_  	<> '*'					AND
			SBM.D_E_L_E_T_  	<> '*'					AND
			CNC.CNC_CLIENT	=  %exp:c_cliente%	AND
			CNC.CNC_LOJACL  	=  %exp:c_loja%		AND
			CNB.CNB_REVISA	= ( SELECT MAX( CN9.CN9_REVISA ) VERSAO
								FROM %table:CN9% CN9
								WHERE 	CN9.CN9_FILIAL  	= %exp:xFilial('CN9')%	AND
										CNB.CNB_FILIAL  	= %exp:xFilial('CNB')%	AND
										CNA.CNA_CONTRA  	= CN9.CN9_NUMERO 			AND
										CN9.D_E_L_E_T_ 	<> '*' )
		GROUP BY  SB1.B1_DESC
				,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA
				,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_ORIGEM	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VENCTO	,CNB.CNB_DATASS
				,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC 	,CNB.CNB_STATRM 	,CNB.CNB_UM     	,CNB.CNB_NOTASE 	,CNB.CNB_DIAVEN
				,CNB.CNB_IMPOST 	,CNB.CNB_INDCTR 	,CNB.CNB_TS     	,CNB.CNB_PROPOS 	,CNB.CNB_VERPRO 	,CNB.R_E_C_N_O_	,CNB_ULTRJT
				,SBM.BM_GRUPO   	,SBM.BM_PICPAD
				,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL
				,SE4.E4_FORMA
		HAVING ROUND(CNB.CNB_QUANT * CNB.CNB_VLUNIT, 2) > 0
		ORDER BY SBM.BM_PICPAD, CNA.CNA_FILIAL, CNA.CNA_CONTRA , CNA.CNA_REVISA , CNB.CNB_ITEM ,CNB.CNB_PRODUT

			EndSql
			T_CO->(DbGoTop())


			While T_CO->(!Eof())

				nPos := aScan(aDados,{|x| x[1]+x[2]+x[4]+x[5] == aSigaMat[nV,1]+T_CO->CNA_FILIAL+T_CO->CNA_CONTRA +T_CO->CNA_REVISA})

				If nPos == 0
					aAdd(aDados,{aSigaMat[nV,1],T_CO->CNA_FILIAL,aSigaMat[nV,3],T_CO->CNA_CONTRA,T_CO->CNA_REVISA,T_CO->CNC_CLIENT,T_CO->CNC_LOJACL,{}})
					nPos := Len(aDados)
				EndIf

				aAdd(aDados[nPos,8],{T_CO->CNB_ITEM											,;
					AllTrim(T_CO->CNB_PRODUT)+"-"+AllTrim(T_CO->B1_DESC)	,;
					Transform(T_CO->CNB_QUANT,"@E 999,999,999.99")  		,;
					Transform(T_CO->CNB_VLUNIT,"@E 999,999,999.99")		,;
					DTOC(StoD(T_CO->CNB_VENCTO))							,;
					DTOC(StoD(T_CO->CNB_DTURJT))							,;
					T_CO->CNB_CONDPG											,;
					T_CO->CNB_SITUAC											,;
					DTOC(StoD(T_CO->CNB_DATASS))							,;
					T_CO->CNB_IMPOST											,;
					T_CO->CNB_INDCTR											,;
					T_CO->CNB_TS												,;
					T_CO->CNB_STATRM											,;
					T_CO->CNB_UM												,;
					T_CO->CNB_NOTASE											,;
					T_CO->CNB_PROPOS											,;
					T_CO->CNB_VERPRO											,;
					T_CO->CNB_ORIGEM											,;
					T_CO->BM_GRUPO											,;
					T_CO->BM_PICPAD											,;
					T_CO->R_E_C_N_O_											,;
					T_CO->CNB_ULTRJT											,;
					substr(T_CO->CNB_DTURJT,5,2)							,;
					alltrim(str(T_CO->CNB_DIAVEN))							,;
					(Transform((T_CO->CNB_QUANT * T_CO->CNB_VLUNIT),"@E 999,999,999.99")),;
					IIf(Empty(T_CO->E4_FORMA),"BOL",T_CO->E4_FORMA)		,;
					""})

				T_CO->(dbSkip())

			EndDo

			T_CO->(dbCloseArea())

		//Next
		Else
			cEmpTemp   := aSigaMat[nV,1]
			cFilTemp   := aSigaMat[nV,2]

			BeginSQL Alias "T_CO"
		SELECT	 SB1.B1_DESC
				,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA
				,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_ORIGEM	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VENCTO	,CNB.CNB_DATASS
				,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC 	,CNB.CNB_STATRM 	,CNB.CNB_UM     	,CNB.CNB_NOTASE 	,CNB.CNB_DIAVEN
				,CNB.CNB_IMPOST 	,CNB.CNB_INDCTR 	,CNB.CNB_TS     	,CNB.CNB_PROPOS 	,CNB.CNB_VERPRO 	,CNB.R_E_C_N_O_	,CNB_ULTRJT
				,SBM.BM_GRUPO   	,SBM.BM_PICPAD
				,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL
				,SE4.E4_FORMA
		FROM %table:CNA% CNA
			INNER JOIN %table:CNB% CNB
			ON	CNB.CNB_FILIAL		= %exp:xFilial('CNB')%
				AND CNB.CNB_CONTRA	= CNA.CNA_CONTRA
				AND CNB.CNB_REVISA	= CNA.CNA_REVISA
				AND CNB.D_E_L_E_T_ = ' '
			INNER JOIN %table:CNC% CNC
			ON	CNC.CNC_FILIAL		= %exp:xFilial('CNC')%
				AND CNC.CNC_NUMERO	= CNA.CNA_CONTRA
				AND CNC.D_E_L_E_T_ = ' '
			INNER JOIN %table:SB1% SB1
			ON SB1.B1_FILIAL			= %exp:xFilial('SB1')%
				AND SB1.B1_COD		= CNB.CNB_PRODUT
				AND SB1.D_E_L_E_T_ = ' '
			INNER JOIN %table:SBM% SBM
			ON SB1.B1_FILIAL			= %exp:xFilial('SBM')%
				AND SBM.BM_FILIAL		= ''
				AND SBM.BM_GRUPO		= SB1.B1_GRUPO
				AND SBM.D_E_L_E_T_	= ' '
			JOIN %table:SE4% SE4
			ON SE4.E4_FILIAL			= %exp:xFilial('SE4')%
				AND SE4.E4_CODIGO		= CNB.CNB_CONDPG
		//			AND SE4.E4_FORMA		<> %Exp:'CC'%
				AND SE4.D_E_L_E_T_	= ' '
		WHERE
			CNB.CNB_SITUAC IN ('A','P') 		AND
			CNB.D_E_L_E_T_	   = ' '					AND
			SB1.D_E_L_E_T_  	= ' '					AND
			SBM.BM_GRUPO	IN (%exp:cTipGrp%)   AND
			SBM.D_E_L_E_T_  	= ' '					AND
			CNC.CNC_CLIENT	=  %exp:c_cliente%	AND
			CNC.CNC_LOJACL =  %exp:c_loja%		AND
			CNB.CNB_REVISA	= ( SELECT MAX( CN9.CN9_REVISA ) VERSAO
								FROM %table:CN9% CN9
								WHERE 	CN9.CN9_FILIAL  	= %exp:xFilial('CN9')%	AND
										CNB.CNB_FILIAL  	= %exp:xFilial('CNB')%	AND
										CNA.CNA_CONTRA  	= CN9.CN9_NUMERO 			AND
										CN9.D_E_L_E_T_ 	= ' ' )
		GROUP BY  SB1.B1_DESC
				,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA
				,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_ORIGEM	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VENCTO	,CNB.CNB_DATASS
				,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC 	,CNB.CNB_STATRM 	,CNB.CNB_UM     	,CNB.CNB_NOTASE 	,CNB.CNB_DIAVEN
				,CNB.CNB_IMPOST 	,CNB.CNB_INDCTR 	,CNB.CNB_TS     	,CNB.CNB_PROPOS 	,CNB.CNB_VERPRO 	,CNB.R_E_C_N_O_	,CNB_ULTRJT
				,SBM.BM_GRUPO   	,SBM.BM_PICPAD
				,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL
				,SE4.E4_FORMA
		HAVING ROUND(CNB.CNB_QUANT * CNB.CNB_VLUNIT, 2) > 0
		ORDER BY SBM.BM_PICPAD, CNA.CNA_FILIAL, CNA.CNA_CONTRA , CNA.CNA_REVISA , CNB.CNB_ITEM ,CNB.CNB_PRODUT

			EndSql
			T_CO->(DbGoTop())


			While T_CO->(!Eof())

				nPos := aScan(aDados,{|x| x[1]+x[2]+x[4]+x[5] == aSigaMat[nV,1]+T_CO->CNA_FILIAL+T_CO->CNA_CONTRA +T_CO->CNA_REVISA})

				If nPos == 0
					aAdd(aDados,{aSigaMat[nV,1],T_CO->CNA_FILIAL,aSigaMat[nV,3],T_CO->CNA_CONTRA,T_CO->CNA_REVISA,T_CO->CNC_CLIENT,T_CO->CNC_LOJACL,{}})
					nPos := Len(aDados)
				EndIf

				aAdd(aDados[nPos,8],{T_CO->CNB_ITEM											,;
					AllTrim(T_CO->CNB_PRODUT)+"-"+AllTrim(T_CO->B1_DESC)	,;
					Transform(T_CO->CNB_QUANT,"@E 999,999,999.99")  		,;
					Transform(T_CO->CNB_VLUNIT,"@E 999,999,999.99")		,;
					DTOC(StoD(T_CO->CNB_VENCTO))							,;
					DTOC(StoD(T_CO->CNB_DTURJT))							,;
					T_CO->CNB_CONDPG											,;
					T_CO->CNB_SITUAC											,;
					DTOC(StoD(T_CO->CNB_DATASS))							,;
					T_CO->CNB_IMPOST											,;
					T_CO->CNB_INDCTR											,;
					T_CO->CNB_TS												,;
					T_CO->CNB_STATRM											,;
					T_CO->CNB_UM												,;
					T_CO->CNB_NOTASE											,;
					T_CO->CNB_PROPOS											,;
					T_CO->CNB_VERPRO											,;
					T_CO->CNB_ORIGEM											,;
					T_CO->BM_GRUPO											,;
					T_CO->BM_PICPAD											,;
					T_CO->R_E_C_N_O_											,;
					T_CO->CNB_ULTRJT											,;
					substr(T_CO->CNB_DTURJT,5,2)							,;
					alltrim(str(T_CO->CNB_DIAVEN))							,;
					(Transform((T_CO->CNB_QUANT * T_CO->CNB_VLUNIT),"@E 999,999,999.99")),;
					IIf(Empty(T_CO->E4_FORMA),"BOL",T_CO->E4_FORMA)		,;
					""})

				T_CO->(dbSkip())

			EndDo

			T_CO->(dbCloseArea())

		EndIf

		If nPos == 0
			aAdd(aDados,{"","","","","","","",{}})
			aAdd(aDados[1,8],{"",""	,"0","0","  /  /  ","  /  /  ","","","  /  /  ","","","","","","",	"","","","","",	0,"","","","","",""})
		EndIf

		For nx := 1 To Len(aDados)

			oCabecalho:=wsclassnew("listacontratos")

			oCabecalho:ci_empresa	   		:= aDados[nx,1]
			oCabecalho:ci_filial	   			:= aDados[nx,2]
			oCabecalho:ci_unidade 	   		:= aDados[nx,3]
			oCabecalho:ci_numero	   			:= aDados[nx,4]
			oCabecalho:ci_versao	   			:= aDados[nx,5]
			oCabecalho:ci_cliente 	   		:= aDados[nx,6]
			oCabecalho:ci_loja		   		:= aDados[nx,7]
			oCabecalho:ai_itens_contrato	:= {}

			For nv := 1 To Len(aDados[nx,8])
				oItens:=wsclassnew("itens_contrato")
				oItens:ci_item	 	   		:= aDados[nx,8,nv,01]  //T_CO->CNB_ITEM
				oItens:ci_produto	   			:= aDados[nx,8,nv,02]  //AllTrim(T_CO->CNB_PRODUT)+"-"+AllTrim(T_CO->B1_DESC)
				oItens:ci_quantidade   		:= aDados[nx,8,nv,03]  //Transform(T_CO->CNB_QUANT,"@E 999,999,999.99")
				oItens:ci_valorunitario   	:= aDados[nx,8,nv,04]  //Transform(T_CO->CNB_VLUNIT,"@E 999,999,999.99")
				oItens:ci_vencimento	 		:= aDados[nx,8,nv,05]  //DTOC(StoD(T_CO->CNB_VENCTO))
				oItens:ci_dtareajuste	   	:= aDados[nx,8,nv,06]  //DTOC(StoD(T_CO->CNB_DTURJT))
				oItens:ci_condicaopagamento	:= aDados[nx,8,nv,07]  //T_CO->CNB_CONDPG
				oItens:ci_sitauacao			:= aDados[nx,8,nv,08]  //T_CO->CNB_SITUAC
				oItens:ci_dataassinatura		:= aDados[nx,8,nv,09]  //DTOC(StoD(T_CO->CNB_DATASS))
				oItens:ci_imposto	   			:= aDados[nx,8,nv,10]  //T_CO->CNB_IMPOST
				oItens:ci_indice	 			:= aDados[nx,8,nv,11]  //T_CO->CNB_INDCTR
				oItens:ci_tes	   				:= aDados[nx,8,nv,12]  //T_CO->CNB_TS
				oItens:ci_startrm	 			:= aDados[nx,8,nv,13]  //T_CO->CNB_STATRM
				oItens:ci_unidademedida	   	:= aDados[nx,8,nv,14]  //T_CO->CNB_UM
				oItens:ci_notasep	 			:= aDados[nx,8,nv,15]  //T_CO->CNB_NOTASE
				oItens:ci_proposta	   		:= aDados[nx,8,nv,16]  //T_CO->CNB_PROPOS
				oItens:ci_versaoproposta		:= aDados[nx,8,nv,17]  //T_CO->CNB_VERPRO
				oItens:ci_origem	   			:= aDados[nx,8,nv,18]  //T_CO->CNB_ORIGEM
				oItens:ci_grupo	 			:= aDados[nx,8,nv,19]  //T_CO->BM_GRUPO
				oItens:ci_linha	   			:= aDados[nx,8,nv,20]  //T_CO->BM_PICPAD
				oItens:ni_recno	 			:= aDados[nx,8,nv,21]  //T_CO->R_E_C_N_O_
				oItens:ci_reajuste	   		:= aDados[nx,8,nv,22]  //T_CO->CNB_ULTRJT
				oItens:ci_mesreaj	 			:= aDados[nx,8,nv,23]  //substr(T_CO->CNB_DTURJT,5,2)
				oItens:ci_diavencto	   		:= aDados[nx,8,nv,24]  //T_CO->CNB_DIAVEN
				oItens:ci_valtotal	 		:= aDados[nx,8,nv,25]  //T_CO->CNB_QUANT * T_CO->CNB_VLUNIT
				oItens:ci_formaPgto	 		:= aDados[nx,8,nv,26]  //T_CO->E4_FORMA
				oItens:ci_numCartao	 		:= aDados[nx,8,nv,27]  //Campo relacionado ao numero do cartão

				aAdd(oCabecalho:ai_itens_contrato,oItens)
			Next nv

			aAdd(::ai_contratos,oCabecalho)

		Next nx

	EndIF

	U_xCONOUT("AUTOATENDIMENTO","CONSULTA_CONTRATOS","RETORNO","")

Return .T.

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³CONSULTA_CONTRATOS_DATA	³ Autor ³ Erich Buttner   ³ Data ³ 27/01/14 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI		                                                    ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONSULTA_CONTRATOS_DATA WsReceive c_cliente, c_loja, c_datade,c_dataate WsSend aq_contratos WsService AUTOATENDIMENTO

	Local aSigaMat   	:= WS_Sw_SigaMat()
	Local aDados     	:= {}
	Local cQuery     	:= ""
	Local cEmpTemp   	:= ""
	Local cFilTemp   	:= ""
	Local nPos	     	:= 0
	Local nV         	:= 1
	Local nX         	:= 0
	Local oCabecalho
	Local oItens
	Local cVend 		:= ""
	Local cDVend 		:= ""
	Local aObs 		:= {}
	Local cNumeroCna	:= ""

	U_xCONOUT("AUTOATENDIMENTO","CONSULTA_CONTRATOS_DATA","REQUISICAO","")

	If !Empty(c_datade) .And. !Empty(c_dataate)

		//	For nV := 1 To Len(aSigaMat)
		cEmpTemp   := aSigaMat[nV,1]
		cFilTemp   := aSigaMat[nV,2]

		BeginSql Alias "T_CN"
				SELECT DISTINCT SB1.B1_DESC
						,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA	,CNA.CNA_NUMERO
						,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VENCTO	,CNB.CNB_DATASS
						,DECODE(CNB.CNB_DTURJT, '', CNB.CNB_DATASS, CNB.CNB_DTURJT) CNB_MESREAJ
						,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC	,CNB.CNB_STATRM	,CNB.CNB_UM 	,CNB.CNB_NOTASE
						,CNB.CNB_DIAVEN	,CNB.CNB_IMPOST ,CNB.CNB_INDCTR ,CNB.CNB_TS     ,CNB.CNB_PROPOS ,CNB.CNB_VERPRO
						,CNB.CNB_ITMPRO	,CNB.CNB_ORIGEM	,CNB.R_E_C_N_O_	CNBRECNO			,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL
						,DECODE(CNB.CNB_ULTRJT, '', '', (SUBSTR(CNB.CNB_ULTRJT, 1, 2) || ',' || SUBSTR(CNB.CNB_ULTRJT, 3, 1))) CNB_ULTRJT
						,SBM.BM_GRUPO		,SBM.BM_PICPAD
				 		,PHB.PHB_DATA   DTALT	,UTL_RAW.CAST_TO_VARCHAR2(DBMS_LOB.SUBSTR(PHB.PHB_OBSERV, 4000,1))  OBSERV	,PHB.PHB_MOTIVO MOTIVO
				FROM %table:CNA% CNA
				INNER JOIN %table:CNB% CNB
				ON	CNB.CNB_FILIAL		= %exp:xFilial('CNB')%
					AND CNB.CNB_CONTRA	= CNA.CNA_CONTRA
					AND CNB.CNB_REVISA	= CNA.CNA_REVISA
					AND CNB.D_E_L_E_T_	<> '*'
				INNER JOIN %table:CNC% CNC
				ON	CNC.CNC_FILIAL		= %exp:xFilial('CNC')%
					AND CNC.CNC_NUMERO	= CNA.CNA_CONTRA
					AND CNC.D_E_L_E_T_	<> '*'
				INNER JOIN %table:SB1%  SB1
				ON SB1.B1_FILIAL = %exp:xFilial('SB1')%
					AND CNA.CNA_FILIAL 	= %exp:xFilial('CNA')%
					AND CNB.CNB_PRODUT 	= SB1.B1_COD
				INNER JOIN %table:SBM% SBM
				ON SBM.BM_FILIAL 	= %exp:xFilial('SBM')%
					AND SBM.BM_GRUPO 		= SB1.B1_GRUPO
					AND SBM.BM_XSERIE 	= '01'
				INNER JOIN %table:PHB%  PHB
				ON PHB.PHB_FILIAL 		= %exp:xFilial('PHB')%
					AND CNA.CNA_FILIAL 	= %exp:xFilial('CNA')%
					AND PHB.PHB_CONTRA 	= CNA.CNA_CONTRA
					AND PHB.PHB_REVISA 	= CNA.CNA_REVISA
				WHERE CNB.CNB_SITUAC	IN (	'A','G','P'	)
					AND PHB.PHB_DATA  BETWEEN  %exp:DtoS(c_datade)% AND  %exp:DtoS(c_dataate)%
					AND (CNB.CNB_QUANT * CNB.CNB_VLUNIT) > 0
					AND CNC.CNC_CLIENT 	=  %exp:c_cliente%
					AND CNC.CNC_LOJACL	=  %exp:c_loja%
					AND CNA.D_E_L_E_T_	= ' '
					AND SB1.D_E_L_E_T_	= ' '
					AND SBM.D_E_L_E_T_	= ' '
					AND PHB.D_E_L_E_T_	= ' '

					ORDER BY PHB.PHB_DATA, CNA_REVISA,SBM.BM_PICPAD,CNA_FILIAL,CNA_CONTRA,CNB_ITEM,CNB_PRODUT,CNC.CNC_CLIENT,CNC.CNC_LOJACL

		EndSql

		dbGoTop()

		While T_CN->(!Eof())
			cNumeroCna	:= T_CN->CNA_NUMERO
			cNumProp := ""
			If SC6->(FieldPos("C6_PROPOST")) > 0 // Tratamento para ambientes que nao possuem este campo criado
				cNumProp := T_CN->CNB_PROPOS
			EndIf

			aVends	:= {}
			aVends	:= u_ChkVend(cNumProp,T_CN->CNC_CLIENT,T_CN->CNC_LOJACL)

			If !Empty(aVends)
				cVend	:= aVends[1][1]
				cDVend := aVends[1][2]
			Endif
			nPos ++
			aAdd(aDados,{aSigaMat[nV,1],T_CN->CNA_FILIAL,aSigaMat[nV,3],T_CN->CNA_CONTRA,T_CN->CNA_REVISA,T_CN->CNC_CLIENT,T_CN->CNC_LOJACL,{}})


			cObserv 	:= AllTrim(T_CN->OBSERV)//aObs[2]//PBI->PBI_OBSERV
			cMotiv 	:= AllTrim(T_CN->MOTIVO)//aObs[1] //PBI->PBI_MOTIVO

			aAdd(aDados[nPos,8],{	T_CN->CNB_ITEM											,;
				AllTrim(T_CN->CNB_PRODUT)+"-"+AllTrim(T_CN->B1_DESC)	,;
				Transform(T_CN->CNB_QUANT,"@E 999,999,999.99")  		,;
				Transform(T_CN->CNB_VLUNIT,"@E 999,999,999.99")		,;
				DTOC(StoD(T_CN->CNB_VENCTO))							,;
				DTOC(StoD(T_CN->CNB_DTURJT))							,;
				T_CN->CNB_CONDPG											,;
				T_CN->CNB_SITUAC											,;
				DTOC(StoD(T_CN->CNB_DATASS))							,;
				T_CN->CNB_IMPOST											,;
				T_CN->CNB_INDCTR											,;
				T_CN->CNB_TS												,;
				T_CN->CNB_STATRM											,;
				T_CN->CNB_UM												,;
				T_CN->CNB_NOTASE											,;
				T_CN->CNB_PROPOS											,;
				T_CN->CNB_VERPRO											,;
				T_CN->CNB_ORIGEM											,;
				T_CN->BM_GRUPO											,;
				T_CN->BM_PICPAD											,;
				T_CN->CNBRECNO											,;
				T_CN->CNB_ULTRJT											,;
				substr(T_CN->CNB_DTURJT,5,2)							,;
				alltrim(str(T_CN->CNB_DIAVEN))							,;
				(Transform((T_CN->CNB_QUANT * T_CN->CNB_VLUNIT),"@E 999,999,999.99"))		,;
				If(!Empty(AllTrim(cObserv)),AllTrim(cObserv)+" - ","")+AllTrim(cMotiv)	,;
					If(Empty(AllTrim(cVend)),Space(6),cVend)				,;
						If(Empty(AllTrim(cDVend)),Space(6),cDVend)			,;
							T_CN->CNB_ITMPRO											,;
							T_CN->CNB_PRODUT											,;
							cEmpTemp													,;
							cFilTemp													})


						T_CN->(dbSkip())
					EndDo
					T_CN->(dbCloseArea())
				Else
					cEmpTemp   := aSigaMat[nV,1]
					cFilTemp   := aSigaMat[nV,2]
					BeginSql Alias "T_CN"
			SELECT DISTINCT SB1.B1_DESC
					,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA	,CNA.CNA_NUMERO
					,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT
					,CNB.CNB_VENCTO	,CNB.CNB_DATASS
					,DECODE(CNB.CNB_DTURJT, '', CNB.CNB_DATASS, CNB.CNB_DTURJT) CNB_MESREAJ
					,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC	,CNB.CNB_STATRM
					,CNB.CNB_UM		,CNB.CNB_NOTASE	,CNB.CNB_DIAVEN	,CNB.CNB_IMPOST
					,CNB.CNB_INDCTR	,CNB.CNB_TS		,CNB.CNB_PROPOS	,CNB.CNB_VERPRO
					,DECODE(CNB.CNB_ULTRJT, '', '', (SUBSTR(CNB.CNB_ULTRJT, 1, 2) || ',' || SUBSTR(CNB.CNB_ULTRJT, 3, 1))) CNB_ULTRJT
					,CNB.CNB_ORIGEM	,CNB.R_E_C_N_O_  CNBRECNO,CNB.CNB_ITMPRO
					,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL
					,SBM.BM_GRUPO		,SBM.BM_PICPAD
					,PHB.PHB_DATA		,UTL_RAW.CAST_TO_VARCHAR2(DBMS_LOB.SUBSTR(PHB.PHB_OBSERV, 4000,1)) OBSERV	,PHB.PHB_MOTIVO
			FROM %table:CNA% CNA
			INNER JOIN %table:CNB% CNB
			ON	CNB.CNB_FILIAL		= %exp:xFilial('CNB')%
				AND CNB.CNB_CONTRA	= CNA.CNA_CONTRA
				AND CNB.CNB_REVISA	= CNA.CNA_REVISA
				AND CNB.D_E_L_E_T_	<> '*'
			INNER JOIN %table:CNC% CNC
			ON	CNC.CNC_FILIAL		= %exp:xFilial('CNC')%
				AND CNC.CNC_NUMERO	= CNA.CNA_CONTRA
				AND CNC.D_E_L_E_T_	<> '*'
			INNER JOIN %table:SB1% SB1
			ON SB1.B1_FILIAL		= %exp:xFilial('SB1')%
				AND CNA.CNA_FILIAL	= ''
				AND CNB.CNB_PRODUT	= SB1.B1_COD
			INNER JOIN %table:SBM% SBM
			ON SB1.B1_FILIAL		= %exp:xFilial('SB1')%
				AND SBM.BM_FILIAL	= %exp:xFilial('SBM')%
				AND SBM.BM_GRUPO	= SB1.B1_GRUPO
				AND SBM.BM_XSERIE	= '01'
			INNER JOIN %table:PHB% PHB
			ON PHB.PHB_FILIAL		= %exp:xFilial('PHB')%
				AND PHB.PHB_CONTRA	= CNA.CNA_CONTRA
				AND PHB.PHB_REVISA	= CNA.CNA_REVISA
			INNER JOIN (SELECT MAX(CN9.CN9_REVISA) VERSAO
						FROM %table:CN9% CN9
						INNER JOIN %table:CNA% CNA
						ON CNA.CNA_FILIAL	= %exp:xFilial('CNA')%
							AND CNA.CNA_CLIENT	= CN9.CN9_CLIENT
							AND CNA.CNA_CLIENT	= CN9.CN9_LOJACL
							AND CNA.CNA_CONTRA	= CN9.CN9_NUMERO
							AND CNA.D_E_L_E_T_	<> '*'
						WHERE CN9.CN9_FILIAL	= %exp:xFilial('CN9')%
							AND CN9.D_E_L_E_T_	<> '*'
							) SCB1
							ON  (SCB1.VERSAO = CNA.CNA_REVISA)
			WHERE CNB.CNB_SITUAC IN (	'A','G','P'	)
				AND CNA.D_E_L_E_T_	= ' '
				AND SB1.D_E_L_E_T_	= ' '
				AND SBM.D_E_L_E_T_	= ' '
				AND PHB.D_E_L_E_T_	= ' '
				AND (CNB.CNB_QUANT * CNB.CNB_VLUNIT) > 0
				AND	CNC.CNC_CLIENT	=  %exp:c_cliente%
				AND	CNC.CNC_LOJACL	=  %exp:c_loja%
			ORDER BY CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNB.CNB_VERPRO	,CNB.CNB_ITEM	,CNB.CNB_PRODUT	,CNC.CNC_CLIENT ,CNC.CNC_LOJACL, PHB.PHB_DATA	,SBM.BM_PICPAD

					EndSql
					dbGoTop()

					While T_CN->(!Eof())
						cNumeroCna	:= T_CN->CNA_NUMERO
						cNumProp := ""
						If SC6->(FieldPos("C6_PROPOST")) > 0 // Tratamento para ambientes que nao possuem este campo criado
							cNumProp := T_CN->CNB_PROPOS
						EndIf
						aVends	:= {}
						aVends	:= u_ChkVend(cNumProp,T_CN->CNC_CLIENT,T_CN->CNC_LOJACL)
						If !Empty(aVends)
							cVend	:= aVends[1][1]
							cDVend := aVends[1][2]
						Endif
						nPos ++

						aAdd(aDados,{aSigaMat[nV,1],T_CN->CNA_FILIAL,aSigaMat[nV,3],T_CN->CNA_CONTRA,T_CN->CNA_REVISA,T_CN->CNC_CLIENT,T_CN->CNC_LOJACL,{}})

						cObserv := T_CN->OBSERV
						cMotiv := T_CN->PHB_MOTIVO

						aAdd(aDados[nPos,8],{T_CN->CNB_ITEM											,;
							AllTrim(T_CN->CNB_PRODUT)+"-"+AllTrim(T_CN->B1_DESC)	,;
							Transform(T_CN->CNB_QUANT,"@E 999,999,999.99")  		,;
							Transform(T_CN->CNB_VLUNIT,"@E 999,999,999.99")		,;
							DTOC(StoD(T_CN->CNB_VENCTO))							,;
							DTOC(StoD(T_CN->CNB_DTURJT))							,;
							T_CN->CNB_CONDPG											,;
							T_CN->CNB_SITUAC											,;
							DTOC(StoD(T_CN->CNB_DATASS))							,;
							T_CN->CNB_IMPOST											,;
							T_CN->CNB_INDCTR											,;
							T_CN->CNB_TS												,;
							T_CN->CNB_STATRM											,;
							T_CN->CNB_UM												,;
							T_CN->CNB_NOTASE											,;
							T_CN->CNB_PROPOS											,;
							T_CN->CNB_VERPRO											,;
							T_CN->CNB_ORIGEM											,;
							T_CN->BM_GRUPO											,;
							T_CN->BM_PICPAD											,;
							T_CN->CNBRECNO											,;
							T_CN->CNB_ULTRJT											,;
							substr(T_CN->CNB_DTURJT,5,2)							,;
							alltrim(str(T_CN->CNB_DIAVEN))							,;
							(Transform((T_CN->CNB_QUANT * T_CN->CNB_VLUNIT),"@E 999,999,999.99")),;
							If(!Empty(AllTrim(cObserv)),AllTrim(cObserv)+" - ","")+AllTrim(cMotiv),;
								If(Empty(AllTrim(cVend)),Space(6),cVend)				,;
									If(Empty(AllTrim(cDVend)),Space(6),cDVend)			,;
										T_CN->CNB_ITMPRO											,;
										T_CN->CNB_PRODUT											,;
										cEmpTemp													,;
										cFilTemp													})

									T_CN->(dbSkip())
								EndDo

								T_CN->(dbCloseArea())
							EndIf
							//	Next nV

							If nPos == 0
								aAdd(aDados,{"","","","","","","",{}})
								aAdd(aDados[1,8],{"",""	,"0","0","  /  /  ","  /  /  ","","","  /  /  ","","","","","","",	"","","","","",	0,"","","","","","","","","","",""})
							EndIf

							For nx := 1 To Len(aDados)

								oCabecalhoData:=wsclassnew("listacontratosdata")

								oCabecalhoData:cq_empresa	   		:= aDados[nx,1]
								oCabecalhoData:cq_filial	   			:= aDados[nx,2]
								oCabecalhoData:cq_unidade 	   		:= aDados[nx,3]
								oCabecalhoData:cq_numero	   			:= aDados[nx,4]
								oCabecalhoData:cq_versao	   			:= aDados[nx,5]
								oCabecalhoData:cq_cliente 	   		:= aDados[nx,6]
								oCabecalhoData:cq_loja		   		:= aDados[nx,7]
								oCabecalhoData:aq_itens_contrato	:= {}
								oCabecalhoData:aq_itens_contant  	:= {}

								For nv := 1 To Len(aDados[nx,8])

									oItensData:=wsclassnew("itens_contratodata")
									oItensData:cq_item	 	   			:= aDados[nx,8,nv,01] //T_CN->CNB_ITEM
									oItensData:cq_produto	   			:= aDados[nx,8,nv,02]  //AllTrim(T_CN->CNB_PRODUT)+"-"+AllTrim(T_CN->B1_DESC)
									oItensData:cq_quantidade   			:= aDados[nx,8,nv,03]  //Transform(T_CN->CNB_QUANT,"@E 999,999,999.99")
									oItensData:cq_valorunitario   		:= aDados[nx,8,nv,04]  //Transform(T_CN->CNB_VLUNIT,"@E 999,999,999.99")
									oItensData:cq_vencimento	 			:= aDados[nx,8,nv,05]  //DTOC(StoD(T_CN->CNB_VENCTO))
									oItensData:cq_dtareajuste	   		:= aDados[nx,8,nv,06]  //DTOC(StoD(T_CN->CNB_DTURJT))
									oItensData:cq_condicaopagamento		:= aDados[nx,8,nv,07]  //T_CN->CNB_CONDPG
									oItensData:cq_sitauacao				:= aDados[nx,8,nv,08]  //T_CN->CNB_SITUAC
									oItensData:cq_dataassinatura		:= aDados[nx,8,nv,09]  //DTOC(StoD(T_CN->CNB_DATASS))
									oItensData:cq_imposto	   			:= aDados[nx,8,nv,10]  //T_CN->CNB_IMPOST
									oItensData:cq_indice	 				:= aDados[nx,8,nv,11]  //T_CN->CNB_INDCTR
									oItensData:cq_tes	   					:= aDados[nx,8,nv,12]  //T_CN->CNB_TS
									oItensData:cq_startrm	 			:= aDados[nx,8,nv,13]  //T_CN->CNB_STATRM
									oItensData:cq_unidademedida	   		:= aDados[nx,8,nv,14]  //T_CN->CNB_UM
									oItensData:cq_notasep	 			:= aDados[nx,8,nv,15]  //T_CN->CNB_NOTASE
									oItensData:cq_proposta	   			:= aDados[nx,8,nv,16]  //T_CN->CNB_PROPOS
									oItensData:cq_versaoproposta		:= aDados[nx,8,nv,17]  //T_CN->CNB_VERPRO
									oItensData:cq_origem	   				:= aDados[nx,8,nv,18]  //T_CN->CNB_ORIGEM
									oItensData:cq_grupo	 				:= aDados[nx,8,nv,19]  //T_CN->BM_GRUPO
									oItensData:cq_linha	   				:= aDados[nx,8,nv,20]  //T_CN->BM_PICPAD
									oItensData:nq_recno	 				:= aDados[nx,8,nv,21]  //T_CN->CNBRECNO
									oItensData:cq_reajuste	   			:= aDados[nx,8,nv,22]  //T_CN->CNBULTREAJ
									oItensData:cq_mesreaj	 			:= aDados[nx,8,nv,23]  //T_CN->CNB_MESREAJ
									oItensData:cq_diavencto	   			:= aDados[nx,8,nv,24]  //T_CN->CNB_VENCTO
									oItensData:cq_valtotal	 			:= aDados[nx,8,nv,25]  //(T_CN->CNB_QUANT * T_CN->CNB_VLUNIT)
									oItensData:cq_obsmotivo	 			:= aDados[nx,8,nv,26]  //T_CN->PHB_MOTIVO
									oItensData:cq_codvend	   			:= aDados[nx,8,nv,27]  //T_CN->BM_PICPAD
									oItensData:cq_nmvend		 			:= aDados[nx,8,nv,28]  //T_CN->CNBRECNO


									aAdd(oCabecalhoData:aq_itens_contrato,oItensData)


									cEmpAnt := aDados[nx,8,nv,31]

									DbSelectArea("CNB")
									CNB->(DbSetOrder(1))

									cCodVer := Val(aDados[nx,5])-1
									cCodVer := If(StrZero(cCodVer,6) == '000000', '000001',StrZero(cCodVer,6))

									If CNB->(MsSeek(Xfilial("CNB")+aDados[nx,4]+cCodVer+cNumeroCna+aDados[nx,8,nv,01]))

										oItensDataant:=wsclassnew("itens_contrdataant")
										oItensDataant:cq_itemant	 	   			:= CNB->CNB_ITEM //T_CN->CNB_ITEM
										oItensDataant:cq_produtoant	   			:= AllTrim(CNB->CNB_PRODUT)+"-"+GetAdvFVal("SB1","B1_DESC",xFilial("SB1")+CNB->CNB_PRODUT,1,"")
										oItensDataant:cq_quantidadeant   		:= Transform(CNB->CNB_QUANT,"@E 999,999,999.99")
										oItensDataant:cq_valorunitarioant   	:= Transform(CNB->CNB_VLUNIT,"@E 999,999,999.99")
										oItensDataant:cq_vencimentoant 			:= DTOC(CNB->CNB_VENCTO)
										oItensDataant:cq_dtareajusteant	   		:= DTOC(CNB->CNB_DTURJT)
										oItensDataant:cq_condicaopagamentoant	:= CNB->CNB_CONDPG
										oItensDataant:cq_sitauacaoant			:= CNB->CNB_SITUAC
										oItensDataant:cq_dataassinaturaant		:= DTOC(CNB->CNB_DATASS)
										oItensDataant:cq_impostoant	   			:= CNB->CNB_IMPOST
										oItensDataant:cq_indiceant	 			:= CNB->CNB_INDCTR
										oItensDataant:cq_tesant	   				:= CNB->CNB_TS
										oItensDataant:cq_startrmant	 			:= CNB->CNB_STATRM
										oItensDataant:cq_unidademedidaant	   	:= CNB->CNB_UM
										oItensDataant:cq_notasepant	 			:= CNB->CNB_NOTASE
										oItensDataant:cq_propostaant	   		:= CNB->CNB_PROPOS
										oItensDataant:cq_versaopropostaant		:= CNB->CNB_VERPRO
										oItensDataant:cq_origemant	   			:= CNB->CNB_ORIGEM
										oItensDataant:cq_grupoant	 			:= GetAdvFVal("SBM","BM_GRUPO",xFilial("SBM")+SD6->D6_PRODUTO,1,"")
										oItensDataant:cq_linhaant	   			:= GetAdvFVal("SBM","BM_PICPAD",xFilial("SBM")+SD6->D6_PRODUTO,1,"")
										oItensDataant:nq_recnoant	 			:= Recno()
										oItensDataant:cq_reajusteant	   		:= CNB->CNB_ULTRJT
										oItensDataant:cq_mesreajant	 			:= substr(DtoS(CNB->CNB_DTURJT),5,2)
										oItensDataant:cq_diavenctoant	   		:= alltrim(str(CNB->CNB_DIAVEN))
										oItensDataant:cq_valtotalant	 		:= (Transform((CNB->CNB_QUANT * CNB->CNB_VLUNIT),"@E 999,999,999.99"))



										cObserv 	:= ""//aObs[2]//PBI->PBI_OBSERV
										cMotiv 	:= ""//aObs[1] //PBI->PBI_MOTIVO

										oItensDataant:cq_obsmotivoant	 		:= If(!Empty(AllTrim(cObserv)),AllTrim(cObserv)+" - ","")+AllTrim(cMotiv)  //T_CN->BM_GRUPO

										aVends	:= {}
										aVends	:= u_ChkVend(CNB->CNB_PROPOS	,c_cliente		,c_loja)
										If !Empty(aVends)
											cVend	:= aVends[1][1]
											cDVend := aVends[1][2]
										Endif

										oItensDataant:cq_codvendant	   		:= cVend  //T_CN->BM_PICPAD
										oItensDataant:cq_nmvendant		 	:= cDVend  //T_CN->CNBRECNO
									Else

										oItensDataant:=wsclassnew("itens_contrdataant")
										oItensDataant:cq_itemant	 	   		:= aDados[nx,8,nv,01]
										oItensDataant:cq_produtoant	   			:= aDados[nx,8,nv,02]
										oItensDataant:cq_quantidadeant   		:= aDados[nx,8,nv,03]
										oItensDataant:cq_valorunitarioant   	:= aDados[nx,8,nv,04]
										oItensDataant:cq_vencimentoant 			:= aDados[nx,8,nv,05]
										oItensDataant:cq_dtareajusteant	   		:= aDados[nx,8,nv,06]
										oItensDataant:cq_condicaopagamentoant	:= aDados[nx,8,nv,07]
										oItensDataant:cq_sitauacaoant			:= aDados[nx,8,nv,08]
										oItensDataant:cq_dataassinaturaant		:= aDados[nx,8,nv,09]
										oItensDataant:cq_impostoant	   			:= aDados[nx,8,nv,10]
										oItensDataant:cq_indiceant	 			:= aDados[nx,8,nv,11]
										oItensDataant:cq_tesant	   				:= aDados[nx,8,nv,12]
										oItensDataant:cq_startrmant	 			:= aDados[nx,8,nv,13]
										oItensDataant:cq_unidademedidaant	   	:= aDados[nx,8,nv,14]
										oItensDataant:cq_notasepant	 			:= aDados[nx,8,nv,15]
										oItensDataant:cq_propostaant	   		:= aDados[nx,8,nv,16]
										oItensDataant:cq_versaopropostaant		:= aDados[nx,8,nv,17]
										oItensDataant:cq_origemant	   			:= aDados[nx,8,nv,18]
										oItensDataant:cq_grupoant	 			:= aDados[nx,8,nv,19]
										oItensDataant:cq_linhaant	   			:= aDados[nx,8,nv,20]
										oItensDataant:nq_recnoant	 			:= aDados[nx,8,nv,21]
										oItensDataant:cq_reajusteant	   		:= aDados[nx,8,nv,22]
										oItensDataant:cq_mesreajant	 			:= aDados[nx,8,nv,23]
										oItensDataant:cq_diavenctoant	   		:= aDados[nx,8,nv,24]
										oItensDataant:cq_valtotalant	 		:= aDados[nx,8,nv,25]
										oItensDataant:cq_obsmotivoant	 		:= aDados[nx,8,nv,26]
										oItensDataant:cq_codvendant	   			:= aDados[nx,8,nv,27]
										oItensDataant:cq_nmvendant				:= aDados[nx,8,nv,28]

									EndIf

									aAdd(oCabecalhoData:aq_itens_contant,oItensDataant)

								Next nv

								aAdd(::aq_contratos,oCabecalhoData)

							Next nx

							U_xCONOUT("AUTOATENDIMENTO","CONSULTA_CONTRATOS_DATA","RETORNO","")

							Return .T.

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³CONS_CONT_COLIG       	³ Autor ³ Karen Sousa     ³ Data ³ 04/02/14 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI		                                                    ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONS_CONT_COLIG WsReceive c_cliente, c_loja WsSend atc_cColig WsService AUTOATENDIMENTO

	Local aSigaMat   := WS_Sw_SigaMat()
	Local aDados     := {}
	Local cQuery     := ""
	Local cEmpTemp   := ""
	Local cFilTemp   := ""
	Local nPos	     := 0
	Local nV         := 0
	Local nX         := 0
	Local nk         := 0
	Local oCabecalho
	Local oItens

	U_xCONOUT("AUTOATENDIMENTO","CONS_CONT_COLIG","REQUISICAO","")

	For nV := 1 To Len(aSigaMat)

		cEmpTemp   := aSigaMat[nV,1]
		cFilTemp   := aSigaMat[nV,2]

		BeginSql Alias 'T_PHG'
	SELECT DISTINCT
			SB1.B1_DESC
			,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA	,CNA.CNA_NUMERO
			,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VLTOT	,CNB.CNB_VENCTO
			,CNB.CNB_DATASS	,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC 	,CNB.CNB_STATRM 	,CNB.CNB_ORIGEM
			,CNB.CNB_UM     	,CNB.CNB_NOTASE 	,CNB.CNB_DIAVEN	,CNB.CNB_IMPOST 	,CNB.CNB_INDCTR
			,CNB.CNB_TS     	,CNB.CNB_PROPOS 	,CNB.CNB_VERPRO 	,CNB.R_E_C_N_O_	,CNB_ULTRJT
			,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL
			,SBM.BM_GRUPO   	,SBM.BM_PICPAD
			,PHG.PHG_FILIAL	,PHG.PHG_PROPOS 	,PHG.PHG_ITMPRO ,PHG.PHG_PRODUT
			,PHG.PHG_CLIENT	,PHG.PHG_LOJA		,PHG.PHG_PERRAT ,PHG.PHG_ITEM
			,SA1.A1_CGC
	FROM %table:CNA% CNA
		INNER JOIN %table:CNB% CNB
		ON	CNB.CNB_FILIAL		= %exp:xFilial('CNB')%
			AND CNB.CNB_CONTRA	= CNA.CNA_CONTRA
			AND CNB.CNB_REVISA	= CNA.CNA_REVISA
			AND CNB.D_E_L_E_T_	<> '*'
		INNER JOIN %table:CNC% CNC
		ON	CNC.CNC_FILIAL		= %exp:xFilial('CNC')%
			AND CNC.CNC_NUMERO	= CNA.CNA_CONTRA
			AND CNC.D_E_L_E_T_	<> '*'
		INNER JOIN %table:SA1% SA1
		ON SA1.A1_FILIAL			= %exp:xFilial('SA1')%
			AND SA1.A1_COD		= CNC.CNC_CLIENT
			AND SA1.A1_LOJA		= CNC.CNC_LOJACL
			AND SA1.D_E_L_E_T_ 	<> '*'
		INNER JOIN %table:SB1% SB1
		ON SB1.B1_FILIAL			= %exp:xFilial('SB1')%
			AND SB1.B1_COD		= CNB.CNB_PRODUT
			AND SB1.D_E_L_E_T_	<> '*'
		INNER JOIN %table:SBM% SBM
		ON SB1.B1_FILIAL			= %exp:xFilial('SB1')%
			AND SBM.BM_FILIAL		= %exp:xFilial('SBM')%
			AND SBM.BM_GRUPO		= SB1.B1_GRUPO
			AND SBM.D_E_L_E_T_	<> '*'
	INNER JOIN  %table:PHG% PHG
	ON PHG.PHG_FILIAL			= %exp:xFilial('PHG')%
		AND PHG.PHG_CONTRA 	= CNA.CNA_CONTRA
		AND PHG.PHG_REVISA	= CNA.CNA_REVISA
		AND PHG.PHG_CLIENT	= CNC.CNC_CLIENT
		AND PHG.PHG_LOJA		= CNC.CNC_LOJACL
		AND PHG.PHG_PROPOS 	= CNB.CNB_PROPOS
	  	AND PHG.PHG_PRODUT	= CNB.CNB_PRODUT
		AND PHG.PHG_ITEM 		= CNB.CNB_ITEM
	WHERE
		CNA.CNA_FILIAL 			= %exp:xFilial('CNA')%
		AND CNB.CNB_SITUAC 			IN ('A','G','P')
		AND CNC.CNC_CLIENT		=  %exp:c_cliente%
		AND CNC.CNC_LOJACL  		=  %exp:c_loja%
		AND CNA.D_E_L_E_T_		<> '*'
		AND CNB.D_E_L_E_T_		<> '*'
		AND SB1.D_E_L_E_T_  		<> '*'
		AND SBM.D_E_L_E_T_  		<> '*'
		AND PHG.D_E_L_E_T_		<> '*'
		AND CNB.CNB_REVISA		= ( SELECT MAX( CN9.CN9_REVISA ) VERSAO
		                  			FROM %table:CN9% CN9
		                  			WHERE 	CN9.CN9_FILIAL  	= %exp:xFilial('CN9')%
		                      	  		AND CNB.CNB_FILIAL 	= %exp:xFilial('CNB')%
		                     	  		AND CNA.CNA_CONTRA 	= CN9.CN9_NUMERO
		                     	   		AND CN9.D_E_L_E_T_ 	<> '*' )
	GROUP BY
			SB1.B1_DESC
			,CNA.CNA_FILIAL	,CNA.CNA_CONTRA	,CNA.CNA_REVISA	,CNA.CNA_NUMERO
			,CNB.CNB_ITEM		,CNB.CNB_PRODUT	,CNB.CNB_QUANT	,CNB.CNB_VLUNIT	,CNB.CNB_VLTOT	,CNB.CNB_VENCTO
			,CNB.CNB_DATASS	,CNB.CNB_DTURJT	,CNB.CNB_CONDPG	,CNB.CNB_SITUAC 	,CNB.CNB_STATRM	,CNB.CNB_ORIGEM
			,CNB.CNB_UM     	,CNB.CNB_NOTASE 	,CNB.CNB_DIAVEN	,CNB.CNB_IMPOST 	,CNB.CNB_INDCTR
			,CNB.CNB_TS     	,CNB.CNB_PROPOS 	,CNB.CNB_VERPRO 	,CNB.R_E_C_N_O_	,CNB_ULTRJT
			,CNC.CNC_CLIENT 	,CNC.CNC_LOJACL
			,SBM.BM_GRUPO   	,SBM.BM_PICPAD
			,PHG.PHG_FILIAL	,PHG.PHG_PROPOS 	,PHG.PHG_ITMPRO ,PHG.PHG_PRODUT
			,PHG.PHG_CLIENT	,PHG.PHG_LOJA		,PHG.PHG_PERRAT ,PHG.PHG_ITEM
			,SA1.A1_CGC
	HAVING ROUND(CNB.CNB_QUANT * CNB.CNB_VLUNIT, 2) > 0
	ORDER BY SBM.BM_PICPAD, CNA.CNA_FILIAL, CNA.CNA_CONTRA , CNA.CNA_REVISA , CNB.CNB_ITEM ,CNB.CNB_PRODUT

		EndSql
		dbGoTop()

		While T_PHG->(!Eof())

			nPos ++

			aAdd(aDados,{aSigaMat[nV,1],T_PHG->CNA_FILIAL	,aSigaMat[nV,3]	,T_PHG->CNA_CONTRA	,T_PHG->CNA_REVISA	,T_PHG->CNC_CLIENT	,T_PHG->CNC_LOJACL,{}})

			aAdd(aDados[nPos,8],  {	T_PHG->CNB_ITEM												,;
				AllTrim(T_PHG->CNB_PRODUT)+"-"+AllTrim(T_PHG->B1_DESC)	,;
				Transform(T_PHG->CNB_QUANT,"@E 999,999,999.99")  		,;
				Transform(T_PHG->CNB_VLUNIT,"@E 999,999,999.99")			,;
				DTOC(StoD(T_PHG->CNB_VENCTO))								,;
				DTOC(StoD(T_PHG->CNB_DTURJT))								,;
				T_PHG->CNB_CONDPG												,;
				T_PHG->CNB_SITUAC												,;
				DTOC(StoD(T_PHG->CNB_DATASS))								,;
				T_PHG->CNB_IMPOST												,;
				T_PHG->CNB_INDCTR												,;
				T_PHG->CNB_TS													,;
				T_PHG->CNB_STATRM												,;
				T_PHG->CNB_UM													,;
				T_PHG->CNB_NOTASE												,;
				T_PHG->CNB_PROPOS												,;
				T_PHG->CNB_VERPRO												,;
				T_PHG->CNB_ORIGEM												,;
				T_PHG->BM_GRUPO												,;
				T_PHG->BM_PICPAD												,;
				T_PHG->R_E_C_N_O_												,;
				T_PHG->CNB_ULTRJT												,;
				substr(T_PHG->CNB_DTURJT,5,2)								,;
				alltrim(str(T_PHG->CNB_DIAVEN))								,;
				(Transform((T_PHG->CNB_QUANT * T_PHG->CNB_VLUNIT),"@E 999,999,999.99")),;
				{}																})

			aAdd(aDados[nPos,8,len(adados[npos,8]),26],{ 	T_PHG->PHG_FILIAL					,;
				T_PHG->PHG_PROPOS					,;
				T_PHG->PHG_ITEM					,;
				T_PHG->A1_CGC						,;
				(Transform((T_PHG->CNB_QUANT * T_PHG->CNB_VLUNIT),"@E 999,999,999.99")),;
				T_PHG->PHG_PRODUT					,;
				T_PHG->PHG_CLIENT					,;
				T_PHG->PHG_LOJA					,;
				"//"								,;
				"//"								,;
				Transform(T_PHG->PHG_PERRAT,"@E 999.99"),;
				POSICIONE("CNC", 5, xFilial("CNC")+T_PHG->CNA_CONTRA+T_PHG->CNA_REVISA+"01", "CNC_CLIENT"),;
				POSICIONE("CNC", 5, xFilial("CNC")+T_PHG->CNA_CONTRA+T_PHG->CNA_REVISA+"01", "CNC_LOJACL"),;
				T_PHG->CNB_SITUAC 				} )
			T_PHG->(dbSkip())

		EndDo

		T_PHG->(dbCloseArea())

	Next

	If nPos == 0
		aAdd(aDados,{"","","","","","","",{}})
		aAdd(aDados[1,8],{"",""	,"0","0","  /  /  ","  /  /  ","","","  /  /  ","","","","","","",	"","","","","",	0,"","","","",{}})
		aAdd(aDados[1,8,1,26],{"",""	,"","","","","","","","","0","","",""})
	EndIf

	For nx := 1 To Len(aDados)

		oCabecalhoC:=wsclassnew("listacontColig")

		oCabecalhoC:ctc_empresa	   	:= aDados[nx,1]
		oCabecalhoC:ctc_filial	   	:= aDados[nx,2]
		oCabecalhoC:ctc_unidade 	   	:= aDados[nx,3]
		oCabecalhoC:ctc_numero	   	:= aDados[nx,4]
		oCabecalhoC:ctc_versao	   	:= aDados[nx,5]
		oCabecalhoC:ctc_cliente 	   	:= aDados[nx,6]
		oCabecalhoC:ctc_loja		   	:= aDados[nx,7]
		oCabecalhoC:atc_itens_contrato:= {}

		aAdd(::atc_cColig,oCabecalhoC)

		For nv := 1 To Len(aDados[nx,8])
			oItensC:=wsclassnew("itens_contColig")
			oItensC:ctc_item	 	   		:= aDados[nx,8,nv,01] //T_PHG->CNB_ITEM
			oItensC:ctc_produto	   		:= aDados[nx,8,nv,02]  //AllTrim(T_PHG->CNB_PRODUT)+"-"+AllTrim(T_PHG->B1_DESC)
			oItensC:ctc_quantidade   	:= aDados[nx,8,nv,03]  //Transform(T_PHG->CNB_QUANT,"@E 999,999,999.99")
			oItensC:ctc_valorunitario   := aDados[nx,8,nv,04]  //Transform(T_PHG->CNB_VLUNIT,"@E 999,999,999.99")
			oItensC:ctc_vencimento	 	:= aDados[nx,8,nv,05]  //DTOC(StoD(T_PHG->CNB_VENCTO))
			oItensC:ctc_dtareajuste	   	:= aDados[nx,8,nv,06]  //DTOC(StoD(T_PHG->CNB_DTURJT))
			oItensC:ctc_condicaopagamento	:= aDados[nx,8,nv,07]  //T_PHG->CNB_CONDPG
			oItensC:ctc_sitauacao		:= aDados[nx,8,nv,08]  //T_PHG->CNB_SITUAC
			oItensC:ctc_dataassinatura	:= aDados[nx,8,nv,09]  //DTOC(StoD(T_PHG->CNB_DATASS))
			oItensC:ctc_imposto	   		:= aDados[nx,8,nv,10]  //T_PHG->CNB_IMPOST
			oItensC:ctc_indice	 		:= aDados[nx,8,nv,11]  //T_PHG->CNB_INDCTR
			oItensC:ctc_tes	   			:= aDados[nx,8,nv,12]  //T_PHG->CNB_TS
			oItensC:ctc_startrm	 		:= aDados[nx,8,nv,13]  //T_PHG->CNB_STATRM
			oItensC:ctc_unidademedida	:= aDados[nx,8,nv,14]  //T_PHG->CNB_UM
			oItensC:ctc_notasep	 		:= aDados[nx,8,nv,15]  //T_PHG->CNB_NOTASE
			oItensC:ctc_proposta	   		:= aDados[nx,8,nv,16]  //T_PHG->CNB_PROPOS
			oItensC:ctc_versaoproposta	:= aDados[nx,8,nv,17]  //T_PHG->CNB_VERPRO
			oItensC:ctc_origem	   		:= aDados[nx,8,nv,18]  //T_PHG->CNB_ORIGEM
			oItensC:ctc_grupo	 			:= aDados[nx,8,nv,19]  //T_PHG->BM_GRUPO
			oItensC:ctc_linha	   			:= aDados[nx,8,nv,20]  //T_PHG->BM_PICPAD
			oItensC:ntc_recno	 			:= aDados[nx,8,nv,21]  //T_PHG->R_E_C_N_O_
			oItensC:ctc_reajuste	   		:= aDados[nx,8,nv,22]  //T_PHG->CNB_ULTRJT
			oItensC:ctc_mesreaj	 		:= aDados[nx,8,nv,23]  //T_PHG->CNB_DTURJT
			oItensC:ctc_diavencto	   	:= aDados[nx,8,nv,24]  //T_PHG->CNB_DIAVEN
			oItensC:ctc_valtotal	 		:= aDados[nx,8,nv,25]  //T_PHG->CNB_QUANT * T_PHG->CNB_VLUNIT
			oItensC:atc_coligada		  	:= {}

			aAdd(oCabecalhoC:atc_itens_contrato,oItensC)

			For nk := 1 To Len(aDados[nx,8,nv,26])
				oItensC2:=wsclassnew("itens_Coligada")
				oItensC2:cti_proposta		:= aDados[nx,8,nv,26,nk,02]
				oItensC2:cti_itemprop		:= aDados[nx,8,nv,26,nk,03]
				oItensC2:cti_cgc				:= aDados[nx,8,nv,26,nk,04]
				oItensC2:cti_valor			:= aDados[nx,8,nv,26,nk,05]
				oItensC2:cti_produto			:= aDados[nx,8,nv,26,nk,06]
				oItensC2:cti_codcli			:= aDados[nx,8,nv,26,nk,07]
				oItensC2:cti_lojacli			:= aDados[nx,8,nv,26,nk,08]
				oItensC2:cti_vigde			:= aDados[nx,8,nv,26,nk,09]
				oItensC2:cti_vigate			:= aDados[nx,8,nv,26,nk,10]
				oItensC2:cti_percrat			:= aDados[nx,8,nv,26,nk,11]
				oItensC2:cti_clipri			:= aDados[nx,8,nv,26,nk,12]
				oItensC2:cti_lojapri			:= aDados[nx,8,nv,26,nk,13]
				oItensC2:cti_sitefat			:= aDados[nx,8,nv,26,nk,14]

				aAdd(oItensC:atc_coligada,oItensC2)

			Next nk


		Next nv


	Next nx

	U_xCONOUT("AUTOATENDIMENTO","CONS_CONT_COLIG","RETORNO","")

Return .T.


/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ VEREMPRESAS ³ Autor ³ Cleyton Ferreira   ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
Static Function VerEmpresas()

	Local aArea     := SM0->(GetArea()) 
	Local aEmpresas := {}

	SM0->(dbGoTop())

	While SM0->(!Eof())

		aAdd(aEmpresas,{SM0->M0_CODIGO,SM0->M0_CODFIL,SM0->M0_CGC,SM0->M0_NOMECOM})

		SM0->(dbSkip())

	EndDo

	RestArea(aArea)

Return(aEmpresas)

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ FCEMPANT    ³ Autor ³ Cleyton Ferreira   ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
Static Function FcEmpant(cEmpresa)

	cEmp:= ''

	Do Case
	Case cEmpresa = '0001'
		cEmp:= '00'
	Case cEmpresa = '0002'
		cEmp:= '20'
	Case cEmpresa = '0005'
		cEmp:= '33'
	Case cEmpresa = '0201'
		cEmp:= '19'
	Case cEmpresa = '0801'
		cEmp:= '47'
	Case cEmpresa = '0007'
		cEmp:= '30'
	EndCase

Return(cEmp)

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ FCEMPANT    ³ Autor ³ Cleyton Ferreira   ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
Static Function FATM01VPAR(cSerie,cNota,aParcelas)

	Local cParcela := ""
	Local nVlrAbat := 0
	Local nValor   := 0
	Local aArea    := SE1->(GetArea())

	aParcelas := {}

	SE1->(dbSetOrder(1)) //E1_FILIAL+E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO
	SE1->(dbSeek(xFilial('SE1')+cSerie+cNota))

	While SE1->(!Eof()) .And. SE1->(E1_FILIAL+E1_PREFIXO+E1_NUM) == xFilial('SE1')+cSerie+cNota

		If Alltrim(SE1->E1_TIPO) <> 'NF'
			SE1->(dbSkip())
			Loop
		EndIf

		nVlrAbat := SE1->(SomaAbat(E1_PREFIXO,E1_NUM,E1_PARCELA,"R",1,,E1_CLIENTE,E1_LOJA))

		If Empty(SE1->E1_PARCELA)
			lParcAnt := .T.
			cParcela := "001"
		Else
			cParcela := SE1->E1_PARCELA
		Endif

		nValor := (SE1->E1_SALDO - nVlrAbat)
		nValor := If(nValor < 0, 0, nValor)

		AADD(aParcelas,{cParcela,nValor})

		SE1->(dbSkip())
	EndDo

	SE1->(RestArea(aArea))

Return .T.

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ FCEMPANT    ³ Autor ³ Cleyton Ferreira   ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
Static Function WS_Sw_SigaMat()

	Local aEmpRet  := {}
	Local cSM0Cod  := ""
	Local cSM0Fil  := ""
	Local cNomeEmp := ""

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ Verifica empresas que entram na busca ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

	ZX5->(DBSeek(xFilial("ZX5")+"TFTS01"))
	While ZX5->(!EOF()) .AND. ZX5->ZX5_FILIAL == xFilial("ZX5") .AND. ZX5->ZX5_TABELA == "TFTS01"
		cSM0Cod := AllTrim(ZX5->ZX5_CHAVE)
		cSM0Fil := AllTrim(ZX5->ZX5_CHAVE2)
		If SM0->(DBSeek(cSM0Cod+cSM0Fil))
			cNomeEmp := AllTrim(SM0->M0_NOMECOM)
			aAdd(aEmpRet,{cSM0Cod,cSM0Fil,cNomeEmp})
		Endif
		ZX5->(DbSkip())
	EndDo

Return(aEmpRet)


/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºPrograma  ³CalcRetencºAutor  ³Adrianne Furtado    º Data ³  13/02/12   º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDesc.     ³                                                            º±±
±±º          ³                                                            º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºUso       ³ AP                                                         º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
Static Function CalcRetencao(cAliasA,cEmpTemp)

	Local aImpostos	:= {}
	Local aAreaAtu	:= GetArea(),aAreaSE1	:= SE1->(GetArea()), aAreaSFQ	:= SFQ->(GetArea())
	Local cPrefixo	:= 	Iif (Empty ((cAliasA)->F2_PREFIXO), &(SuperGetMV ("MV_2DUPREF")), (cAliasA)->F2_PREFIXO)
	Local cChaveSE1	:= "", nRecSE1, cFilSE1	:= xFilial("SE1")
	Local cQuery	:= ""
	Local nX		:= 1
	Local aTitulo	:= {}
	Local cEmpTit	:= ""
	SE1->(DbSetOrder(2))   // E1_FILIAL + E1_CLIENTE + E1_LOJA    + E1_PREFIXO + E1_NUM     + E1_PARCELA + E1_TIPO
	SFQ->(DbSetOrder(1))   // FQ_FILIAL + FQ_ENTORI  + FQ_PREFORI + FQ_NUMORI  + FQ_PARCORI + FQ_TIPOORI + FQ_CFORI + FQ_LOJAORI

	cChaveSE1	:= cFilSE1+(cAliasA)->(F2_CLIENTE+F2_LOJA+cPrefixo+F2_DUPL)
	If !SE1->(DbSeek(cChaveSE1))
		Return aImpostos
	EndIf

	While SE1->(!Eof()) .And. SE1->(E1_FILIAL+E1_CLIENTE+E1_LOJA+E1_PREFIXO+E1_NUM)==cChaveSE1
		cChaveSFQ:= xFilial("SFQ") + "SE1" + SE1->(E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO+E1_CLIENTE+E1_LOJA )

		If !SFQ->( DbSeek(cChaveSFQ) )
			SE1->(DbSkip())
			Loop
		EndIf

		nRecSE1:= SE1->(Recno())

		aImpostos:={{nRecSE1, SE1->E1_PIS, SE1->E1_COFINS, SE1->E1_CSLL}}
		AADD(aImpostos, {SE1->E1_CLIENTE, SE1->E1_LOJA, SE1->E1_PREFIXO, SE1->E1_NUM, SE1->E1_PARCELA, SE1->E1_TIPO, SM0->M0_CODIGO, SE1->E1_FILIAL, SE1->E1_DTPRORR, SE1->E1_VENCTO,SE1->E1_EMISSAO, SE1->E1_VALOR})

		While SFQ->(!Eof()) .And. SFQ->(FQ_FILIAL+FQ_ENTORI+FQ_PREFORI+FQ_NUMORI+FQ_PARCORI+FQ_TIPOORI+FQ_CFORI+FQ_LOJAORI)==cChaveSFQ
			//Vou buscar em toda a tabela os titulos que originaram a retenção de acordo com a SFQ para a impressão
			cQuery:= ""
			cQuery+= " SELECT SE1.E1_CLIENTE, SE1.E1_LOJA, SE1.E1_PREFIXO, SE1.E1_NUM, SE1.E1_PARCELA, SE1.E1_TIPO, SE1.E1_DTPRORR, SE1.E1_VENCTO, SE1.E1_EMISSAO, SE1.E1_VALOR, SE1.E1_FILIAL"

			cQuery += " FROM " + RetFullName("SE1",cEmpTemp) + " SE1 "
			cQuery+= " WHERE "
			cQuery+= " 		SE1.E1_FILIAL = '" + xFilial("SE1") + "' "
			cQuery+= " AND SE1.E1_CLIENTE = " + ValToSql(SFQ->FQ_CFDES)
			cQuery+= " AND SE1.E1_LOJA = "     + ValToSql(SFQ->FQ_LOJADES) + " AND SE1.E1_PREFIXO = " + ValToSql(SFQ->FQ_PREFDES)
			cQuery+= " AND SE1.E1_NUM = "      + ValToSql(SFQ->FQ_NUMDES) + " AND SE1.E1_PARCELA = " + ValToSql(SFQ->FQ_PARCDES)
			cQuery+= " AND TRIM(SE1.E1_TIPO) = " + AllTrim(ValToSql(SFQ->FQ_TIPODES))
			cQuery+= " AND SE1.D_E_L_E_T_ = ' '"
			cQuery+= " ORDER BY " + SqlOrder(SE1->(IndexKey()))

			DbUseArea(.T., "TOPCONN", TcGenQry(,,cQuery), "T_SE1", .T., .T.)
			T_SE1->(DbGoTop())

			While T_SE1->(!Eof())
				AADD(aImpostos, {T_SE1->E1_CLIENTE, T_SE1->E1_LOJA, T_SE1->E1_PREFIXO, T_SE1->E1_NUM, T_SE1->E1_PARCELA, T_SE1->E1_TIPO, SM0->M0_CODIGO, T_SE1->E1_FILIAL, STOD(T_SE1->E1_DTPRORR), STOD(T_SE1->E1_VENCTO),STOD(T_SE1->E1_EMISSAO), T_SE1->E1_VALOR})
				T_SE1->(DbSkip())
			EndDo

			If Select("T_SE1") > 0
				T_SE1->(DbCloseArea())
			EndIf

			SFQ->(DbSkip())
		EndDo

		SE1->(DbGoTo(nRecSE1))
		Exit
		SE1->(DbSkip())
	EndDo

	RestArea(aAreaSE1)
	RestArea(aAreaSFQ)
	RestArea(aAreaAtu)

	For nX := 1 to Len(aImpostos)

		If Len(aImpostos[nX]) > 4
			SM0->(DbSetOrder(1))
			SM0->(DbSeek(aImpostos[nX,7]))

			cEmpTit := AllTrim( aImpostos[nX,7]+aImpostos[nX,8]) + " - " + AllTrim(SM0->M0_NOME) + " / " + AllTrim(SM0->M0_FILIAL)

			dDtVenc	:= IIf(Empty(aImpostos[nX,9]), aImpostos[nX,10], aImpostos[nX,9])
			//prefixo         numero           emissao                vencimento     valor                 empresa-titulo
			AADD(aTitulo, {aImpostos[nX,3], aImpostos[nX,4],DToS(aImpostos[nX,11]),DToS(dDtVenc) ,Str(aImpostos[nX,12]),cEmpTit})
		EndIf

	Next nX

Return aTitulo

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ VEREMPRESAS ³ Autor ³ Cleyton Ferreira   ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
Static Function ObservCon(cEmpTemp,cFilTemp,cNum,cCv)

	Local cQry1	:= ""
	Local aRet	 	:= {}

	cQry1 := " SELECT PBI_FILIAL, PBI_MOTIVO, PBI_OBS
	cQry1 += " FROM "+RetFullName("PBI",cEmpTemp)+" PBI
	cQry1 += " WHERE
	cQry1 += " 		PBI_FILIAL = '"+cFilTemp+"'
	cQry1 += " 		AND
	cQry1 += " 		PBI_CONTRA = '"+cNum+"' AND PBI_VERSAO = '"+cCv+"' "
	cQry1 += " AND D_E_L_E_T_ = ' '
	cQry1 += " GROUP BY PBI_FILIAL, PBI_MOTIVO, PBI_OBS


	cQry1 := ChangeQuery(cQry1)

	dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQry1), "TPBI1" , .F., .T.)

	dbSelectArea("TPBI1")
	dbGoTop()

	aRet := {TPBI1->PBI_MOTIVO, TPBI1->PBI_OBS}

	TPBI1->(dbCloseArea())

Return aRet

WsMethod CONS_CLOUD_PENDE WsReceive NULLPARAM WsSend o_Cloud_tit WsService AUTOATENDIMENTO
	Local cQuery		:= ""
	Local olisttitulos	:= {}
	Local nX			:= 1
	Local cEmpCloud 	:= GetMV("MV_#EMPCLD",,"AA")
	Local cFilCloud 	:= GetMV("MV_#FILCLD",,"01")
	Local aEmp			:= PF4EmpMxm("C")
	Local aEmp2			:= {}
	Local cFilSX5		:= ""
	Local cTabSX5 		:= U_GetSX2Cloud(cEmpCloud,"SX5",@cFilSX5,cFilCloud)

	aEmp2 		:= {}
	U_xCONOUT("AUTOATENDIMENTO","CONS_CLOUD_PENDE","REQUISICAO","")

	For nX := 1 to Len(aEmp)
		nP := aScan(aEmp2, {|x| x[1] == aEmp[nX,1] })
		If nP == 0
			AAdd(aEmp2, aEmp[nX] )
			nP := Len(aEmp2)
		EndIf
		If SubStr(aEmp2[nP,17],1,1) == "("
			aEmp2[nP,17] := SubStr(aEmp2[nP,17],1,Len(aEmp2[nP,17])-1) + ",'"+aEmp[nX,17]+"')"
		Else
			aEmp2[nP,17] := "('"+aEmp[nX,17]+"')"
		EndIf
	Next nX



	For nX := 1 to Len(aEmp2)

		If nX > 1
			cQuery += " UNION  "
		EndIf

		cQuery += " SELECT DISTINCT "
		cQuery += " 	'" + aEmp2[nX,1] +"' AS CODEMPRESA "
		cQuery += " 	,'" + aEmp2[nX,2] +"' AS NOMEMPRESA "
		cQuery += " 	,SE1.E1_FILIAL AS UNIDADE "
		cQuery += " 	,SE1.E1_CLIENTE "
		cQuery += " 	,SE1.E1_LOJA "
		cQuery += " 	,SE1.E1_PREFIXO "
		cQuery += " 	,SE1.E1_NUM "
		cQuery += " 	,SE1.E1_PARCELA "
		cQuery += " 	,SE1.E1_TIPO "
		cQuery += " 	,SE1.E1_EMISSAO "
		cQuery += " 	,SE1.E1_VENCREA "
		cQuery += " 	,SE1.E1_VALOR "
		cQuery += " 	,SE1.E1_TXMOEDA "
		cQuery += " 	,SE1.E1_DTPRORR "
		cQuery += " FROM " + RetFullName("AON",aEmp2[nX,1]) + " AON "

		cQuery += " INNER JOIN " + RetFullName("AOQ",aEmp2[nX,1]) + " AOQ "
		cQuery += " ON AOQ_FILIAL = AON_FILIAL "
		cQuery += " AND AOQ_IDSIMU = AON_IDSIMU
		cQuery += " AND AOQ.D_E_L_E_T_ = '  ' "

		cQuery += " INNER JOIN " + RetFullName("SC6",aEmp2[nX,1]) + " SC6 "
		cQuery += " ON C6_FILIAL IN " + aEmp2[nX,17]
		cQuery += " AND C6_PROPOST = AON_CODPRO "
		cQuery += " AND C6_CLI = AON_CODCLI "
		cQuery += " AND C6_LOJA = AON_LOJCLI "
		cQuery += " AND C6_PRODUTO = AOQ_CODPRO "
		cQuery += " AND SC6.D_E_L_E_T_ = '  ' "

		cQuery += " INNER JOIN " + RetFullName("SE1",aEmp2[nX,1]) + " SE1 "
		cQuery += " ON  E1_FILIAL = C6_FILIAL "
		cQuery += " AND E1_NUM = C6_NOTA "
		cQuery += " AND E1_PREFIXO = C6_SERIE "
		cQuery += " AND E1_CLIENTE = C6_CLI "
		cQuery += " AND E1_LOJA = C6_LOJA "
		cQuery += " AND E1_TIPO = 'NF' "
		cQuery += " AND E1_SALDO    	<> 0 	 "
		cQuery += " AND E1_VENCREA  <= '" + DTOS(dDataBase)+ "' "
		cQuery += " AND E1_TIPO <> '"+MVPROVIS+"' "
		cQuery += " AND E1_TIPO <> 'PR ' "
		cQuery += " AND E1_TIPO <> 'RA ' "
		cQuery += " AND E1_TIPO NOT LIKE '%-' "
		cQuery += " AND SE1.D_E_L_E_T_ = '  ' "

		cQuery += " WHERE AON_FILIAL = '  ' "
		cQuery += " AND AON_CODCLI BETWEEN ' ' AND 'Z' "
		cQuery += " AND AON_LOJCLI BETWEEN ' ' AND 'Z' "
		cQuery += " AND AON_CODPRO <> ' ' "
		cQuery += " AND AON.D_E_L_E_T_ = '  ' "

	Next nX

	cQuery := " SELECT T1.* FROM ( " + cQuery + ") AS T1 ORDER BY T1.CODEMPRESA, T1.UNIDADE, T1.E1_EMISSAO "

	cQuery := ChangeQuery(cQuery)

	dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQuery), "T_E1" , .F., .T.)

	TcSetField("T_E1","E1_EMISSAO","D",TamSx3("E1_EMISSAO")[1],TamSx3("E1_EMISSAO")[2])
	TcSetField("T_E1","E1_VENCREA","D",TamSx3("E1_VENCREA")[1],TamSx3("E1_VENCREA")[2])
	TcSetField("T_E1","E1_VALOR","N",TamSx3("E1_VALOR")[1],TamSx3("E1_VALOR")[2])
	TcSetField("T_E1","E1_DTPRORR","D",TamSx3("E1_DTPRORR")[1],TamSx3("E1_DTPRORR")[2])

	If T_E1->(!Eof()) .And. T_E1->(!Bof())

		While T_E1->(!Eof())

			oListTitulos 					:= wsclassnew("o_CloudTitulos")
			olisttitulos:cb_cliente			:= T_E1->E1_CLIENTE
			olisttitulos:cb_loja			:= T_E1->E1_LOJA
			olisttitulos:cb_vencimento		:= dToc(T_E1->E1_VENCREA)
			olisttitulos:cb_prefixo			:= T_E1->E1_PREFIXO
			olisttitulos:cb_numero			:= T_E1->E1_NUM
			olisttitulos:cb_parcela			:= T_E1->E1_PARCELA
			olisttitulos:cb_emissao			:= dToc(T_E1->E1_EMISSAO)
			olisttitulos:cb_original_valor	:= Transform(T_E1->E1_VALOR,"@E 999,999,999.99")

			aAdd(::o_Cloud_tit,olisttitulos)

			T_E1->(DbSkip())
		EndDo

	Else

		oListTitulos 					:= wsclassnew("o_CloudTitulos")
		olisttitulos:cb_cliente			:= ""
		olisttitulos:cb_loja			:= ""
		olisttitulos:cb_vencimento		:= ""
		olisttitulos:cb_prefixo			:= ""
		olisttitulos:cb_numero			:= ""
		olisttitulos:cb_parcela			:= ""
		olisttitulos:cb_emissao			:= ""
		olisttitulos:cb_original_valor	:= ""

		aAdd(::o_Cloud_tit,olisttitulos)

	EndIf

	U_xCONOUT("AUTOATENDIMENTO","CONS_CLOUD_PENDE","RETORNO","")

Return .T.

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ CONS_CLOUD_CABEC ³ Autor ³ Hermes Ferreira  ³ Data ³ 13/01/15 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONS_CLOUD_CABEC WsReceive c_cliente, c_loja, cc_de_emissao, cc_ate_emissao, cc_de_nfiscal, cc_ate_nfiscal,cc_de_nfEletr,cc_ate_nfEletr WsSend o_Cloud_cabec WsService AUTOATENDIMENTO

	Local cQuery     	:= ""
	Local cQryComp   	:= ""
	Local cEmpTemp   	:= ""
	Local cFilTemp   	:= ""
	Local nPos	   		:= 0
	Local nV         	:= 0
	Local nX         	:= 0
	Local aDados     	:= {}
	Local oCabecalho
	Local cEmpCloud 	:= GetMV("MV_#EMPCLD",,"AA")
	Local cFilCloud 	:= GetMV("MV_#FILCLD",,"01")
	Local aEmp			:= PF4EmpMxm("C")
	Local aEmp2			:= {}
	Local cFilSX5		:= ""
	Local cTabSX5 		:= U_GetSX2Cloud(cEmpCloud,"SX5",@cFilSX5,cFilCloud)

	Public oMsgItem3

	aEmp2 		:= {}
	U_xCONOUT("AUTOATENDIMENTO","CONS_CLOUD_CABEC","REQUISICAO","")
	For nX := 1 to Len(aEmp)
		nP := aScan(aEmp2, {|x| x[1] == aEmp[nX,1] })
		If nP == 0
			AAdd(aEmp2, aEmp[nX] )
			nP := Len(aEmp2)
		EndIf
		If SubStr(aEmp2[nP,17],1,1) == "("
			aEmp2[nP,17] := SubStr(aEmp2[nP,17],1,Len(aEmp2[nP,17])-1) + ",'"+aEmp[nX,17]+"')"
		Else
			aEmp2[nP,17] := "('"+aEmp[nX,17]+"')"
		EndIf
	Next nX

	For nX := 1 to Len(aEmp2)

		If nX > 1
			cQuery += " UNION  "
		EndIf

		cQuery += " SELECT DISTINCT "
		cQuery += " 			'" + aEmp2[nX,1] +"' AS CODEMPRESA "
		cQuery += " 			,'" + aEmp2[nX,2] +"' AS NOMEMPRESA "
		cQuery += " 			, F2_FILIAL AS UNIDADE "
		cQuery += " 			, SF2.F2_CLIENTE "
		cQuery += " 			, SF2.F2_LOJA "
		cQuery += " 			, SF2.F2_DOC  "
		cQuery += " 			, SF2.F2_SERIE "
		cQuery += " 			, SF2.F2_NFELETR "
		cQuery += " 			, SF2.F2_EMISSAO "
		cQuery += " 			, SF2.F2_FILIAL "
		cQuery += "				, ( "
		cQuery += " 					SELECT "
		cQuery += " 						MIN(SE1.E1_VENCTO) "
		cQuery += " 					FROM "+ RetFullName("SE1",aEmp2[nX,1])+" SE1 "
		cQuery += " 					WHERE E1_FILIAL = F2_FILIAL "
		cQuery += " 					AND E1_CLIENTE = F2_CLIENTE "
		cQuery += " 					AND E1_LOJA = F2_LOJA "
		cQuery += " 					AND E1_PREFIXO = F2_SERIE "
		cQuery += " 					AND E1_NUM = F2_DOC "
		cQuery += " 					AND E1_TIPO = 'NF' "
		cQuery += " 					AND SE1.D_E_L_E_T_ = ' ' "
		cQuery += " 			) AS E1_VENCREA "
		cQuery += "				, ( "
		cQuery += " 					SELECT "
		cQuery += " 						SUM(SE1.E1_VLCRUZ) "
		cQuery += " 					FROM "+ RetFullName("SE1",aEmp2[nX,1])+" SE1 "
		cQuery += " 					WHERE E1_FILIAL = F2_FILIAL "
		cQuery += " 					AND E1_CLIENTE = F2_CLIENTE "
		cQuery += " 					AND E1_LOJA = F2_LOJA "
		cQuery += " 					AND E1_PREFIXO = F2_SERIE "
		cQuery += " 					AND E1_NUM = F2_DOC "
		cQuery += " 					AND E1_TIPO = 'NF' "
		cQuery += " 					AND SE1.D_E_L_E_T_ = ' '"
		cQuery += " 			) AS E1_VLCRUZ "
		cQuery += "				, ( "
		cQuery += " 					SELECT "
		cQuery += " 						CASE WHEN SUM(SE1.E1_SALDO) > 0 THEN 'Em Aberto' ELSE 'Pago' END "
		cQuery += " 					FROM "+ RetFullName("SE1",aEmp2[nX,1]) + " SE1 "
		cQuery += " 					WHERE E1_FILIAL = F2_FILIAL "
		cQuery += " 					AND E1_CLIENTE = F2_CLIENTE "
		cQuery += " 					AND E1_LOJA = F2_LOJA "
		cQuery += " 					AND E1_PREFIXO = F2_SERIE "
		cQuery += " 					AND E1_NUM = F2_DOC "
		cQuery += " 					AND E1_TIPO = 'NF' "
		cQuery += " 					AND SE1.D_E_L_E_T_ = ' '"
		cQuery += " 			) AS STATUSTIT "
		cQuery += " FROM  " + RetFullName("SF2",aEmp2[nX,1])+" SF2 "

		cQuery += " WHERE "
		cQuery += " 	SF2.F2_FILIAL  IN " + aEmp2[nX,17]
		cQuery += " AND SF2.F2_CLIENTE = '" + c_cliente + "' "
		cQuery += " AND SF2.F2_LOJA    = '" + c_loja + "' "
		cQuery += " AND SF2.F2_EMISSAO BETWEEN '"+cc_de_emissao+"' AND '"+cc_ate_emissao+"' "
		cQuery += " AND SF2.F2_DOC     BETWEEN '"+cc_de_nfiscal+"' AND '"+cc_ate_nfiscal+"' "
		cQuery += " AND SF2.F2_NFELETR BETWEEN '"+cc_de_nfEletr+"' AND '"+cc_ate_nfEletr+"' "

		cQuery += " AND EXISTS  "
		cQuery += " 		(  "
		cQuery += " 			SELECT  "
		cQuery += " 				1   "
		cQuery += " 			FROM    " + RetFullName("SD2",aEmp2[nX,1]) + " SD2 "


		cQuery += " 			INNER JOIN " + RetFullName("SC6",aEmp2[nX,1]) + " SC6 "
		cQuery += " 			ON SC6.C6_FILIAL = SD2.D2_FILIAL "
		cQuery += " 			AND SC6.C6_NUM = SD2.D2_PEDIDO "
		cQuery += " 			AND SC6.C6_ITEM = SD2.D2_ITEMPV "
		cQuery += " 			AND SC6.C6_CLI = SD2.D2_CLIENTE "
		cQuery += " 			AND SC6.C6_LOJA = SD2.D2_LOJA "
		cQuery += " 			AND SC6.D_E_L_E_T_ = '  ' "

		cQuery += " 			INNER JOIN " + RetFullName("ADY",aEmp2[nX,1]) + " ADY "
		cQuery += " 			ON ADY.ADY_FILIAL = '" + xFilial("ADY") + "' "
		cQuery += " 			AND ADY.ADY_PROPOS = SC6.C6_PROPOST "
		cQuery += " 			AND ADY.D_E_L_E_T_ = '  ' "

		cQuery += " 			INNER JOIN " + RetFullName("ADJ",aEmp2[nX,1]) + " ADJ "
		cQuery += " 			ON ADJ.ADJ_FILIAL = ADY.ADY_FILIAL "
		cQuery += " 			AND ADJ.ADJ_NROPOR = ADY.ADY_OPORTU "
		cQuery += " 			AND ADJ.D_E_L_E_T_ = '  ' "

		cQuery += " 			INNER JOIN " + cTabSX5 + " SX5 "
		cQuery += " 			ON SX5.X5_FILIAL = '" + cFilSX5 + "' "
		cQuery += " 			AND SX5.X5_TABELA = 'JF' "
		cQuery += " 			AND SX5.X5_CHAVE = ADJ.ADJ_SUBCAT "
		cQuery += " 			AND SX5.D_E_L_E_T_ = '  ' "

		cQuery += " 			WHERE SD2.D2_FILIAL = SF2.F2_FILIAL "
		cQuery += " 			AND SD2.D2_DOC = SF2.F2_DOC "
		cQuery += " 			AND SD2.D2_SERIE = SF2.F2_SERIE "
		cQuery += " 			AND SD2.D2_CLIENTE = SF2.F2_CLIENTE "
		cQuery += " 			AND SD2.D2_LOJA = SF2.F2_LOJA "
		cQuery += " 			AND SD2.D_E_L_E_T_ = '  ' "

		cQuery += " 		)  "

		cQuery += " AND SF2.D_E_L_E_T_ = ' ' "

	Next nX

	cQuery := " SELECT T1.* FROM ( " + cQuery + ") AS T1 ORDER BY T1.CODEMPRESA, T1.UNIDADE, T1.F2_EMISSAO "

	cQuery := ChangeQuery(cQuery)

	dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQryComp+cQuery), "T_F2" , .F., .T.)

	TcSetField("T_F2","F2_EMISSAO","D",TamSx3("F2_EMISSAO")[1],TamSx3("F2_EMISSAO")[2])
	TcSetField("T_F2","E1_VENCREA","D",TamSx3("E1_VENCREA")[1],TamSx3("E1_VENCREA")[2])
	TcSetField("T_F2","E1_VLCRUZ","N",TamSx3("E1_VLCRUZ")[1],TamSx3("E1_VLCRUZ")[2])
//
	If T_F2->(!Eof()) .And. T_F2->(!Bof())

		While T_F2->(!Eof())

			oCabecalho:=wsclassnew("o_Cloudcabecalho")
			oCabecalho:cloud_empresa	   	:= AllTrim(T_F2->CODEMPRESA)
			oCabecalho:cloud_filial	   		:= AllTrim(T_F2->F2_FILIAL)
			oCabecalho:cloud_unidade	   	:= AllTrim(T_F2->NOMEMPRESA)
			oCabecalho:cloud_cliente	   	:= T_F2->F2_CLIENTE
			oCabecalho:cloud_documento	   	:= AllTrim(T_F2->F2_DOC)
			oCabecalho:cloud_serie		   	:= AllTrim(T_F2->F2_SERIE)
			oCabecalho:cloud_nf_eletronica	:= T_F2->F2_NFELETR
			oCabecalho:cloud_emissao	   	:= DtoC(T_F2->F2_EMISSAO)
			oCabecalho:cloud_vencimento		:= DtoC(T_F2->E1_VENCREA)
			oCabecalho:cloud_Valor			:= Transform(T_F2->E1_VLCRUZ,PesqPict('SE1','E1_VLCRUZ')  )
			oCabecalho:cloud_Status			:= AllTrim(T_F2->STATUSTIT)

			aAdd(::o_Cloud_cabec,oCabecalho)

			T_F2->(dbSkip())

		EndDo

	Else

		oCabecalho:=wsclassnew("o_Cloudcabecalho")
		oCabecalho:cloud_empresa	   	:= ""
		oCabecalho:cloud_filial	   		:= ""
		oCabecalho:cloud_unidade	   	:= ""
		oCabecalho:cloud_cliente	   	:= ""
		oCabecalho:cloud_documento	   	:= ""
		oCabecalho:cloud_serie		   	:= ""
		oCabecalho:cloud_nf_eletronica	:= ""
		oCabecalho:cloud_emissao	   	:= ""
		oCabecalho:cloud_vencimento		:= ""
		oCabecalho:cloud_Valor			:= ""
		oCabecalho:cloud_Status			:= ""

		aAdd(::o_Cloud_cabec,oCabecalho)
	EndIF

	T_F2->(dbCloseArea())

	U_xCONOUT("AUTOATENDIMENTO","CONS_CLOUD_CABEC","RETORNO","")

Return .T.

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºPrograma   ³ChkVend   ³Verifica Ovendedor da proposta			                      º±±
±±º           ³          ³                                                                º±±
±±ÌÍÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºParâmetros ³Nil.                                                                       º±±
±±ÌÍÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºRetorno    ³Nil                                                                        º±±
±±ÌÍÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºExecucao   ³        - Modulo de Viagens                                                º±±
±±ÌÍÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºObservações³Antigo VGMA120                                                             º±±
±±ÌÍÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±º                                DESENVOLVIMENTO INICIAL                                º±±
±±ÌÍÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºChamado    ³ 10.07.07 ³                                                                º±±
±±ÇÄÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¶±±
±±ºSolicitante³ 10.07.07 ³                                                                º±±
±±ÇÄÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¶±±
±±ºAutor      ³ 28/01/14 ³Erich Buttner		                                              º±±
±±ÌÍÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±º                                     ATUALIZACOES                                      º±±
±±ÌÍÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºChamado    ³ Compatibilização Base Relacional                                          º±±
±±ÇÄÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¶±±
±±ºSolicitante³          ³				                                                  º±±
±±ÇÄÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¶±±
±±ºAutor      ³          ³														          º±±
±±ÇÄÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¶±±
±±ºDescricao  ³                                                                           º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/


User Function ChkVend(cProposta,cCodCli,cCodLoj)
	Local aRet 			:= {}
	Local cCLVL			:= ""
	Local cCC			:= ""
	Local aArea			:= GetArea()
	Local cQuery		:= ""
	Local cVend		:= ""
	Local cDvend		:= ""

	DEFAULT cProposta		:= ""
	DEFAULT cCodCli		:= ""
	DEFAULT cCodLoj		:= ""

	If Empty(cProposta) .OR. Empty(cCodCli) .OR. Empty(cCodLoj)
		Aadd(aRet,{cCLVL,cCC})
		RestArea(aArea)
		Return aRet
	EndIf

	DbSelectArea("AD2")
	DbSelectArea("ADJ")
	DbSelectArea("SCK")


//-------------------------------------------------------
//Query para levantamento de time de vendas da proposta	 |
//-------------------------------------------------------

	cQuery := " SELECT AD2_FILIAL, AD2_NROPOR, AD2_REVISA, AD2_HISTOR, AD2_VEND, AD2_PERC, AD2_CODCAR, AD2_XREGRA, AD2_UNIDAD "
	cQuery += " FROM " + RetSQLName("AD2") + " AD2 "
	cQuery += " INNER JOIN " + RetSQLName("ADJ") + " ADJ ON ADJ_FILIAL = AD2_FILIAL "
	cQuery += "                         AND ADJ_NROPOR = AD2_NROPOR "
	cQuery += "                         AND ADJ_REVISA = AD2_REVISA "
	cQuery += "                         AND ADJ.D_E_L_E_T_ <> '*' "
	cQuery += " INNER JOIN " + RetSQLName("SCK") + " SCK ON SCK.D_E_L_E_T_ <> '*'  "
	cQuery += "                          AND CK_FILIAL = '01'   "
	cQuery += "                          AND CK_PROPOST = ADJ_PROPOS   "
	cQuery += "                          AND CK_ITEMPRO = SUBSTR(ADJ_ITEM,2,2)   "
	cQuery += "                      AND CK_PROPOST = '" + cProposta + "' "
	cQuery += " WHERE AD2.D_E_L_E_T_ <> '*' "
	cQuery += " AND AD2_FILIAL = '01' "
	cQuery += " GROUP BY AD2_FILIAL, AD2_NROPOR, AD2_REVISA, AD2_HISTOR, AD2_VEND, AD2_PERC, AD2_CODCAR, AD2_XREGRA, AD2_UNIDAD "
	cQuery := ChangeQuery(cQuery)
	TCQUERY cQuery NEW ALIAS "TIMECRM"


//-------------------------------------------------------
//ï¿½Verifica time de vendas do CRM                     |
//-------------------------------------------------------
	While !TIMECRM->(EOF())
		//Avalia cargos que participam da proposta mas nao participam do forecast
		dbSelectArea("SA3")
		SA3->(dbSetOrder(1))
		If SA3->(dbSeek(xFilial("SA3")+TIMECRM->AD2_VEND  ))
			cVend := TIMECRM->AD2_VEND
			cDvend := SA3->A3_NOME
		EndIf
		TIMECRM->(DbSkip())
	EndDo
	TIMECRM->(DbCloseArea())


	Aadd(aRet,{cVend,cDvend})
	RestArea(aArea)
Return aRet

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ CONS_CLIPCSIST ³ Autor ³ Fernando Nascimento³ Data ³ 19/01/17 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/

WsMethod CONS_CLIPCSIST WsReceive c_Empresa, c_Filial, c_ClientePC WsSend o_RetCliTotvs WsService AUTOATENDIMENTO

	Local _oRet		:= NIL
	Local _cQry		:= ""
	Local _cAlias	:= ""
	Local _lOpen	:= .F.

	U_xCONOUT("AUTOATENDIMENTO","CONS_CLIPCSIST","REQUISICAO","")

//-----------------------------------------------------------------------------------
// Retorno mais rapido das informacoes via WS pois nao sera realizado RpcSetEnv()  //
//-----------------------------------------------------------------------------------
	If ( Select("SX6" ) <= 0 .Or. c_Empresa <> cEmpAnt )
		RpcClearEnv()
		_lOpen := RpcSetEnv(c_Empresa, c_Filial)
	Else
		cEmpAnt := c_Empresa
		cFilAnt := c_Filial
		SM0->( dbSetOrder(1) )
		_lOpen := SM0->( dbSeek( cEmpAnt+cFilAnt ))
	EndIf

	If _lOpen
		_cAlias := GetNextAlias()

		_cQry := "	SELECT			" + CRLF
		_cQry += "		A1_COD		" + CRLF
		_cQry += "		,A1_LOJA	" + CRLF
		_cQry += "	FROM			" + CRLF
		_cQry +=		+ RetSqlName("SA1") + "	" + CRLF
		_cQry += "	WHERE						" + CRLF
		_cQry += "		A1_FILIAL = '" + xFilial("SA1") + "'" + CRLF
		_cQry += "		AND									" + CRLF
		_cQry += "		A1_XCODPCS = '" + c_ClientePC + "'	" + CRLF
		_cQry += "		AND					" + CRLF
		_cQry += "		D_E_L_E_T_ = ' '	" + CRLF

		If Select( _cAlias ) > 0
			dbSelectArea( _cAlias )
			( _cAlias )->( dbCloseArea() )
		EndIf

		TCQUERY _cQry NEW ALIAS ( _cAlias )

		If ( _cAlias )->( !EoF() )
			_oRet := WsClassNew( "o_RetCli" )
			_oRet:c_CodTotvs	:= ( _cAlias )->A1_COD
			_oRet:c_LojTotvs	:= ( _cAlias )->A1_LOJA
		Else
			_oRet := WsClassNew( "o_RetCli" )
			_oRet:c_CodTotvs	:= ""
			_oRet:c_LojTotvs	:= ""
		EndIf
	Else
		_oRet := WsClassNew( "o_RetCli" )
		_oRet:c_CodTotvs	:= ""
		_oRet:c_LojTotvs	:= ""
	EndIf

	AADD(o_RetCliTotvs, _oRet)

	( _cAlias )->( dbCloseArea() )

	U_xCONOUT("AUTOATENDIMENTO","CONS_CLIPCSIST","RETORNO","")

Return(.T.)

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ CONS_TITABERTOS ³Autor ³ Fernando Nascimento³ Data ³ 19/01/17 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONS_TITABERTOS WsReceive c_Empresa, c_Filial, c_Cliente, c_Loja WsSend o_RetTitAb WsService AUTOATENDIMENTO

	Local _oRet		:= NIL
	Local _cQry		:= ""
	Local _cAlias	:= ""
	Local _cCnpj	:= ""
	Local _cRazao	:= ""
	Local _lOpen	:= .F.

	U_xCONOUT("AUTOATENDIMENTO","CONS_TITABERTOS","REQUISICAO","")

//-----------------------------------------------------------------------------------
// Retorno mais rapido das informacoes via WS pois nao sera realizado RpcSetEnv()  //
//-----------------------------------------------------------------------------------
	If ( Select("SX6" ) <= 0 .Or. c_Empresa <> cEmpAnt )
		RpcClearEnv()
		_lOpen := RpcSetEnv(c_Empresa, c_Filial)
	Else
		cEmpAnt := c_Empresa
		cFilAnt := c_Filial
		SM0->( dbSetOrder(1) )
		_lOpen := SM0->( dbSeek( cEmpAnt+cFilAnt ))
	EndIf

	If _lOpen
		_cCnpj	:= GetAdvFVal("SM0", "M0_CGC", ( c_Empresa + c_Filial ), 1)
		_cRazao	:= GetAdvFVal("SM0", "M0_FILIAL", ( c_Empresa + c_Filial ), 1)

		_cQry := "	SELECT			" + CRLF
		_cQry += "		E1_NUM		" + CRLF
		_cQry += "		,E1_EMISSAO	" + CRLF
		_cQry += "		,E1_PREFIXO	" + CRLF
		_cQry += "		,E1_PARCELA	" + CRLF
		_cQry += "		,E1_VENCTO	" + CRLF
		_cQry += "		,E1_VENCREA	" + CRLF
		_cQry += "		,E1_VALOR	" + CRLF
		_cQry += "		,E1_ISS		" + CRLF
		_cQry += "		,E1_CSLL	" + CRLF
		_cQry += "		,E1_PIS		" + CRLF
		_cQry += "		,E1_COFINS	" + CRLF
		_cQry += "		,E1_IRRF	" + CRLF
		_cQry += "		,CASE WHEN E1_VENCREA > TO_CHAR(SYSDATE, 'YYYYMMDD') THEN 'A VENCER' ELSE 'VENCIDO' END AS SITUACAO	" + CRLF
		_cQry += "		,A1_CGC		" + CRLF
		_cQry += "		,A1_NOME	" + CRLF
		_cQry += "	FROM 			" + CRLF
		_cQry += 		+ RetSqlName("SE1") + "	SE1			" + CRLF
		_cQry += "			INNER JOIN						" + CRLF
		_cQry += 				+ RetSqlName("SA1") + "	SA1	" + CRLF
		_cQry += "			ON	" + CRLF
		_cQry += "				A1_FILIAL = '" + xFilial("SA1") + "'	" + CRLF
		_cQry += "				AND						" + CRLF
		_cQry += "				A1_COD = E1_CLIENTE		" + CRLF
		_cQry += "				AND						" + CRLF
		_cQry += "				A1_LOJA = E1_LOJA		" + CRLF
		_cQry += "				AND						" + CRLF
		_cQry += "				SA1.D_E_L_E_T_ = ' '	" + CRLF
		_cQry += "	WHERE	" + CRLF
		_cQry += "		E1_FILIAL = '" + xFilial("SE1") + "'	" + CRLF
		_cQry += "		AND										" + CRLF
		_cQry += "		E1_CLIENTE = '" + c_Cliente + "'		" + CRLF
		_cQry += "		AND										" + CRLF
		_cQry += "		E1_LOJA = '" + c_Loja + "'	" + CRLF
		_cQry += "		AND							" + CRLF
		_cQry += "		E1_SALDO <> 0		" + CRLF
		_cQry += "		AND					" + CRLF
		_cQry += "		E1_TIPO = 'NF'	" + CRLF
		_cQry += "		AND				" + CRLF
		_cQry += "		E1_TIPO <> '" + MVPROVIS + "'	" + CRLF
		_cQry += "		AND					" + CRLF
		_cQry += "		E1_TIPO <> 'PR '	" + CRLF
		_cQry += "		AND					" + CRLF
		_cQry += "		E1_TIPO <> 'RA '	" + CRLF
		_cQry += "		AND					" + CRLF
		_cQry += "		E1_TIPO NOT LIKE '%-'		" + CRLF
		_cQry += "		AND							" + CRLF
		_cQry += "		SE1.D_E_L_E_T_ = ' '		" + CRLF
		_cQry += "	ORDER BY		" + CRLF
		_cQry += "		E1_NUM		" + CRLF
		_cQry += "		,E1_PREFIXO	" + CRLF
		_cQry += "		,E1_PARCELA	" + CRLF

		_cAlias := GetNextAlias()

		If Select( _cAlias ) > 0
			DbSelectArea( _cAlias )
			( _cAlias )->( DbCloseArea() )
		EndIf

		TCQUERY _cQry NEW ALIAS ( _cAlias )

		If ( _cAlias )->( !EoF() )
			While ( _cAlias )->( !EoF() )

				_oRet := WsClassNew( "o_RetTit" )
				_oRet:c_Titulo		:= ( _cAlias )->E1_NUM
				_oRet:c_Parcela		:= ( _cAlias )->E1_PARCELA
				_oRet:c_Vencto		:= DtoC(StoD(( _cAlias )->E1_VENCTO))
				_oRet:c_VencRea		:= DtoC(StoD(( _cAlias )->E1_VENCREA))
				_oRet:c_ValorBruto	:= STR(( _cAlias )->E1_VALOR)
				_oRet:c_ValorLiq	:= STR(( ( _cAlias )->E1_VALOR - (( _cAlias )->(E1_ISS + E1_CSLL + E1_PIS + E1_COFINS + E1_IRRF)) ))
				_oRet:c_Situacao	:= ( _cAlias )->SITUACAO
				_oRet:c_CNPJ		:= ( _cAlias )->A1_CGC
				_oRet:c_RazaoSoc	:= ( _cAlias )->A1_NOME
				_oRet:c_CnpjPC		:= _cCnpj
				_oRet:c_RazaoSocPC	:= _cRazao
				_oRet:c_Prefix		:= ( _cAlias )->E1_PREFIXO
				_oRet:c_Emissao		:= DtoC(StoD(( _cAlias )->E1_EMISSAO))

				AADD(o_RetTitAb, _oRet)

				( _cAlias )->( DbSkip() )
			EndDo
		Else
			_oRet := WsClassNew( "o_RetTit" )
			_oRet:c_Titulo		:= ""
			_oRet:c_Parcela		:= ""
			_oRet:c_Vencto		:= ""
			_oRet:c_VencRea		:= ""
			_oRet:c_ValorBruto	:= ""
			_oRet:c_ValorLiq	:= ""
			_oRet:c_Situacao	:= ""
			_oRet:c_CNPJ		:= ""
			_oRet:c_RazaoSoc	:= ""
			_oRet:c_CnpjPC		:= ""
			_oRet:c_RazaoSocPC	:= ""
			_oRet:c_Prefix		:= ""
			_oRet:c_Emissao		:= ""

			AADD(o_RetTitAb, _oRet)
		EndIf

		( _cAlias )->( DbCloseArea() )
	Else
		_oRet := WsClassNew( "o_RetTit" )
		_oRet:c_Titulo		:= ""
		_oRet:c_Parcela		:= ""
		_oRet:c_Vencto		:= ""
		_oRet:c_VencRea		:= ""
		_oRet:c_ValorBruto	:= ""
		_oRet:c_ValorLiq	:= ""
		_oRet:c_Situacao	:= ""
		_oRet:c_CNPJ		:= ""
		_oRet:c_RazaoSoc	:= ""
		_oRet:c_CnpjPC		:= ""
		_oRet:c_RazaoSocPC	:= ""
		_oRet:c_Prefix		:= ""
		_oRet:c_Emissao		:= ""

		AADD(o_RetTitAb, _oRet)
	EndIf

	U_xCONOUT("AUTOATENDIMENTO","CONS_TITABERTOS","RETORNO","")

Return( .T. )

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ CONS_GETXML     ³Autor ³ Fernando Nascimento³ Data ³ 19/01/17 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WsMethod CONS_GETXML WsReceive c_Empresa, c_Filial, c_Entidade, c_SerieNota WsSend o_RetXml WsService AUTOATENDIMENTO

	Local _cRet			:= ""
	Local _nX			:= 0
	Local _oRet			:= NIL
	Local _oAux			:= NIL
	Local _oWsNFeSBRA	:= NIL
	Local _lRetornaFxOk	:= .F.
	Local _lOpen		:= .T.

	U_xCONOUT("AUTOATENDIMENTO","CONS_GETXML","REQUISICAO","")

//WSDLSetProfile(.T.)
//WSDLDbgLevel(2)

//-----------------------------------------------------------------------------------
// Retorno mais rapido das informacoes via WS pois nao sera realizado RpcSetEnv()  //
//-----------------------------------------------------------------------------------
	If ( Select("SX6" ) <= 0 .Or. c_Empresa <> cEmpAnt )
		RpcClearEnv()
		_lOpen := RpcSetEnv(c_Empresa, c_Filial)
	Else
		cEmpAnt := c_Empresa
		cFilAnt := c_Filial
		SM0->( dbSetOrder(1) )
		_lOpen := SM0->( dbSeek( cEmpAnt+cFilAnt ))
	EndIf

	If _lOpen
		//------------------------------------------------------------------------------------------
		// Sera utilizado o metodo "NFSE001" do proprio TSS para que se possa consultar e obter o //
		// XML da nota de acordo com o que eh enviado ao WS como parametro (entidade e serie+nf)  //
		//------------------------------------------------------------------------------------------
		_oWsNFeSBRA						:= WSNFSE001():New()
		_oWsNFeSBRA:cUSERTOKEN			:= "TOTVS"
		_oWsNFeSBRA:cID_ENT				:= c_Entidade
		_oWsNFeSBRA:_URL				:= AllTrim(GetMv("MV_SPEDURL",,"http://")) + "/NFSE001.apw"
		_oWsNFeSBRA:nDiasParaExclusao	:= 0 //Deve-se passar este parametro como zero

		_oWsNFeSBRA:oWsNFSEID						:= WsClassNew("NFSE001_NFSID")
		_oWsNFeSBRA:oWsNFSEID:oWsNotas				:= WsClassNew("NFSE001_ARRAYOFNFSESID1")
		_oWsNFeSBRA:oWsNFSEID:oWsNotas:oWsNFSESID1	:= {}

		_oAux		:= WsClassNew("NFSE001_NFSESID1")
		_oAux:cID	:= c_SerieNota

		//------------------------------------------------------------------------------------------
		// A serie+nf deve ser passada como array para que o retorno seja capturado corretamente  //
		//------------------------------------------------------------------------------------------
		AADD(_oWsNFeSBRA:oWsNFSEID:oWsNotas:oWsNFSESID1, _oAux)

		_lRetornaFxOk := _oWsNFeSBRA:RetornaNFSE()
		If _lRetornaFxOk
			If !ValType(_oWsNFeSBRA:OWSRETORNANFSERESULT:oWSNOTAS) == NIL
				If !ValType(_oWsNFeSBRA:OWSRETORNANFSERESULT:oWSNOTAS:oWSNFSES5) == NIL
					_oAux := _oWsNFeSBRA:OWSRETORNANFSERESULT:oWSNOTAS:oWSNFSES5

					//------------------------------------------------------------------------------------------
					// O XML que eh exportado pela rotina do Protheus pode ser encontrado no node "CXMLPROT". //
					//------------------------------------------------------------------------------------------
					If ValType(_oAux) == "A"
						For _nX := 1 To Len( _oAux )
							_cRet += _oAux[_nX]:oWsNFE:cXmlProt
						Next _nX
					Else
						_cRet := "Falha no retorno do XML/objeto que contém os dados do XML"
					EndIf
				EndIf
			EndIf
		Else
			_cRet := "Falha na captura do XML de acordo com os dados enviados"
		EndIf
	Else
		_cRet := "Falha na comunicacao com a empresa " + c_Empresa + " e filial " + c_Filial
	EndIf

	_oRet := WsClassNew( "o_XmlProt" )
	_oRet:c_XmlProt := _cRet

	AADD(o_RetXml, _oRet)

	U_xCONOUT("AUTOATENDIMENTO","CONS_GETXML","RETORNO","")

Return( .T. )

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ CONS_PCCABEC     ³ Autor ³ Cleyton Ferreira ³ Data ³ 25/10/10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ TOTVS - TDI                                                   ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
	WsMethod CONS_PCCABEC ;
		WsReceive c_cliente, c_loja, cc_de_emissao, cc_ate_emissao, cc_de_nfiscal, cc_ate_nfiscal, cc_de_nfEletr, cc_ate_nfEletr, co_de_CodNfe, co_ate_CodNfe ;
		WsSend o_pccabec ;
		WsService AUTOATENDIMENTO

		Local cQuery     	:= ""
		Local cQryComp   	:= ""
		Local cEmpTemp   	:= ""
		Local cFilTemp   	:= ""
		Local cCnpj			:= ""
		Local nPos			:= 0
		Local nV         	:= 0
		Local nX         	:= 0
		Local aSigaMat   	:= WS_Sw_SigaMat()
		Local aDados     	:= {}
		Local oCabecalho	:= NIL

		Public oMsgItem3

		U_xCONOUT("AUTOATENDIMENTO","CONS_PCCABEC","REQUISICAO","")

		For nV := 1 To Len(aSigaMat)

			cEmpTemp	:= aSigaMat[nV,1]
			cFilTemp	:= aSigaMat[nV,2]
			cCnpj		:= GetAdvFVal("SM0", "M0_CGC", ( cEmpTemp + cFilTemp ), 1)

			cQuery += "SELECT '"+ cEmpTemp +"' EMPRESA, '" + aSigaMat[nV,3] +"' UNIDADE, "
			cQuery += "SF2.F2_CLIENTE, SF2.F2_LOJA   , SF2.F2_DOC   , "
			cQuery += "SF2.F2_SERIE  , SF2.F2_NFELETR, SF2.F2_EMISSAO, SF2.F2_FILIAL, SF2.F2_VALBRUT, SF2.F2_CODNFE, "
			cQuery += "(SELECT MIN(SE1.E1_VENCTO) FROM "+ RetFullName("SE1",cEmpTemp)+" SE1 WHERE E1_FILIAL = F2_FILIAL  "
			cQuery += "AND E1_CLIENTE = F2_CLIENTE AND E1_LOJA = F2_LOJA AND E1_PREFIXO = F2_SERIE "
			cQuery += "AND E1_NUM = F2_DOC AND E1_TIPO = 'NF') E1_VENCREA"
			cQuery += "FROM  " + RetFullName("SF2",cEmpTemp)+" SF2 "
			cQuery += "WHERE "
			cQuery += "SF2.F2_FILIAL  = '"+cFilTemp+"'	AND "
			cQuery += "SF2.F2_CLIENTE = '"+c_cliente+"'	AND "
			cQuery += "SF2.F2_LOJA    = '"+c_loja+"' 	AND "
			cQuery += "SF2.F2_EMISSAO BETWEEN '"+cc_de_emissao+"' AND '"+cc_ate_emissao+"' AND "
			cQuery += "SF2.F2_DOC     BETWEEN '"+cc_de_nfiscal+"' AND '"+cc_ate_nfiscal+"' AND "
			cQuery += "SF2.F2_NFELETR BETWEEN '"+cc_de_nfEletr+"' AND '"+cc_ate_nfEletr+"' AND "
			cQuery += "SF2.F2_CODNFE	BETWEEN '" + co_de_CodNfe + "' AND '" + co_ate_CodNfe + "' AND "
			cQuery += "SF2.D_E_L_E_T_ = ' ' "

			cQuery := ChangeQuery(cQuery)

			dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQryComp+cQuery), "T_F2" , .F., .T.)

			While T_F2->(!Eof())

				nPos := aScan(aDados,{|x| 	AllTrim(x[1])			+AllTrim(x[2])				+AllTrim(x[6])			+AllTrim(x[7])	== ;
					AllTrim(aSigaMat[nV,1])	+AllTrim(T_F2->F2_FILIAL)	+AllTrim(T_F2->F2_DOC)	+AllTrim(T_F2->F2_SERIE)})

				If nPos == 0

					aAdd(aDados,{	AllTrim(aSigaMat[nV,1])			,;//01
					AllTrim(T_F2->F2_FILIAL)		,;//02
					aSigaMat[nV,3]					,;//03
					T_F2->F2_CLIENTE				,;//04
					AllTrim(T_F2->F2_DOC)			,;//05
					AllTrim(T_F2->F2_SERIE)			,;//06
					T_F2->F2_NFELETR				,;//07
					DtoC(StoD(T_F2->F2_EMISSAO))	,;//08
					DtoC(StoD(T_F2->E1_VENCREA))	,;//09
					STR(T_F2->F2_VALBRUT)			,;//10
					AllTrim(T_F2->F2_CODNFE)		,;//11
					cCnpj							})//12

					nPos := Len(aDados)
				EndIf

				T_F2->(dbSkip())

			EndDo

			T_F2->(dbCloseArea())

			cQuery := ""
		Next nV

		If Len(aDados) == 0
			aAdd(aDados,{"","","","","","","","","","","",""})
		EndIf

		aSort(aDados,,,{|x,y| CtoD(X[8]) > CtoD(y[8]) })

		For nx := 1 To Len(aDados)

			oCabecalho:=wsclassnew("o_pccabecalho")
			oCabecalho:co_empresa		:= aDados[nx,01]	//01-AllTrim(aSigaMat[nV,1]
			oCabecalho:co_filial		:= aDados[nx,02]	//02-AllTrim(T_F2->F2_FILIAL)
			oCabecalho:co_unidade		:= aDados[nx,03]	//03-aSigaMat[nV,3]
			oCabecalho:co_cliente		:= aDados[nx,04]	//04-T_F2->F2_CLIENTE
			oCabecalho:co_documento		:= aDados[nx,05]	//05-AllTrim(T_F2->F2_DOC)
			oCabecalho:co_serie			:= aDados[nx,06]	//06-AllTrim(T_F2->F2_SERIE)
			oCabecalho:co_nf_eletronica	:= aDados[nx,07]	//07-T_F2->F2_NFELETR
			oCabecalho:co_emissao		:= aDados[nx,08]	//08-DtoC(StoD(T_F2->F2_EMISSAO))
			oCabecalho:co_vencimento	:= aDados[nx,09]	//09-DtoC(StoD(T_F2->E1_VENCREA))
			oCabecalho:co_valbruto		:= aDados[nx,10]	//10-STR(T_F2->F2_VALBRUT)
			oCabecalho:co_chavenfe		:= aDados[nx,11]	//11-AllTrim(T_F2->F2_CODNFE)
			oCabecalho:co_cnpjpc		:= aDados[nx,12]	//12-cCnpj

			aAdd(::o_pccabec,oCabecalho)

		Next nx

		U_xCONOUT("AUTOATENDIMENTO","CONS_PCCABEC","RETORNO","")

		Return .T.

//--------------------------------------------
// Consumo de WS - TFATS001
//--------------------------------------------
User Function TFATS001( __cEmpresa, __cFilial, __cUrl, __cMetodo, __aParam )

	Local oWsdl		:= NIL
	Local nPosMtd	:= 0
	Local nQtParam	:= 0
	Local aOps		:= {}
	Local cRet		:= ""
	Local lRet		:= .T.

	Default __cEmpresa 	:= "00"
	Default __cFilial	:= "00001000100"
	Default __cUrl		:= "http://wscorp.totvs.com.br/AUTOATENDIMENTO.apw?WSDL"
	Default __cMetodo	:= ""
	Default __aParam	:= {}

	RpcClearEnv()
	RpcSetType(3)
	RpcSetEnv( __cEmpresa, __cFilial )

	//-------------------------------------------
	// Cria o objeto da classe TWsdlManager
	//-------------------------------------------
	oWsdl := TWsdlManager():New()
	oWsdl:lVerbose := .T.

	//-------------------------------------------
	// Faz o parse de uma URL
	//-------------------------------------------
	lRet := oWsdl:ParseURL( AllTrim( __cUrl ) )

	If !lRet
		cRet := "Erro ParseURL: " + oWsdl:cError
		Return( {lRet, cRet} )
	EndIf

	//-------------------------------------------
	// Verifica se o metodo existe
	//-------------------------------------------
	aOps	:= oWsdl:ListOperations()
	nPosMtd := aScan( aOps, { |x| Upper(x[1]) == Upper(__cMetodo) } )

	If nPosMtd <= 0
		cRet := "Erro: Método " + Upper(__cMetodo) + " não encontrado."
		Return( {lRet, cRet} )
	EndIf

	//-------------------------------------------
	// Define a operacao
	//-------------------------------------------
	lRet := oWsdl:SetOperation( aOps[nPosMtd,1] )

	If !lRet
		cRet := "Erro: SetOperation: " + oWsdl:cError
		Return( {lRet, cRet} )
	EndIf

	//--------------------------------------------------------------------
	// Define o valor de cada parametro a ser passado para o método
	//--------------------------------------------------------------------
	For nQtParam := 1 To Len( __aParam )
		lRet := oWsdl:SetValue( nQtParam-1 , __aParam[nQtParam]	)
	Next nQtParam

	//--------------------------------------------------------------------------------------------------
	// Se retornou o valor correto, envia a URL da nota para ser visualizada no site da prefeitura
	//--------------------------------------------------------------------------------------------------
	lRet := oWsdl:SendSoapMsg()

	If !lRet
		cRet := "Erro SendSoapMsg: " + oWsdl:cError
	Else
		cRet := oWsdl:GetParsedResponse()
	EndIf

	oWsdl := NIL

Return( {lRet, cRet} )



//-------------------------------------------------------------------
/*/{Protheus.doc} AvalCli
Verificar de onde extrair o cliente, conforme empresa conectada e cgc
issue TIBACKOP-2218 

* Se a empresa for 60
  - Se código não inicia com T :
		Procura pelo código do cliente na SA1600. Se achou utiliza ele. Se não achou, pesquisa pelo
		código do cliente na SA1000. Se achou, pega o CGC dele. Se CGC <> '', pesquisa de volta na SA1600 pelo CGC
		achando assim o código de cliente correspondente. Utilizar este ultimo localizado no WS!
		

  - Se código inicia com T :
		Procura pelo código do cliente na SA1000. Se achou, pega o CGC dele. Se CGC <> '', pesquisa na 
		SA16000 pelo CGC achando assim o código de cliente correspondente. Utilizar este ultimo localizado no WS!

* Se a empresa for 00
  - Se código não inicia com T :
		Procura pelo código do cliente na SA1000. Se achou utiliza ele. Se não achou, pesquisa pelo
		código do cliente na SA1600. Se achou, pega o CGC dele. Se CGC <> '', pesquisa de volta na SA1000 pelo CGC
		achando assim o código de cliente correspondente. Utilizar este ultimo localizado no WS!
		

  - Se código inicia com T :
		Procura pelo código do cliente na SA1000 e utiliza ele!


PREMISSAS TIBACKOP-2645 (PÓS OTIMIZAÇÃO DA FUNÇÃO) =========

PREMISSA 1)

Os responsáveis pelo campo AI0_CODDMS em Projetos deverá garantir que todos cliente Dimensa seja amarrado sempre a um único cliente da Matriz. Garantindo a integridade abaixo entre AI0000 e SA1600:

AI0_CODDMS = A1_COD
AND AI0_LOJDMS = A1_LOJA
AND AI0_CLIDMS = '1'

ps: foram encontrados cerca de 100 clientes sem amarração hoje e projetos deverá efetuar o ajuste de base regularizando

PREMISSA 2 ) 

Não poderá haver na Matriz um código de cliente SA1000->A1_COD que exista igual na Dimensa SA1600->A1_COD
Exemplo:

SA1000_COD   SA1600_COD
T50700       D00001  (aceito)
CLE001       D00002  (aceito)
PAP001       PAP001  (NÃO ACEITO), caso contrário fura a nossa regra

PREMISSA 3 )

Só existem campos de link entre SA1600 e AI000. Ou seja, quando se tratar de outra empresa, 70,77 etc a nossa função de pesquisa de clientes não funcionará. Deverá ser feito projeto reformulando toda a solução. 

PREMISSA 4 )
Os campos abaixo da AI0600 jamais poderão estar preenchidos e não serão utilizados

AI0_CODDMS = 
AND AI0_LOJDMS = 
AND AI0_CLIDMS = 

PREMISSA 5 )
- A CORRESPONDÊNCIA DE CLIENTE DA DIMENSA PARA A MATRIZ SEMPRE SERÁ DE 1 PARA 1
- TODO CLIENTE DA DIMENSA DEVERÁ OBRIGATORIAMENTE TER 1 UNICO CORRESPONDENTE NA MATRIZ
- NEM TODO CLIENTE DA MATRIZ TERÁ UM CLIENTE DIMENSA

<AUTHOR> Leite
@since 21/06/2024
@version Troca.V2
@return
/*/
//-------------------------------------------------------------------

User function AvalCli(cWSEMP, cod_cliente, cod_loja, cod_vend, cod_sit)
Local lRet       := .F. 
Local cSa1Alias  := GetNextAlias()
Local cAI0Matr   := "% "+ RetFullName("AI0","00") +" %"
default cod_vend := ""
default cod_sit  := "" 

If cWSEMP == "60"
	BeginSql Alias cSa1Alias
		SELECT A1_COD, A1_LOJA, A1_VEND, A1_XSITCLI 
		FROM  %Table:SA1% DIME     //SA1600 
		WHERE DIME.%notdel%
		AND   DIME.A1_COD     = %exp:(cod_cliente)%  
		AND   DIME.A1_LOJA    = %exp:(cod_loja)% 
			
		UNION 

		SELECT A1_COD, A1_LOJA, A1_VEND, A1_XSITCLI 
		FROM   %Exp:cAI0Matr% MATR INNER JOIN  %Table:SA1% DIMEN ON // AI0000 e SA1600 
			MATR.AI0_CODDMS = DIMEN.A1_COD
		AND MATR.AI0_LOJDMS = DIMEN.A1_LOJA
		AND MATR.AI0_CLIDMS = '1'
		AND MATR.D_E_L_E_T_ = ' '
		WHERE DIMEN.%notdel% AND 
			  MATR.%notdel%  AND 
			  MATR.AI0_CODCLI = %exp:(cod_cliente)%  AND 
		      MATR.AI0_LOJA   = %exp:(cod_loja)% 
	EndSql
Else //"00"
	BeginSql Alias cSa1Alias
			SELECT A1_COD, A1_LOJA, A1_VEND, A1_XSITCLI 
			FROM %Table:SA1%  MATR   //SA1000 
			WHERE MATR.%notdel% 
			AND   MATR.A1_COD     = %exp:(cod_cliente)%  
			AND   MATR.A1_LOJA    = %exp:(cod_loja)% 
			
			UNION 

			SELECT A1_COD, A1_LOJA, A1_VEND, A1_XSITCLI 
			FROM %Table:AI0%  MATR1 INNER JOIN %Table:SA1%  MATR2 ON    // AI0000  SA1000
				MATR1.AI0_FILIAL = MATR2.A1_FILIAL AND
				MATR1.AI0_CODCLI = MATR2.A1_COD    AND
				MATR1.AI0_LOJA   = MATR2.A1_LOJA 
				
			WHERE MATR2.%notdel% AND 
				  MATR1.%notdel% AND 
				  MATR1.AI0_CODDMS  = %exp:(cod_cliente)%  AND 
			      MATR1.AI0_LOJDMS  = %exp:(cod_loja)%     AND 
				  MATR1.AI0_CLIDMS  = '1'
	EndSql
EndIf 

If (cSa1Alias)->(!Eof()) //conforme premissas no enunciado da função só deverá haver 1 registro correspondente sempre
	cod_cliente := (cSa1Alias)->(A1_COD)
	cod_loja    := (cSa1Alias)->(A1_LOJA)
	cod_vend	:= (cSa1Alias)->(A1_VEND)
	cod_sit     := (cSa1Alias)->(A1_XSITCLI) 
	lRet        := .T. 
Endif 

(cSa1Alias)->(DbCloseArea())

return lRet 
//-------------------------------------------------------------------
/*/{Protheus.doc} SetFGen
Parametros de link de filiais

<AUTHOR> Leite
@since 21/06/2024
@version Troca.V2
@return
/*/
//-------------------------------------------------------------------
static function SetFGene () // 44
cMaskPfsp   := GetMV('TI_FAT15',,'https://nfe.prefeitura.sp.gov.br/contribuinte/notaprint.aspx?nf=__NF__&inscricao=__IM__&verificacao=__CV__S&returnurl=..%2fverificacao.aspx%3ftipo%3d0')
cMaskPfre   := GetMV('TI_FAT19',,'https://nfse.recife.pe.gov.br/contribuinte/notaprint.aspx?nf=__NF__&inscricao=__IM__&verificacao=__CV__S&returnurl=..%2fverificacao.aspx%3ftipo%3d0')
cMaskPfjv   := GetMV('TI_FAT20',,'https://www.nfem.joinville.sc.gov.br/processos/imprimir_nfe.aspx?codigo=__CV__&numero=__NF__&documento_prestador=__IM__')
_cMaringMsk := GetMv("TI_PFCIANO",,"https://maringa.fintel.com.br/ImprimirNfse/__NF__/__MARINGA__/__CV__")
cMaskPfFL   := GetMV('TI_FAT20A',,'https://nfps-e.pmf.sc.gov.br/consulta-frontend/#!/consulta?cod=__CV__&cmc=__IM__')
cMaskPfbh   := GetMV('TI_FAT21',,"https://bhissdigital.pbh.gov.br/nfse/pages/consultaNFS-e_cidadao.jsf")
cMaskPfrj   := GetMV('TI_FAT22',,"https://notacarioca.rio.gov.br/contribuinte/notaprint.aspx?nf=__NF__&inscricao=__IM__&verificacao=__CV__&returnurl=..%2fDocumentos%2fverificacao.aspx")
cMaskPfba   := GetMV('TI_FAT23',,'https://nfse.salvador.ba.gov.br/site/contribuinte/nota/notaprint.aspx?inscricao=__IM__&nf=__NF__&verificacao=__CV__')
cMaskPfcp   := GetMV('TI_FAT26',,"http://nfse.campinas.sp.gov.br/NotaFiscal/verificarAutenticidade.php")
cMaskPfWS   := GetMv("TI_PFWSYST",,"http://sync-pr.nfs-e.net/datacenter/include/nfw/nfw_imp_notas.php?codauten=__NF__") //Wealth - System
cMPfCaxias  := GetMv("TI_PFCAXIA",,'https://nfse.caxias.rs.gov.br/nfse/consultaExterna') //TOTVS CAXIAS DO SUL 
cMPfAssis   := GetMv("TI_PFASSIS",,"http://pmassis.dyndns-server.com/issweb/paginas/public/formConsultarRps.jsf")
_cMaskPfMa  := GetMV('TI_FAT24',,"https://spe.macae.rj.gov.br/nfse/nfse.aspx?ccm=__IM__&nf=__NF__&cod=__CV__") // Totvs Macae
_cMaskPfDf  := GetMV('TI_FAT25',,"https://dec.fazenda.df.gov.br/AConsulta.aspx?tp=consultaCompleta") // Totvs Brasilia
cMaskPfPa   := GetMv('TI_PFPALK',,'https://nfe.portoalegre.rs.gov.br/nfse/pages/consultaNFS-e_cidadao.jsf') // Totvs Porto Alegre
_cMaskGoias := GetMv("TI_PFGOIAS",,"http://www2.goiania.go.gov.br/sistemas/snfse/asp/snfse00200w0.asp?inscricao=__IM__&nota=__NF__&verificador=__CV__") // PC Sistemas
	
_cMaskCuri  := GetMv("TI_PFCURIT",,"http://isscuritiba.curitiba.pr.gov.br/NotaCuritibana/NotaRPS/AutenticidadeNota?doc=4364470000195&num=__NF__&cod=__CV__") //CIASHOP - CURITIBA
_cMaskRPrt  := GetMv("TI_PFRPRET",,"http://visualizar.ginfes.com.br/report/consultarNota?__report=nfs_ribeirao_preto&cdVerificacao=__CV__&numNota=__NF__&cnpjPrestador=__CGC__") //CONSINCO - RIBEIRAO PRETO
_cUrlLimei  := GetMv('TI_PFLIMEI',,'https://limeira.iibrasil.com.br/login.php') // LIMEIRA
cDtbarue    := SUPERGETMV('TI_DTBARU',.F.,'20150430') //- ALTERADO POR R.R.MEZZALIRA - 11/06/2015 CHAMADO TRYJ86 - PARAMETRO PARA INDICAR DATA DE CORTE PRA MUDANÇA DO SITE DA NFE PRA BARUERI
cPrefRecif  := GetMv("TI_RECPREF",,"0000201000100/0000202000100")
cPrefSC     := GetMv("TI_PREFSC",,"0000001000600/0000001001700")
cPrefFL     := GetMv("TI_PREFSC2",,"0004601000100/0000202001000")
cPrefBH     := GetMv("TI_PREFBH",,"0000001001200")
cPrefRJ     := GetMv("TI_PREFRJ",,"0000001000800/0000101000100/0000401000100/0000102000100")
cPrefCam    := GetMv("TI_PREFCAM",,"0000001001900")
cPrefWealth := GetMv("TI_PREFWSI",,"0002801000100") //WEALTH SYSTEMS
cPrefAlp    := GetMv("TI_PREFALP",,"0000801000100/0000001001000")
lUsaAlpSP   := GetMv("TI_USAALPS",,.T.) //Indica se vai usar a mudanção do link de Alphaville para São Paulo
cPrefAlp1   := GetMv("TI_PREALP1",,"0000001001000")//Filial de Alphaville que virou para SP
cPrefMac    := GetMv("TI_PREFMAC",,"0000001002300")
cPrefDF     := GetMv("TI_PREFDF",,"0000301000100")
cPrefPOA    := GetMv("TI_PREFPOA",,"0000001001100/6060001000200")
cPrefASS    := GetMv("TI_PREFASS",,"0000001002700")
_cPrefPCGoi := GetMv("TI_PREFGOI",,"0000302000300*0000302000500") //PC Sistemas Goias
_cPrefPCBh  := GetMv("TI_PREFPBH",,"0000302000200") //PC Sistemas BH
_cPrefCian  := GetMv("TI_PREFCIA",,"0000001003100") //Agora será Maringa apenas
cMaringa    := GetMv("TI_MARINGA",,"53113791003148") 
cPrefSSABA  := GetMv("TI_PREFSSA",,"0000202000200") //TOTVS RECIFE - FILIAL BA
cPrefCurit  := GetMv("TI_PREFCUR",,"0001101000100") //CIASHOP - CURITIBA
cPrefRibPre := GetMv("TI_PREFRPT",,"0002701000100") //CONSINCO - RIBEIRAO PRETO
cPrefDimSP  := GetMv("TI_PREFDIM",,"6060001000100") //DIMENSA - SAO PAULO
cPrefCAX	:= GetMv("TI_PREFCAX",,"0000001002600") //TOTVS CAXIAS DO SUL
_cPrefLime  := GetMv("TI_PREFLIM",,"0000202000900") // LIMEIRA

return 

//-------------------------------------------------------------------
/*/{Protheus.doc} RetMvParam


<AUTHOR> Leite
@since 04/12/2024
@version Troca.V2
@return
/*/
//-------------------------------------------------------------------
static function RetMvParam (cParametro)

If Alltrim(cParametro) == "TI_SUPSTA"
	return  SuperGetMv("TI_SUPSTA", , "8")  
Endif

If Alltrim(cParametro) == "TI_S01USDT"
	return  GetMv("TI_S01USDT",,.T.)
Endif

If Alltrim(cParametro) == "TI_S01DATA"
	return GetMv("TI_S01DATA",,"20000101|20491231")
Endif

If Alltrim(cParametro) == 'PS_GETEMPR'
	return GetMV('PS_GETEMPR',,.t.)
Endif

If Alltrim(cParametro) == "TI_S02USDT"
	return GetMv("TI_S02USDT",,.T.)
Endif

If Alltrim(cParametro) == "TI_WSGRP"
	return STRTOKARR(GetMV("TI_WSGRP",,"9999"),"/")
Endif 

return 

//-------------------------------------------------------------------
/*/{Protheus.doc} F340VerBxFil
Função replicada a partir da existente no fonte FINR340.PRW

<AUTHOR> Leite
@since 04/12/2024
@version Troca.V2
@return
/*/
//-------------------------------------------------------------------
Static Function F340VerBxFil( aValor, aFiliais, nMoeda )

Local aTmpValor := {}
Local nX		:= 0
Local nY		:= 0

// Pesquisa baixas do titulo em outras filiais
For nX := 1 To Len(aFiliais)
	If aFiliais[nX] <> SE1->E1_FILIAL
		AAdd( aTmpValor, Baixas(SE1->E1_NATUREZ,SE1->E1_PREFIXO,SE1->E1_NUM,SE1->E1_PARCELA,SE1->E1_TIPO,nMoeda,"R",SE1->E1_CLIENTE,dDataBase,SE1->E1_LOJA,aFiliais[nX],,,.T.) )
	EndIf	
Next nX                                       

// Atualiza valores das baixas em outras filiais no vetor definitivo aValor
For nX := 1 To Len(aTmpValor)
	For nY := 1 To Len(aValor)
		If nY <> 9	// Nao soma historico de baixa
			aValor[nY] += aTmpValor[nX,nY]
		EndIf	
	Next nY
Next nX                                                                    

// Para impressao, guarda o historico da primeira baixa encontrada
If Empty( aValor[9] )
	For nX := 1 To Len( aTmpValor )
		If !Empty( aTmpValor[ nX , 9 ] )
			aValor[ 9 ] := aTmpValor[ nX , 9 ]
			Exit
		EndIf
	Next nX
EndIf

aSize( aTmpValor , 0 )
aTmpValor := Nil

Return( aValor )
