# Documentação - Funções que utilizam RPCSetEnv e/ou alteram cEmpAnt ou __cUserId

## Resumo da Análise
Esta documentação apresenta os resultados da busca por funções na pasta "Santo Graal" que utilizam:
- **RPCSetEnv**: Para configuração de ambiente RPC
- **cEmpAnt**: Variável que armazena o código da empresa atual
- **__cUserId**: Variável que armazena o ID do usuário atual

## Arquivos Analisados

### 1. THLTXFUN.prw (comum\THLTXFUN.prw)

**Funções que utilizam RPCSetEnv:**
- **U_PF4Rpc()**: Executa funções por RPC
  - Linha: `lEnvConfigured := oServer:CallProc("RpcSetEnv", cEmp /*cRpcEmp*/, cFil/*cRpcFil*/, /*cEnvUser*/, /*cEnvPass*/, Nil /*cEnvMod*/, /*cFunName*/, /*aTables*/, /*lShowFinal*/, /*lAbend*/, /*lOpenSX*/, /*lConnect*/)`

- **U_PF4CallProc()**: Chamada das funções de atualização Santo Graal em Paralelo
  - Linha: `If !RpcSetEnv(cEmpThread, cFilThread)`

- **U_PF4Job()**: Rotina via Schedule para atualização de contratos
  - Linha: `If !RPCSETENV( aJob[1] , aJob[2])`
  - Linha: `RpcSetEnv("00",; // c - Código da empresa..`

**Funções que utilizam cEmpAnt:**
- **U_PF4ChkCtr()**: Levantamento de contratos e CNPJs
  - Linha: `nP := aScan(aRet[3], {|x| x[3] == P22->P22_PRODUT .and. x[10] == cEmpAnt+cFilAnt})`
  - Linha: `cEmpAnt+cFilAnt,; //³01 - Empresa/Filial : EEFF`
  - Linha: `Posicione("SM0" , 1 , cEmpAnt+cFilAnt ,"M0_FILIAL"),; //³11 - Descrição da Unidade de Negócio`

- **U_PF4Thread()**: Executa thread de processamento paralelo
  - Linha: `StartJob( "U_PF4CallProc", GetEnvServer(), .F., cEmpAnt, cFilAnt, cCdCli, cLjCli, cTipo, xParmA, cUserName)`

**Funções que utilizam __cUserId:**
- **U_PF4ChkCtr()**: Levantamento de contratos e CNPJs
  - Linha: `cUsuario := AllTrim(UsrRetMail(__cUserID))`

- **SourceSlot()**: WS que busca os slot dos produtos no HLCloud
  - Linha: `cUsuario := AllTrim(UsrRetMail(__cUserID))`

### 2. THLTX001.prw (Diversos\THLTX001.prw)

**Funções que utilizam __cUserId:**
- **ResumRest()**: Leitura de contrato e atualização da tabela de resumo, usando WS-Rest
  - Linha: `cUsuario := AllTrim(UsrRetMail(__cUserID))`

- **ResumRpc()**: Leitura de contrato e atualização da tabela de resumo, usando RPC
  - Linha: `cUsuario := AllTrim(UsrRetMail(__cUserID))`

### 3. FWEmergencyLib.prw (HLFisico\Emergency\FWEmergencyLib.prw)

**Funções que utilizam RPCSetEnv:**
- **User Function SenEmegAjustBase()**: (Função comentada)
  - Linha: `RPCSetEnv("00","01",,,,,,,.F.)`

### 4. HLFXFUN.PRW (HLFisico\Interface\HLFXFUN.PRW)

**Funções que utilizam RPCSetEnv:**
- **U_CLISetEnv()**: Configuração de ambiente para cliente
  - Linha: `RPCSetEnv(cCliEmp,cCliFil,,,,,,,.F.)`

**Funções que utilizam cEmpAnt:**
- Função não identificada especificamente
  - Linha: `cFile := "\SIGAADV\"+aSX[ni][1]+cEmpAnt+"0`

### 5. JHLFI002.prw (HLFisico\Jobs\JHLFI002.prw)

**Funções que utilizam RPCSetEnv:**
- **U_THLFI002()**: Rotina de ajuste
  - Linha: `RpcSetEnv("00",; // c - Código da empresa.`

### 6. Outros arquivos com ocorrências menores:

**CLIA012.PRW e CLIA019.PRW:**
- Utilizam `cEmpAnt` em conexões RPC:
  - `CREATE RPCCONN oSrv ON SERVER cRpcServer PORT nRpcPort ENVIRONMENT GetEnvServer() EMPRESA cEmpAnt FILIAL cFilAnt CLEAN`

**FWWSLSKEY.PRW, HLFVWS001.PRW, HLFWSCL02.prw:**
- Utilizam `RPCSetEnv("00","01",,,,,,,.F.)`

**THLTW001.PRW:**
- Utiliza `__cUserID`:
  - `U_xSendMail( UsrRetMail(__cUserID), "Solicitacao "+FwNoAccent(cTTSol), cBody,,.T., ,,.T.)`

## Análise de Impacto

### RPCSetEnv
- **Propósito**: Configuração de ambiente RPC para comunicação entre servidores
- **Uso Principal**: Estabelecimento de conexão com ambiente específico (empresa/filial)
- **Arquivos Críticos**: THLTXFUN.prw (múltiplas funções), JHLFI002.prw

### cEmpAnt
- **Propósito**: Identificação da empresa atual no contexto de execução
- **Uso Principal**: Composição de chaves de busca e identificação de registros
- **Arquivos Críticos**: THLTXFUN.prw (função U_PF4ChkCtr)

### __cUserId
- **Propósito**: Identificação do usuário atual para auditoria e logs
- **Uso Principal**: Obtenção de email do usuário para notificações
- **Arquivos Críticos**: THLTXFUN.prw, THLTX001.prw, THLTW001.PRW

## Recomendações

1. **Validação de Ambiente**: Sempre validar se RPCSetEnv foi executado com sucesso antes de prosseguir
2. **Tratamento de Erro**: Implementar tratamento adequado para falhas de conexão RPC
3. **Auditoria**: Manter logs das alterações de ambiente e usuário
4. **Segurança**: Validar permissões antes de alterar contexto de empresa/usuário

## Data da Análise
**Data**: $(Get-Date -Format "dd/MM/yyyy HH:mm:ss")
**Escopo**: Pasta protheus_br\Santo Graal e subpastas
**Arquivos Analisados**: Todos os arquivos .prw e .PRW