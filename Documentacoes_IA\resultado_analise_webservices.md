# Análise de WebServices - Uso de RPCSetEnv, cEmpAnt e __cUserId

## Resumo da Análise

Esta análise identificou os arquivos de webservices (que contêm WSRESTFUL e WSMETHOD) que utilizam as funções/variáveis:
- **RPCSetEnv**: Função para definir ambiente (empresa/filial)
- **cEmpAnt**: Variável que armazena a empresa atual
- **__cUserId**: Variável que armazena o ID do usuário atual

## Arquivos Identificados

### 1. Arquivos que utilizam RPCSetEnv:

- **adm_vendas\CRM\WebServices\TCRMS031.prw**
- **adm_vendas\CRM\WebServices\TCRMS050.PRW**
- **adm_vendas\CRM\WebServices\TCRMS055.prw**
- **adm_vendas\CRM\WebServices\TCRMS056.PRW**
- **adm_vendas\CRM\WebServices\TCRMS078.prw**
- **Backoffice\Interceptor\WebServices\Dimensa\TIINTERCPTDIMEN.PRW**
- **Backoffice\Interceptor\WebServices\TIWSINTERCEPTOR.PRW**
- **Incorporador\TINCWS02.prw**
- **Servicos\PSA\PSAWS.PRW**

### 2. Arquivos que utilizam cEmpAnt:

- **adm_vendas\CRM\WebServices\TCRMS003.PRW**
- **adm_vendas\CRM\WebServices\TCRMS026.PRW**
- **adm_vendas\CRM\WebServices\TCRMS038.prw**
- **adm_vendas\CRM\WebServices\TCRMS045.PRW**
- **adm_vendas\CRM\WebServices\TCRMS078.prw**
- **adm_vendas\CTT\WebService\TCTTS001.PRW**
- **Backoffice\Interceptor\WebServices\TIWSINTERCEPTOR.PRW**
- **Incorporador\TINCWS02.prw**
- **Monitor Jobs\FaturamentoBR\WSFATBRGWMS.prw**
- **Servicos\Engenharia Servicos\WsFranquia.prw**
- **Servicos\PSA\PSAWS.PRW**
- **Web Services\WRTotvsDigital.PRW**

### 3. Arquivos que utilizam __cUserId:

- **adm_vendas\CRM\CRM Servicos\WebService\SimuladorWS.prw**
- **adm_vendas\CRM\WebServices\TCRMS056.PRW**
- **adm_vendas\CRM\WebServices\TCRMS061.prw**
- **adm_vendas\CRM\WebServices\TCRMS068.PRW**
- **Servicos\PSA\PSAWS.PRW**
- **Web Services\WRTotvsDigital.PRW**

## Arquivos Consolidados (Únicos)

Os seguintes arquivos utilizam pelo menos uma das funções/variáveis pesquisadas:

1. **adm_vendas\CRM\CRM Servicos\WebService\SimuladorWS.prw** (__cUserId)
2. **adm_vendas\CRM\WebServices\TCRMS003.PRW** (cEmpAnt)
3. **adm_vendas\CRM\WebServices\TCRMS026.PRW** (cEmpAnt)
4. **adm_vendas\CRM\WebServices\TCRMS031.prw** (RPCSetEnv)
5. **adm_vendas\CRM\WebServices\TCRMS038.prw** (cEmpAnt)
6. **adm_vendas\CRM\WebServices\TCRMS045.PRW** (cEmpAnt)
7. **adm_vendas\CRM\WebServices\TCRMS050.PRW** (RPCSetEnv)
8. **adm_vendas\CRM\WebServices\TCRMS055.prw** (RPCSetEnv)
9. **adm_vendas\CRM\WebServices\TCRMS056.PRW** (RPCSetEnv, __cUserId)
10. **adm_vendas\CRM\WebServices\TCRMS061.prw** (__cUserId)
11. **adm_vendas\CRM\WebServices\TCRMS068.PRW** (__cUserId)
12. **adm_vendas\CRM\WebServices\TCRMS078.prw** (RPCSetEnv, cEmpAnt)
13. **adm_vendas\CTT\WebService\TCTTS001.PRW** (cEmpAnt)
14. **Backoffice\Interceptor\WebServices\Dimensa\TIINTERCPTDIMEN.PRW** (RPCSetEnv)
15. **Backoffice\Interceptor\WebServices\TIWSINTERCEPTOR.PRW** (RPCSetEnv, cEmpAnt)
16. **Incorporador\TINCWS02.prw** (RPCSetEnv, cEmpAnt)
17. **Monitor Jobs\FaturamentoBR\WSFATBRGWMS.prw** (cEmpAnt)
18. **Servicos\Engenharia Servicos\WsFranquia.prw** (cEmpAnt)
19. **Servicos\PSA\PSAWS.PRW** (RPCSetEnv, cEmpAnt, __cUserId)
20. **Web Services\WRTotvsDigital.PRW** (cEmpAnt, __cUserId)

## Observações

- **Total de arquivos identificados**: 20 arquivos únicos
- **Arquivo com mais ocorrências**: Servicos\PSA\PSAWS.PRW (utiliza todas as três funções/variáveis)
- **Função mais utilizada**: cEmpAnt (12 arquivos)
- **Função menos utilizada**: __cUserId (6 arquivos)

## Recomendações

1. **Revisar o uso de RPCSetEnv**: Verificar se a configuração de ambiente está sendo feita corretamente
2. **Validar alterações de cEmpAnt**: Garantir que mudanças na empresa atual não afetem outros processos
3. **Controlar __cUserId**: Verificar se as alterações no ID do usuário são necessárias e seguras
4. **Documentar dependências**: Mapear as dependências entre os webservices e as variáveis de ambiente

---
*Análise realizada em: $(Get-Date)*
*Caminho base: c:\SourceTOTVS\Workspace\protheus_br*